Connect-AzAccount -Subscription "3fc7229b-8fc5-43d3-af83-ea674a628586"

$ResourceGroupName = "rg-csp-shared-dev"
$ServerName = "sql-crefaz-dev"
$ElaName = "ela-crefaz-dev"
$ela = Get-AzSqlElasticJobAgent -ResourceGroupName $ResourceGroupName -ServerName $ServerName -Name $ElaName

# Criar credenciais no SQL 
# banco master
# CREATE LOGIN masteruser WITH PASSWORD='m!UCPX&S*4mz'
# CREATE USER masteruser FROM LOGIN masteruser
# CREATE LOGIN jobuser WITH PASSWORD='pXU#0bXO!FWA'
# Banco db-crefaz-dev
# CREATE USER jobuser FROM LOGIN jobuser
# GRANT ALTER ON SCHEMA::job TO jobuser
# GRANT execute ON SCHEMA::dbo TO jobuser


# create job credential in Job database for master user
Write-Output "Creating job credentials..."
$MasterloginPasswordSecure = (ConvertTo-SecureString -String 'm!UCPX&S*4mz' -AsPlainText -Force)
$JobloginPasswordSecure = (ConvertTo-SecureString -String 'pXU#0bXO!FWA' -AsPlainText -Force)

$masterCred = New-Object -TypeName "System.Management.Automation.PSCredential" -ArgumentList "masteruser", $MasterloginPasswordSecure 
$masterCred = $ela | New-AzSqlElasticJobCredential -Name "masteruser" -Credential $masterCred

$jobCred = New-Object -TypeName "System.Management.Automation.PSCredential" -ArgumentList "jobuser", $JobloginPasswordSecure
$jobCred = $ela | New-AzSqlElasticJobCredential -Name "jobuser" -Credential $jobCred

