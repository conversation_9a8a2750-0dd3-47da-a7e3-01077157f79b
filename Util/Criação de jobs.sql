/******* Verificando se grupo de destino para execução dos jobs **************/
select * from jobs.target_groups
select * from jobs.target_group_members

/******* Criando grupo de destino para execução dos jobs **************/
exec jobs.sp_add_target_group @target_group_name = 'sg-crefaz-dev'
exec jobs.sp_add_target_group_member @target_group_name = 'sg-crefaz-dev', @target_type = 'SqlDatabase', @database_name = 'db-crefaz-dev', @server_name = 'sql-crefaz-dev'

/******* Verificando lista atual de jobs *****************/
select * from jobs.jobsteps

/********* Criando novo job e step *************************/
exec [jobs].sp_add_job @job_name = 'Distribuição Automática de Propostas'
    ,@description = 'Executa a rotina de distribuição de proposta a cada 1 minuto'
    ,@enabled = 1
    ,@schedule_interval_type = 'Minutes'
    ,@schedule_interval_count = 1
    ,@schedule_start_time = '2022-05-13 12:00:00'


exec [jobs].sp_add_jobstep @job_name =  'Distribuição Automática de Propostas'
    ,@step_id = 1
    ,@step_name = 'Executa job.stpPropostaDistribuicaoAutomatica'
    ,@command = 'exec job.stpPropostaDistribuicaoAutomatica'
    ,@credential_name = 'jobuser'
    ,@target_group_name = 'sg-crefaz-dev'


exec [jobs].sp_add_job @job_name = 'Atualiza valor do parâmetro'
    ,@description = 'Atualiza valor do parâmetro'
    ,@enabled = 1
    ,@schedule_interval_type = 'Days'
    ,@schedule_interval_count = 1
    ,@schedule_start_time = '2022-05-12 00:01:00'


exec [jobs].sp_add_jobstep @job_name =  'Atualiza valor do parâmetro'
    ,@step_id = 1
    ,@step_name = 'Executa job.stpAtualizaValorParametro'
    ,@command = 'exec job.stpAtualizaValorParametro'
    ,@credential_name = 'jobuser'
    ,@target_group_name = 'sg-crefaz-dev'


exec [jobs].sp_add_job @job_name = 'Inativação da operatividade por inatividade'
    ,@description = 'Altera o status da operatividade do analista caso fique em inatividade'
    ,@enabled = 1
    ,@schedule_interval_type = 'Minutes'
    ,@schedule_interval_count = 1
    ,@schedule_start_time = '2022-05-12 00:01:00'


exec [jobs].sp_add_jobstep @job_name =  'Inativação da operatividade por inatividade'
    ,@step_id = 1
    ,@step_name = 'Executa job.stpOpeatividadeInatividade'
    ,@command = 'exec job.stpOpeatividadeInatividade'
    ,@credential_name = 'jobuser'
    ,@target_group_name = 'sg-crefaz-dev'


exec [jobs].sp_add_job @job_name = 'Borderô'
    ,@description = 'Executa as rotinas de borderô'
    ,@enabled = 1
    ,@schedule_interval_type = 'Hours'
    ,@schedule_interval_count = 1
    ,@schedule_start_time = '2022-05-12 00:01:00'


exec [jobs].sp_add_jobstep @job_name =  'Borderô'
    ,@step_id = 1
    ,@step_name = 'Executa job.stpExecucaoBordero'
    ,@command = 'exec job.stpExecucaoBordero'
    ,@credential_name = 'jobuser'
    ,@target_group_name = 'sg-crefaz-dev'

exec [jobs].sp_add_job @job_name = 'Integração Usuário RBM'
    ,@description = 'Executa a rotina de Integração de Usuário na RBM a cada 1 minuto'
    ,@enabled = 1
    ,@schedule_interval_type = 'Days'
    ,@schedule_interval_count = 1
    ,@schedule_start_time = '2022-05-17 00:30:00'


exec [jobs].sp_add_jobstep @job_name =  'Integração Usuário RBM'
    ,@step_id = 1
    ,@step_name = 'Executa job.stpIntegracaoUsuarioRBM'
    ,@command = 'exec job.stpIntegracaoUsuarioRBM'
    ,@credential_name = 'jobuser'
    ,@target_group_name = 'sg-crefaz-dev'

exec [jobs].sp_add_job @job_name = 'Limpar PropostaStatusHistoricoCache'
    ,@description = 'Executa a nova rotina de espurgo de dados da tabela PropostaStatusHistoricoCache para garantir que ela contenha sempre propostas que estão sendo movimentadas no dia.'
    ,@enabled = 1
    ,@schedule_interval_type = 'Days'
    ,@schedule_interval_count = 1
    ,@schedule_start_time = '2024-05-23 00:00:00'

exec [jobs].sp_add_jobstep @job_name =  'Limpar PropostaStatusHistoricoCache'
    ,@step_id = 1
    ,@step_name = 'Executa job.stpPropostaLimpaPropostaStatusHistoricoCache'
    ,@command = 'exec job.stpPropostaLimpaPropostaStatusHistoricoCache'
    ,@credential_name = 'jobuser'
    ,@target_group_name = 'sg-crefaz-prod'

exec [jobs].sp_add_job @job_name = 'Remove Proposta Cache'
    ,@description = 'Executa a nova rotina de remoção de propostas da tabela propostaCache a cada X dias (definido no parametro QUANTIDADE_DIAS_CACHE_PROPOSTAS)'
    ,@enabled = 1
    ,@schedule_interval_type = 'Days'
    ,@schedule_interval_count = 1
    ,@schedule_start_time = '2024-06-10 00:05:00'

exec [jobs].sp_add_jobstep @job_name =  'Remove Proposta Cache'
    ,@step_id = 1
    ,@step_name = 'Executa job.stpRemovePropostaCache'
    ,@command = 'exec job.stpRemovePropostaCache'
    ,@credential_name = 'jobuser'
    ,@target_group_name = 'sg-crefaz-prod'

/********** Atualizando steps dos jobs ********************/
exec [jobs].sp_update_jobstep @job_name = 'Cancelamento automático de proposta'
     ,@step_id = 1
     ,@new_name = 'Executa job.stpCancelamentoPropostasExpiradas'
     ,@command =  'exec job.stpCancelamentoPropostasExpiradas'
     ,@credential_name = 'jobuser'
     ,@target_group_name = 'sg-crefaz-prod'

exec [jobs].sp_update_jobstep @job_name = 'Atualiza valor do parâmetro'
     ,@step_id = 1
     ,@new_name = 'Executa job.stpAtualizaValorParametro'
     ,@command =  'exec job.stpAtualizaValorParametro'
     ,@credential_name = 'jobuser'
     ,@target_group_name = 'sg-crefaz-prod'
