# Nome do arquivo a ser lido
$OriFileName = "completo"
# Local do arquivo a ser lido, também será o local de destino
$local = "C:\Users\<USER>\Documents\crefaz\programaveis\"
# ExtensÃ£o do arquivo
$ext = "sql"

$pula = "0"

# Abre o arquivo de leitura
$reader = new-object System.IO.StreamReader($local+$OriFileName+"."+$ext)

# lê uma linha
while(($line = $reader.ReadLine()) -ne $null)
{	
	if($line -like "/[*][*][*][*][*][*] Object:  Index*"){
		$indice = "1"
	}
	else {
		# se a linha seguir o padrão desejado, ele ira criar um arquivo novo
		if($line -like "/[*][*][*][*][*][*] Object: *"){
			$rootName =  $line.Substring(17,$line.IndexOf(' ',17)- 17)+"\"+$line.Substring($line.IndexOf(' ',17)+2,$line.IndexOf(']')- $line.IndexOf('[')-1)+"."+$line.Substring($line.IndexOf(']')+3,$line.IndexOf(']',$line.IndexOf(']')+1)- $line.IndexOf('[',$line.IndexOf('[')+1)-1)
			$fileName = "{0}{1}.{2}" -f ($local,$rootName, $ext)
			$fileName
			$pula = "1" 
		}
		if($line -like "CREATE*"){
			$pula = "0"
		}
		# se a linha seguir o padrão desejado, ele ira criar um arquivo novo
		if($line -like "ALTER TABLE *"){
			$rootName =  "Table\"+$line.Substring(13,$line.IndexOf(']')-13)+"."+$line.Substring($line.indexof('[',14)+1,$line.IndexOf(']',$line.indexof(']',$line.indexof(']')+1))-$line.indexof('[',14)-1)
			$fileName = "{0}{1}.{2}" -f ($local,$rootName, $ext)
			$fileName
		}
		# se a linha seguir o padrão desejado, ele ira criar um arquivo novo
		if($indice -eq "1"){
			$line
			$rootName =  "Table\"+$line.Substring($line.indexof('[',$line.indexof('[')+1)+1,$line.IndexOf(']',$line.indexof(']')+1)-$line.indexof('[',$line.indexof('[')+1)-1)+"."+$line.substring($line.indexof('[',$line.indexof('[',$line.indexof('[')+1)+1)+1,$line.indexof(']',$line.indexof(']',$line.indexof(']')+1)+1)-$line.indexof('[',$line.indexof('[',$line.indexof('[')+1)+1)-1)
			$fileName = "{0}{1}.{2}" -f ($local,$rootName, $ext)
			$fileName
			$indice = "0"
		}		
		# Adiciona a linha ao ultimo arquivo criado
		if( $pula -eq "0"){
			if( $line -eq "GO" ){
				$line = ""
			}
			Add-Content -path $fileName -value $line   
		}
	}
}
# fecha o arquivo lido e finaliza o programa
$reader.Close()
