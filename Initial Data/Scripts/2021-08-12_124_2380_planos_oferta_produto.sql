declare @TabelaEnergia int
declare @CarenciaMedia int = 15
declare @Tabela as table (Valor numeric(15,2), Plano int, Prestacao numeric(15,2))

insert into @Tabela values
(200, 8, 64.72),
(200, 12, 56.04),
(200, 15, 53.74),
(200, 16, 53.38),
(250, 8, 76.61),
(250, 12, 65.60),
(250, 15, 62.45),
(250, 16, 61.88),
(300, 8, 88.49),
(300, 12, 75.17),
(300, 15, 71.16),
(300, 16, 70.39),
(350, 8, 100.37),
(350, 12, 84.73),
(350, 15, 79.87),
(350, 16, 78.90),
(400, 8, 112.26),
(400, 12, 94.30),
(400, 15, 88.58),
(400, 16, 87.41),
(400, 18, 84.32),
(450, 12, 103.86),
(450, 15, 97.30),
(450, 16, 95.92),
(450, 18, 92.48),
(500, 12, 113.42),
(500, 15, 106.00),
(500, 16, 104.43),
(500, 18, 100.63),
(600, 16, 117.15),
(600, 18, 114.07),
(700, 12, 146.81),
(700, 15, 136.95),
(700, 16, 134.68),
(700, 18, 131.17),
(800, 12, 165.99),
(800, 15, 154.79),
(800, 16, 152.22),
(800, 18, 148.22),
(900, 12, 185.17),
(900, 15, 172.62),
(900, 16, 169.75),
(900, 18, 165.27),
(1000, 15, 190.46),
(1000, 16, 187.28),
(1000, 18, 182.32)

 
set @TabelaEnergia = (select Id from TabelaJuros where Nome = 'Tabela Energia Padrão')
update TabelaJuros set CarenciaMedia = @CarenciaMedia where Id = @TabelaEnergia
delete TabelaJurosValores where TabelaJurosId = @TabelaEnergia


ALTER TABLE [dbo].[TabelaJurosValores] DROP CONSTRAINT [UQ_TabelaJurosValores_TabelaJurosId_Plano]

ALTER TABLE [dbo].[TabelaJurosValores] ADD  CONSTRAINT [UQ_TabelaJurosValores_TabelaJurosId_Plano] UNIQUE NONCLUSTERED 
(
    [TabelaJurosId] ASC,
    [Valor] ASC,
    [Plano] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ONLINE = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]

insert into TabelaJurosValores
select @TabelaEnergia, Valor, Plano, dbo.fncCalculaTaxa(Valor+(select Iof from dbo.[fncCalculaValorPrestacaoIOF](Valor, @CarenciaMedia, Plano, 1))+35, @CarenciaMedia, Plano, Prestacao)
from @Tabela