--  
insert into Integracao.DeParaRBMInfo (Nome) 
VALUES  ('Estado Civil'), --1
        ('<PERSON><PERSON><PERSON> de Instrução'), --2
        ('<PERSON><PERSON><PERSON>ta'), --3
        ('Modalidade Conta'), --4
        ('Produ<PERSON>'), --5
        ('Classificação') --6

--
INSERT into integracao.DeParaRBMDetalhe (DeParaRBMInfoId, RBMId, Descricao)
values 
(1,1,'SOLTEIRO'), --Estado Civil
(1,2,'CASADO'), --Estado Civil
(1,3,'DIVORCIADO'), --Estado Civil
(1,4,'SEPARADO'), --Estado Civil
(1,5,'VIUVO'), --Estado Civil

(2,1, 'ENSINO FUNDAMENTAL COMPLETO'), --<PERSON>ra<PERSON> de Instrução
(2,2, 'ENSINO FUNDAMENTAL INCOMPLETO'), --<PERSON><PERSON><PERSON> <PERSON> In<PERSON>rução
(2,3, '<PERSON><PERSON><PERSON><PERSON> MÉDIO COMPLETO'), --<PERSON><PERSON><PERSON> <PERSON> Instrução
(2,4, '<PERSON>NSIN<PERSON> MÉDIO INCOMPLETO'), --<PERSON><PERSON><PERSON> de Instrução
(2,5, 'MESTRADO'), --Grau de Instrução
(2,6, 'PÓS-GRADUAÇÃO'), --Grau de Instrução
(2,7, 'SUPERIOR'), --Grau de Instrução
(2,8, 'COMPELTO'), --Grau de Instrução
(2,9, 'DOUTORADO'), --Grau de Instrução

(3,1,'CORRENTE'), --Tipo Conta
(3,2,'POUPANÇA'), --Tipo Conta
(3,3,'CAIXA FÁCIL'), --Tipo Conta

(4,1,'INDIVIDUAL'), --Modalidade Conta
(4,2,'CONJUNTA'), --Modalidade Conta
(4,3,'TERCEIROS'), --Modalidade Conta

(5,1,'FATURA DE ENERGIA'), --Produto
(5,6,'LUZ EM DIA'), --Produto
(5,7,'CP BOLETO'), --Produto
(5,10,'CONSIGNADO'), --Produto
(5,11,'REFIN CP'), --Produto
(5,13,'CP CHEQUE'), --Produto
(5,14,'CDC LOJISTA'), --Produto

(6,1,'SERVIDOR PÚBLICO'), --Classificação
(6,2,'APOSENTADO'), --Classificação
(6,3,'PENSIONISTA'), --Classificação
(6,4,'AUTÔNOMO'), --Classificação
(6,5,'PROFISSIONAL LIBERAL'), --Classificação
(6,6,'EMPRESÁRIO'), --Classificação
(6,7,'EMPRESA PRIVADA'), --Classificação
(6,8,'OUTROS') --Classificação


---
INSERT into integracao.DeParaRBM (DeParaRBMDetalheId, CodeId)
values  (1,0), (2,1), (3,2), (4,2), (5,3), --Estado Civil
        (6,2), (7,1), (8,4), (9,3), (10,7), (11,6), (12,5), (14,8), --Grau de Instrução
        (15,0), (16,1), (17,2), --Tipo Conta
        (18,0), (19,1), --Modalidade Conta
        (21,6), (22,12), (23,1), (24,4),(25,10), (26,11),(27,5),  --Produto
        (28,2), (29,3), (30,4), (31,5), (32,6), (33,7), (34,1), (35,8) --Classificação

