alter table Seguranca.<PERSON><PERSON><PERSON> add <PERSON><PERSON> int;
alter table Seguranca.<PERSON><PERSON><PERSON> add Usuario<PERSON><PERSON><PERSON> varchar(30);
alter table Seguranca.Usuario add <PERSON>ha<PERSON><PERSON><PERSON> varchar(50);

create table Telefonia (
    Id tinyint identity(1,1),
    Nome varchar(50) not null
);
alter table Telefonia add constraint PK_Telefonia primary key (id);

create table HistoricoChamada (
    Id int identity(1,1),
    TelefoniaId tinyint not null,
    ChamadaId int not null,
    <PERSON><PERSON> int not null,
    UsuarioId int not null, -- Referencia Seguranca.Usuario.Id
    DataHora datetime not null,
    PropostaId int not null, -- Referencia Proposta.Id
    ChamadaDataHoraInicio datetime,
    ChamadaDataHoraFim datetime,
    NomeArquivo varchar(255), -- receberá o nome do arquivo no file server
    LinkGravacaoExterno varchar(255),
    Status tinyint,
    Confirmacao bit
);
alter table HistoricoChamada add constraint PK_HistoricoChamada primary key (Id);
alter table HistoricoChamada add constraint FK_HistoricoChamada_Telefonia foreign key (TelefoniaId) references Telefonia (Id);
alter table HistoricoChamada add constraint [FK_HistoricoChamada_Seguranca.Usuario] foreign key (UsuarioId) references Seguranca.Usuario (Id);
alter table HistoricoChamada add constraint FK_HistoricoChamada_Proposta foreign key (PropostaId) references Proposta (Id);

alter table HistoricoChamada add constraint DF_HistoricoChamada default (dateadd(hour, -3,getdate())) for DataHora;

insert into Parametro.Enum values ('statusChamada','[{"id":0, "nome":"Atendida"},{"id":1, "nome":"Cancelada"},{"id":2, "nome":"Ocupada"}]')

