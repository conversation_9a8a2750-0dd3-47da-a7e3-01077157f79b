declare @valor varchar(max)
declare @tipo tinyint -- 1 string, 2 inteiro, 3 numero e 4 data
declare @ParametroGrupo int


set @valor =
'Falha de comunicação com o Motor de Crédito.
Tente novamente mais tarde.'
set @tipo  = 1
set @ParametroGrupo = 3
insert into Parametro.Parametro values (@ParametroGrupo,@tipo,'MSG_ERRO_COMUNICACAO_CRIVO','Mensagem de falha de comunicação','Mensagem de falha de comunicação com o Motor de Crédito, que será exibida no popup de erro',1,@valor)
insert into Parametro.ParametroHistorico values (@@IDENTITY, dbo.getdateBR(),@valor)


set @valor = 'Falha de Consulta'
set @tipo  = 1
set @ParametroGrupo = 3
insert into Parametro.Parametro values (@ParametroGrupo,@tipo,'MSG_SITUACAO_FALHA_CONSULTA_CRIVO','Mensagem de falha de consulta','Mensagem de falha de consulta ao Motor de Crédito, que será exibida na tela de acompanhamento de proposta',1,@valor)
insert into Parametro.ParametroHistorico values (@@IDENTITY, dbo.getdateBR(),@valor)
