
DECLARE @parametroGrupoId INT, @parametroId INT

SELECT @parametroGrupoId = Id from Parametro.ParametroGrupo where Nome = 'Crivo'

----
INSERT into Parametro.Parametro
VALUES(@ParametroGrupoId, 1, 'V_Atraso_Mínimo', 'Atraso mínimo da Fatura', 'Atraso mínimo para incluir fatura no Luz em Dia', 1, '"Driver"."Minhas Variaveis"."---- Política - P2 - Energia ----"."V_Atraso_Mínimo"')
SET @parametroId = @@IDENTITY

insert into Parametro.ParametroHistorico
select id, dbo.getdateBR(), Valor from Parametro.ParametroHistorico where Id = @parametroId


insert into Parametro.Enum values ('elegivelLuzEmDia', '[{"id": 0, "nome":"LD", "descricao":"Luz em Dia"},{"id": 1, "nome":"SC", "descricao":"Solicita Comprovante"},{"id": 2, "nome":"DF", "descricao":"Descontar da Fatura"},{"id": 3, "nome":"LT", "descricao":"Luz Tradicional"},{"id": 4, "nome":"NE", "descricao":"Não Elegível"}]')

alter table Proposta alter column ElegivelLuzEmDia tinyint

