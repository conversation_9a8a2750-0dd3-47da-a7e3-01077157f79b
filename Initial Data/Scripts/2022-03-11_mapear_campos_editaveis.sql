

--insert into Parametro.Enum(Codigo,valor) VALUES
--('tipocanal','[{"id": 0, "nome": "CFZ"},{ "id": 1, "nome": "Corban"},{ "id": 2,"nome":"Ce<PERSON>z"}, { "id": 3, "nome": "Lojista"}]')
update a set Codigo = 'tipocanal' from Parametro.Enum a where a.<PERSON>= 'operatividadecanal'

insert into Parametro.Enum(Codigo,valor) VALUES
('tipoCalculoVencimento','[{ "id": 1, "nome": "Vencimento Carnê"},{ "id": 2,"nome":"Vencimento Energia"}, { "id": 3, "nome": "Vencimento Boleto"}]')

insert into Parametro.Enum(Codigo,valor) VALUES
('tipoTabelaJuros','[{ "id": 0, "nome": "<PERSON>ros Per<PERSON>"},{ "id": 1,"nome":"Valores Fixos"}]')

insert into Parametro.Enum(Codigo,valor) VALUES
('tipoParametro','[{ "id": 1, "nome": "String"},{ "id": 2,"nome":"Inteiro"},{ "id": 3,"nome":"Numerico aceita casas decimais"},{ "id": 4,"nome":"data"}]')

insert into Parametro.Enum(Codigo,valor) VALUES
('tipoUsuarioToken','[{ "id": 0, "nome": "Usuario App"},{ "id": 1,"nome":"Usuario Sistema"}]')

insert into Parametro.Enum(Codigo,valor) VALUES
('tipoCamposEditaveis','[{ "id": 0, "nome": "Inclusão"},{ "id": 1,"nome":"Exclusão"}]')

update a set a.Valor = '[{"id": 0,"nome": "Ambos"},{"id": 1,"nome": "Fisico"},{"id": 2,"nome": "Digital"}]' 
from Parametro.Enum a where Codigo = 'tipoModalidade'

update a set a.Valor = '[{"id": 0,"nome": "Edição"},{"id": 1,"nome": "Leitura"}]' 
from Parametro.Enum a where Codigo = 'TipoCampo'



	
