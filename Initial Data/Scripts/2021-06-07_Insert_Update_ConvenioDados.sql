insert into ConvenioDadosCategoria (Nome) values ('PN'), ('N° do Cliente')
update ConvenioDados set ConvenioDadosCategoriaId = (select id from ConvenioDadosCategoria where Nome = 'Data de Leitura') where Nome in ('Data de Leitura')
update ConvenioDados set ConvenioDadosCategoriaId = (select id from ConvenioDadosCategoria where Nome = 'Instalação') where Chave = 1
update ConvenioDados set ConvenioDadosCategoriaId = (select id from ConvenioDadosCategoria where Nome = 'Rota') where Nome in ('Rota', 'Lote', 'Etapa')
update ConvenioDados set ConvenioDadosCategoriaId = (select id from ConvenioDadosCategoria where Nome = 'PN') where Nome in ('PN')
update ConvenioDados set ConvenioDadosCategoriaId = (select id from ConvenioDadosCategoria where Nome = 'N° do Cliente') where Nome in ('N° do Cliente')
