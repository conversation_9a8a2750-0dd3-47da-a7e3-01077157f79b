

begin tran
    declare @grupoMenuId int

    insert into Seguranca.Acesso values
    ('Visualizar'),
    ('Editar'),
    ('Criar'),
    ('Deletar')

    select @grupoMenuId = id from Seguranca.ObjetoGrupo where Nome = 'Menu'

    insert into Seguranca.AcessoObjeto(AcessoId,ObjetoId)
    select b.Id AcessoId,a.id ObjetoId 
    from Seguranca.Objeto a
    cross Join Seguranca.Acesso b 
    where a.ObjetoGrupoId = @grupoMenuId
    and b.nome = 'Visualizar'

    insert into Seguranca.AcessoObjeto(AcessoId,ObjetoId)
    select b.Id AcessoId,a.id ObjetoId 
    from Seguranca.Objeto a
    cross Join Seguranca.Acesso b
    where a.ObjetoGrupoId <> @grupoMenuId
    and b.nome <> 'Deletar'

commit


