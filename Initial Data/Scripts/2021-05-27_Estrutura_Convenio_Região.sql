
ALTER TABLE [dbo].[Proposta] drop CONSTRAINT DF__Proposta__TipoMo__14B10FFA

ALTER TABLE [dbo].[Proposta] ADD CONSTRAINT DF_Proposta_TipoModalidade DEFAULT (1) FOR [TipoModalidade]

create table dbo.ConvenioRegiao (
    Id int identity(1,1) not null,
    ConvenioId int not null,
    UfId smallint not null,
	CidadeId smallint null,
	Tipo tinyint not null
)

alter table ConvenioRegiao add constraint PK_ConvenioRegiao primary key (Id);
alter table ConvenioRegiao add constraint FK_ConvenioRegiao_Convenio foreign key (ConvenioId) references Convenio(Id);
alter table ConvenioRegiao add constraint FK_ConvenioRegiao_Uf foreign key (UfId) references UF(Id);
alter table ConvenioRegiao add constraint FK_ConvenioRegiao_Cidade foreign key (CidadeId) references Cidade(Id);
alter table ConvenioRegiao add constraint DF_ConvenioRegiao_Tipo default (0) for [Tipo];


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'0 inclusão - 1 exclusão - Inclui ou Exclui a cidade da lista' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ConvenioRegiao', @level2type=N'COLUMN',@level2name=N'Tipo'
