declare @abasid int, @produtoid int

select @abasid = id from propostaAbas where nome = 'Histórico CIA'

insert into ProdutoAbas
select a.id a, b.id b
from Produto a
cross join PropostaAbas b
where b.id <> @abasid
order by 1

insert into ProdutoAbas
select a.id a, b.id b
from Produto a
cross join PropostaAbas b
where a.nome in ('Energia','Luz em Dia') and b.id = @abasid
order by 1
 
select @abasid = id from propostaAbas where nome = 'Bancários'

select @produtoid = id from produto where nome = 'CDC'

delete from ProdutoAbas where produtoid = @produtoid and PropostaAbasId = @abasid


