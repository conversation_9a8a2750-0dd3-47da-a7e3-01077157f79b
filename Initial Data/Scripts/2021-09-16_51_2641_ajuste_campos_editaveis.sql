DECLARE @dicionariodadosId INT, @camposEditaveisId INT

select @dicionariodadosId = id from DicionarioDados WITH(nolock) where Tag = 'cliente.cpfConjuge'
select @camposEditaveisId = Id from CamposEditaveis WITH(nolock) where Tipo = 0 and EtapaId is null and DicionarioDadosId = @dicionariodadosId
update CamposEditaveis set Tipo = 1 where Id = @camposEditaveisId
update CamposEditaveis set Tipo = 0 where Id <> @camposEditaveisId and DicionarioDadosId = @dicionariodadosId


select @dicionariodadosId = id from DicionarioDados WITH(nolock) where Tag = 'profissional.cnpj'
select @camposEditaveisId = Id from CamposEditaveis WITH(nolock) where Tipo = 0 and EtapaId is null and DicionarioDadosId = @dicionariodadosId
update CamposEditaveis set Tipo = 1 where Id = @camposEditaveisId
update CamposEditaveis set Tipo = 0 where Id <> @camposEditaveisId and DicionarioDadosId = @dicionariodadosId


select @dicionariodadosId = id from DicionarioDados WITH(nolock) where Tag = 'profissional.dataAdmissao'
select @camposEditaveisId = Id from CamposEditaveis WITH(nolock) where Tipo = 0 and EtapaId is null and DicionarioDadosId = @dicionariodadosId
update CamposEditaveis set Tipo = 1 where Id = @camposEditaveisId
update CamposEditaveis set Tipo = 0 where Id <> @camposEditaveisId and DicionarioDadosId = @dicionariodadosId


select @dicionariodadosId = id from DicionarioDados WITH(nolock) where Tag = 'profissional.rendaLiquida'
select @camposEditaveisId = Id from CamposEditaveis WITH(nolock) where Tipo = 0 and EtapaId is null and DicionarioDadosId = @dicionariodadosId
update CamposEditaveis set Tipo = 1 where Id = @camposEditaveisId
update CamposEditaveis set Tipo = 0 where Id <> @camposEditaveisId and DicionarioDadosId = @dicionariodadosId


select @dicionariodadosId = id from DicionarioDados WITH(nolock) where Tag = 'historico.data'
select @camposEditaveisId = Id from CamposEditaveis WITH(nolock) where Tipo = 0 and EtapaId is null and DicionarioDadosId = @dicionariodadosId
update CamposEditaveis set Tipo = 1 where Id = @camposEditaveisId
update CamposEditaveis set Tipo = 0 where Id <> @camposEditaveisId and DicionarioDadosId = @dicionariodadosId


select @dicionariodadosId = id from DicionarioDados WITH(nolock) where Tag = 'historico.hora'
select @camposEditaveisId = Id from CamposEditaveis WITH(nolock) where Tipo = 0 and EtapaId is null and DicionarioDadosId = @dicionariodadosId
update CamposEditaveis set Tipo = 1 where Id = @camposEditaveisId
update CamposEditaveis set Tipo = 0 where Id <> @camposEditaveisId and DicionarioDadosId = @dicionariodadosId


select @dicionariodadosId = id from DicionarioDados WITH(nolock) where Tag = 'historico.usuario'
select @camposEditaveisId = Id from CamposEditaveis WITH(nolock) where Tipo = 0 and EtapaId is null and DicionarioDadosId = @dicionariodadosId
update CamposEditaveis set Tipo = 1 where Id = @camposEditaveisId
update CamposEditaveis set Tipo = 0 where Id <> @camposEditaveisId and DicionarioDadosId = @dicionariodadosId


select @dicionariodadosId = id from DicionarioDados WITH(nolock) where Tag = 'historico.departamento'
select @camposEditaveisId = Id from CamposEditaveis WITH(nolock) where Tipo = 0 and EtapaId is null and DicionarioDadosId = @dicionariodadosId
update CamposEditaveis set Tipo = 1 where Id = @camposEditaveisId
update CamposEditaveis set Tipo = 0 where Id <> @camposEditaveisId and DicionarioDadosId = @dicionariodadosId


select @dicionariodadosId = id from DicionarioDados WITH(nolock) where Tag = 'historico.mensagem'
select @camposEditaveisId = Id from CamposEditaveis WITH(nolock) where Tipo = 0 and EtapaId is null and DicionarioDadosId = @dicionariodadosId
update CamposEditaveis set Tipo = 1 where Id = @camposEditaveisId
update CamposEditaveis set Tipo = 0 where Id <> @camposEditaveisId and DicionarioDadosId = @dicionariodadosId


select @dicionariodadosId = id from DicionarioDados WITH(nolock) where Tag = 'operacao.convenioNome'
select @camposEditaveisId = Id from CamposEditaveis WITH(nolock) where Tipo = 0 and EtapaId is null and DicionarioDadosId = @dicionariodadosId
update CamposEditaveis set Tipo = 1 where Id = @camposEditaveisId
update CamposEditaveis set Tipo = 0 where Id <> @camposEditaveisId and DicionarioDadosId = @dicionariodadosId


select @dicionariodadosId = id from DicionarioDados WITH(nolock) where Tag = 'endereco.ufId'
select @camposEditaveisId = Id from CamposEditaveis WITH(nolock) where Tipo = 0 and EtapaId is null and DicionarioDadosId = @dicionariodadosId
update CamposEditaveis set Tipo = 1 where Id = @camposEditaveisId
update CamposEditaveis set Tipo = 0 where Id <> @camposEditaveisId and DicionarioDadosId = @dicionariodadosId

--------

insert into DicionarioDados
VALUES ('Remover referências', 'contatos.referencia.remover', 1, null)
set @dicionariodadosId = @@IDENTITY

insert into CamposEditaveis
select @dicionariodadosId as dicionarioDadosId, EtapaId, MotivoId, Tipo, Checagem, Tela
from CamposEditaveis a WITH(nolock)
join DicionarioDados b WITH(nolock) on b.Id = a.DicionarioDadosId 
where Tag = 'contatos.referencia.nome'


UPDATE DicionarioDados
set Tag = 'contatos.referencia.adicionar'
where Descricao = 'Adicionar referencias'
