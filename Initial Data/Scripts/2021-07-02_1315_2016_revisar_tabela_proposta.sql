declare @erro varchar (1000)

BEGIN TRAN
    BEGIN TRY

    EXEC sp_rename 'dbo.Proposta.TempoEmpregoAtual', 'TempoEmprego', 'COLUMN';

    EXEC sp_rename 'dbo.Proposta.ProdutoOriginal', 'ProdutoOriginalId', 'COLUMN';

    EXEC sp_rename 'dbo.Proposta.GrauInstrucao', 'GrauInstrucaoId', 'COLUMN';

    EXEC sp_rename 'dbo.Proposta.Digito', 'AgenciaDigito', 'COLUMN';

    EXEC sp_rename 'dbo.Proposta.Numero', 'ContaNumero', 'COLUMN';

    EXEC sp_rename 'dbo.Proposta.ModoMotorCredito', 'ModoMotorCreditoContingencia', 'COLUMN';

    ALTER TABLE dbo.Proposta ALTER COLUMN ModoMotorCreditoContingencia BIT;

    ALTER TABLE dbo.Proposta ADD CONSTRAINT FK_Proposta_GrauInstrucao FOREIGN KEY (GrauInstrucaoId)
    REFERENCES dbo.GrauInstrucao(Id)

    ALTER TABLE dbo.Proposta ADD CONSTRAINT FK_Proposta_Profissao FOREIGN KEY (ProfissaoId)
    REFERENCES dbo.Profissao(Id)

    ALTER TABLE dbo.Proposta ADD CONSTRAINT FK_Proposta_Unidade FOREIGN KEY (UnidadeId)
    REFERENCES dbo.Unidade(Id)
    
    ALTER TABLE dbo.Proposta ADD CONSTRAINT FK_Proposta_Produto_Original FOREIGN KEY (ProdutoOriginalId)
    REFERENCES dbo.Produto(Id)

    ALTER TABLE dbo.Proposta ADD CONSTRAINT FK_Proposta_Pais_Nacionalidade FOREIGN KEY (NacionalidadeId)
    REFERENCES dbo.Pais(Id)

    COMMIT
    --ROLLBACK
    END TRY
    BEGIN CATCH
        
         set @erro = ERROR_MESSAGE()
            rollback
            RAISERROR(@erro,16,1)
            return
    END CATCH
    