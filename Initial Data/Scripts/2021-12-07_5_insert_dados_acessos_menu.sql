
begin tran
    declare @objetogrupoId int,@usuarioId int,@perfilComercial int,@controlerId int,@perfilVendas int

    insert into api.controller values('crefazapisqlstag','Seguranca','Controle de acessos dos Usuarios')

    set @controlerId = @@IDENTITY

    insert into api.route(controller_id,[name],[description],[route],[method],[procedure],[input],[output],[response_type_id])
    select 
        @controlerId,'Seguranca','Listar os acessos dos menus que o usuario logado tem permissão.','/api/seguranca','GET','controller.stpSeguranca',[input],[output],response_type_id
    from api.route where [route] = '/api/proposta' and input = '{}'

    --select * from api.route where [route] = '/api/seguranca'

    select @objetogrupoId = id from Seguranca.ObjetoGrupo where nome = 'Menu'

    if not exists(select * from Seguranca.Perfil where Nome = 'Vendas')
        insert into Seguranca.Perfil values ('Vendas')

    if DB_NAME() <> 'db-crefaz-prod'
    begin

        insert into Seguranca.Usuario
        select PessoaId,'teste.comercial',senha,ramal,CorrespondenteId,UnidadeId,nome,CPFCNPJ,Ativo,Bloqueado from Seguranca.Usuario where login = 'edson.alves'

        set @usuarioId = @@IDENTITY
        
        insert into Seguranca.PerfilUsuario
        select a.id,@usuarioId from seguranca.perfil a where a.Nome = 'Comercial'

        insert into Seguranca.Usuario
        select PessoaId,'teste.vendas',senha,ramal,CorrespondenteId,UnidadeId,nome,CPFCNPJ,Ativo,Bloqueado from Seguranca.Usuario where login = 'edson.alves'

        set @usuarioId = @@IDENTITY 

        insert into Seguranca.PerfilUsuario
        select a.id,@usuarioId from seguranca.perfil a where a.Nome = 'Vendas'
    end

    select @perfilComercial = id from Seguranca.Perfil where Nome = 'Comercial'

    ---menus
    insert into Seguranca.PerfilConcessao
    select @perfilComercial,null,a.id
    from Seguranca.acessoobjeto a with(nolock)
    join Seguranca.acesso b with(nolock) on b.id = a.acessoId
    join Seguranca.objeto c with(nolock) on c.id = a.objetoId
    join Seguranca.objetogrupo d with(nolock) on d.id = c.ObjetoGrupoId
    where d.nome = 'Menu' 
    and b.Nome = 'Visualizar'
    and c.Nome in ('Simulação','Nova Proposta','Acompanhamento','Impressão de Contratos')

    ---criar
    insert into Seguranca.PerfilConcessao
    select @perfilComercial,null,a.id
    from Seguranca.acessoobjeto a
    join Seguranca.acesso b on b.id = a.acessoId
    join Seguranca.objeto c on c.id = a.objetoId
    join Seguranca.objetogrupo d on d.id = c.ObjetoGrupoId
    where b.Nome = 'Visualizar'
    and c.tag in (
        'tela.credito.proposta.simulacao',
        'botao.simulacao.simular',
        'botao.simulacao.preanalise',
        'tela.credito.proposta.novaProposta',
        'tela.propostacredito',
        'botao.propostaCredito.cancelarProposta',
        'botao.propostaCredito.centralComunicacao',
        'tela.credito.proposta.acompanhamento',
        'botao.acompanhamento.simulacaoProposta',
        'botao.acompanhamento.cadastrarProposta',
        'botao.acompanhamento.imprimirContrato',
        'botao.acompanhamento.selecaoModalidade',
        'botao.acompanhamento.impressaoDocumentos',
        'botao.acompanhamento.situacaoProposta',
        'botao.acompanhamento.reenviarProposta',
        'tela.credito.proposta.impressaoContrato'
    )
  
    select @perfilVendas = id from Seguranca.Perfil where Nome = 'Vendas'

    ---menus
    insert into Seguranca.PerfilConcessao
    select @perfilVendas,null,a.id
    from Seguranca.acessoobjeto a with(nolock)
    join Seguranca.acesso b with(nolock) on b.id = a.acessoId
    join Seguranca.objeto c with(nolock) on c.id = a.objetoId
    join Seguranca.objetogrupo d with(nolock) on d.id = c.ObjetoGrupoId
    where d.nome = 'Menu' 
    and b.Nome = 'Visualizar'
    and c.Nome in ('Simulação','Nova Proposta','Acompanhamento','Impressão de Contratos')

    ---visualizar
    insert into Seguranca.PerfilConcessao--
    select @perfilVendas,null,a.id
    from Seguranca.acessoobjeto a
    join Seguranca.acesso b on b.id = a.acessoId
    join Seguranca.objeto c on c.id = a.objetoId
    join Seguranca.objetogrupo d on d.id = c.ObjetoGrupoId
    where b.Nome = 'Visualizar'
    and c.tag in (
        'tela.credito.proposta.simulacao',
        'botao.simulacao.simular',
        'botao.simulacao.preanalise',
        'tela.credito.proposta.novaProposta',
        'tela.propostacredito',----------
        'botao.propostaCredito.cancelarProposta',
        'botao.propostaCredito.centralComunicacao',
        'tela.credito.proposta.acompanhamento',
        'botao.acompanhamento.simulacaoProposta',
        'botao.acompanhamento.cadastrarProposta',
        'botao.acompanhamento.imprimirContrato',
        'botao.acompanhamento.selecaoModalidade',
        'botao.acompanhamento.impressaoDocumentos',
        'tela.credito.proposta.impressaoContrato'
    )

    ---criar
    insert into Seguranca.PerfilConcessao--
    select @perfilVendas,null,a.id
    from Seguranca.acessoobjeto a
    join Seguranca.acesso b on b.id = a.acessoId
    join Seguranca.objeto c on c.id = a.objetoId
    join Seguranca.objetogrupo d on d.id = c.ObjetoGrupoId
    where b.Nome = 'Criar'
    and c.tag in (
        'tela.credito.proposta.novaProposta',
        'botao.propostaCredito.centralComunicacao'
    )

    ---editar
    insert into Seguranca.PerfilConcessao--
    select @perfilVendas,null,a.id
    from Seguranca.acessoobjeto a
    join Seguranca.acesso b on b.id = a.acessoId
    join Seguranca.objeto c on c.id = a.objetoId
    join Seguranca.objetogrupo d on d.id = c.ObjetoGrupoId
    where b.Nome = 'Editar'
    and c.tag in (
        'tela.propostacredito',
        'botao.acompanhamento.selecaoModalidade',
        'botao.propostaCredito.cancelarProposta'
    )

commit
