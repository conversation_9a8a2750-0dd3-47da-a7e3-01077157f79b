declare @grupoId int 
select @grupoId = id from Parametro.ParametroGrupo where Nome = 'RBM'

declare @valor varchar(max)
declare @parametroId int 


set @valor = cast((select Id from PropostaEtapa with(nolock) where Nome = 'Aguard. Validação') as varchar)
insert into Parametro.Parametro values (@grupoId, 2, 'RBM_ETAPA_EXPORTACAO', 'Cod Etapa exportação proposta', 'Código da etapa no qual ocorre a exportação da proposta para o sistema RBM',1, @valor)
set @parametroId = @@IDENTITY
insert into Parametro.ParametroHistorico values (@parametroId, dbo.getdateBR(), @valor)

set @valor = cast((select Id from PropostaEtapa with(nolock) where Nome = 'Fila Pagamento') as varchar)
insert into Parametro.Parametro values (@grupoId, 2, 'RBM_ETAPA_FORMALIZACAO', 'Cod Etapa formalizacao proposta', 'Código da etapa no qual ocorre a confirmação da formalização da proposta no sistema RBM',1, @valor)
set @parametroId = @@IDENTITY
insert into Parametro.ParametroHistorico values (@parametroId, dbo.getdateBR(), @valor)
