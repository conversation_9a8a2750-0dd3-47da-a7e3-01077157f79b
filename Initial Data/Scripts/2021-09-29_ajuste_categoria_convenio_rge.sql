update Convenio set nome ='RGE' where nome = 'RGE - RS'

DECLARE @convenioId INT, @convenioDadosCategoriaId int

select @convenioId = Id from Convenio where Nome = 'RGE'


select @convenioDadosCategoriaId = Id from ConvenioDadosCategoria where Nome = 'Rota'

update ConvenioDados
set ConvenioDadosCategoriaId = @convenioDadosCategoriaId
where Nome = 'Lote' and ConvenioId = @convenioId

select @convenioDadosCategoriaId = Id from ConvenioDadosCategoria where Nome = 'Instalação'

update ConvenioDados
set ConvenioDadosCategoriaId = @convenioDadosCategoriaId
where Nome = 'Seu Código' and ConvenioId = @convenioId