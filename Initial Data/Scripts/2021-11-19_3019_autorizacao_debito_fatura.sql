declare @errado varchar(255),
        @novo varchar(255)

set @errado = 'R$'
set @novo =   ''
update a 
set layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica],
        Tipo */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'
and  TipoDocumento = 'autorizacao-debito-fatura-energia'

set @errado = '()'
set @novo =   '{{PLANO}} ({{PLANOEXTENSO}})'
update a 
set layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica],
        Tipo */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'
and  TipoDocumento = 'autorizacao-debito-fatura-energia'

set @errado = 'titular, o valor de  em'
set @novo =   'titular, o valor de {{VALOR}} em'
update a 
set layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica],
        Tipo */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'
and  TipoDocumento = 'autorizacao-debito-fatura-energia'

set @errado = 'XXXXXXXXX'
set @novo =   'crédito pessoal'
update a 
set layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica],
        Tipo */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'
and  TipoDocumento = 'autorizacao-debito-fatura-energia'

