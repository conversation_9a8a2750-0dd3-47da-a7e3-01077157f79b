insert into ProdutoRegiao
SELECT b.Id ProdutoId, a.<PERSON>iao<PERSON>d, a.Id UfId, null CidadeId, 1 Tipo
from UF a
CROSS APPLY Produto b
where UF not in ('AC','AL','AM','AP','BA','CE','MA','PA','PB','PE','PI','RN','RO','RR','SE','TO')
and b.Nome = 'Boleto'

insert into ProdutoRegiao
SELECT b.Id ProdutoId, a.RegiaoId, a.Id UfId, null CidadeId, 1 Tipo
from UF a
CROSS APPLY Produto b
where UF in ('SP','SP','SP','SP','GO','RJ','CE','RS','SC','BA','RN','PE','SP','ES')
and b.Nome = 'Energia'


--- demais produtos liberados para todas as regiões
insert into ProdutoRegiao
SELECT a.Id ProdutoId, b.id RegiaoId, null Ufid, null CidadeId, 1 tipo
from Produto a
CROSS APPLY Regiao b
where a.Nome not in ('<PERSON><PERSON>o','Energia')