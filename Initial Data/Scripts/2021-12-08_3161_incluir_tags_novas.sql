

begin tran
    declare @grupomenuId int,
            @grupofiltroId int,
            @grupobotaoId int,
            @grupoabaId int,
            @grupoframeId int,
            @grupotelaId int,
            @grupotabelaId int


    select @grupomenuId = id from Seguranca.objetogrupo where nome = 'menu'

    select @grupofiltroId = id from Seguranca.objetogrupo where nome = 'filtro'

    select @grupobotaoId = id from Seguranca.objetogrupo where nome = 'botao'

    select @grupoabaId = id from Seguranca.objetogrupo where nome = 'aba'

    select @grupoframeId = id from Seguranca.objetogrupo where nome = 'frame'

    select @grupotelaId = id from Seguranca.objetogrupo where nome = 'tela'

    select @grupotabelaId = id from Seguranca.objetogrupo where nome = 'tabela'

    insert into seguranca.objeto(nome,tag,ObjetoGrupoId,Ordem) values
    ('Parâmetro','tela.configuracoes.parametro',@grupotelaId,1)

    insert into Seguranca.AcessoObjeto(AcessoId,ObjetoId)
    select b.Id AcessoId,a.id ObjetoId 
    from Seguranca.Objeto a
    cross Join Seguranca.Acesso b
    where a.ordem = 1
    and b.nome <> 'Deletar'

    update a set Ordem = 0 from Seguranca.Objeto a where Ordem = 1


commit