BEGIN

    --
    -- Id do controller
    -- N<PERSON> necessário definir valor
    --
    declare @controllerId int

    --
    -- Variáveis de inserção
    -- Defina os valores do controlador caso seja inserção
    -- Defina os valores da rota para inserção de nova rota
    --
    declare @controllerName varchar(50) = 'Propostas',
            @controllerUser varchar(50) = 'crefazapisqlstag',
            @controllerDesc varchar(MAX) = 'Acessa a API de Propostas',
            @routeName varchar(255) = 'Relatório de Visão Analítica',
            @routeDesc varchar(MAX) = 'Retorna os dados de propostas para relatório analítico com filtrado por produtos',
            @route varchar(100) = '/api/proposta/visao-analitica',
            @routeMethod varchar(10) = 'POST',
            @routeProcedure varchar(255) = 'controller.stpProposta',
            @routeInput varchar(MAX) = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "pagina": 1,
            "quantidadePorPagina": 15,
            "ordenacao": "",
            "ordenacaoAsc": false,
            "produtoId": 1,
            "propostaId": null,
            "origemId": null,
            "analistas": []
        }
    ],
    "required": [
        "pagina",
        "quantidadePorPagina",
        "ordenacao",
        "ordenacaoAsc",
        "produtoId"
    ],
    "properties": {
        "pagina": {
            "$id": "#/properties/pagina",
            "type": "integer",
            "title": "The pagina schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "quantidadePorPagina": {
            "$id": "#/properties/quantidadePorPagina",
            "type": "integer",
            "title": "The quantidadePorPagina schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                15
            ]
        },
        "ordenacao": {
            "$id": "#/properties/ordenacao",
            "type": "string",
            "title": "The ordenacao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                ""
            ]
        },
        "ordenacaoAsc": {
            "$id": "#/properties/ordenacaoAsc",
            "type": "boolean",
            "title": "The ordenacaoAsc schema",
            "description": "An explanation about the purpose of this instance.",
            "default": false,
            "examples": [
                false
            ]
        },
        "produtoId": {
            "$id": "#/properties/produtoId",
            "type": "integer",
            "title": "The produtoId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "propostaId": {
            "$id": "#/properties/propostaId",
            "type": "integer",
            "title": "The propostaId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": null,
            "examples": [
                null
            ]
        },
        "origemId": {
            "$id": "#/properties/origemId",
            "type": "integer",
            "title": "The origemId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": null,
            "examples": [
                null
            ]
        },
        "analistas": {
            "$id": "#/properties/analistas",
            "type": "array",
            "title": "The analistas schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                []
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/analistas/items"
            }
        }
    },
    "additionalProperties": true
}',
            @routeOutput varchar(MAX) = '{}',
            @routeResponseId int = 2

    select @controllerId = Id from api.controller where [name] = @controllerName

    if (@controllerId IS NULL)
    BEGIN
        insert into api.controller
        (
            [user],
            [name],
            [description]
        )
        VALUES
        (
            @controllerUser,
            @controllerName,
            @controllerDesc
        )

        set @controllerId = @@IDENTITY
    END

    insert into api.route
    (
        [controller_id],
        [name],
        [description],
        [route],
        [method],
        [procedure],
        [input],
        [output],
        [response_type_id]
    ) values (
        @controllerId,
        @routeName,
        @routeDesc,
        @route,
        @routeMethod,
        @routeProcedure,
        @routeInput,
        @routeOutput,
        @routeResponseId
    )

END
-- mudança para criar um novo commit
