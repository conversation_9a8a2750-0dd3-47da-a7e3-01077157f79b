
--#Criar Controller
BEGIN

    --
    -- Id do controller
    -- N<PERSON> necessário definir valor
    --
    declare @controllerId int 

    --
    -- Variáveis de inserção
    -- Defina os valores do controlador caso seja inserção
    -- Defina os valores da rota para inserção de nova rota
    --
    declare @controllerName varchar(50) = 'Comissao',
            @controllerUser varchar(50) = 'crefazapisqlstag',
            @controllerDesc varchar(MAX) = 'Atualizar comissão',
            @routeName varchar(255) = 'Atualizar comissão',
            @routeDesc varchar(MAX) = 'Atualiza uma comissão especifica',
            @route varchar(100) = '/api/comissao',
            @routeMethod varchar(10) = 'PUT',
            @routeProcedure varchar(255) = 'controller.stpComissao',
            @routeInput varchar(MAX) = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "array",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": [],
    "examples": [
        [
            {
                "comissaoId": 1,
                "operacaoRMB": 562,
                "propostaId": 25,
                "valorComisssao": 500.41,
                "dataHoraPagamento": null,
                "cpfcnpj": "00000000001",
                "cliente": "Fulano",
                "tipoLancamento": 0,
                "valorOperacao": 5400,
                "valorLiquido": 4500,
                "prazo": 1,
                "produto": "Boleto",
                "uf": "ES",
                "percentualComissao": 5,
                "empresa": "Crefaz",
                "dataHoraComissao": "2021-06-25T18:20:21.759Z",
                "matriz": "Crefaz"
            },
            {
                "comissaoId": 2,
                "operacaoRMB": 562,
                "propostaId": 25,
                "valorComisssao": 500.41,
                "dataHoraPagamento": null,
                "cpfcnpj": "00000000001",
                "cliente": "Fulano",
                "tipoLancamento": 0,
                "valorOperacao": 5400,
                "valorLiquido": 4500,
                "prazo": 2,
                "produto": "Boleto",
                "uf": "ES",
                "percentualComissao": 5,
                "empresa": "Crefaz",
                "dataHoraComissao": "2021-06-25T18:20:21.759Z",
                "matriz": "Crefaz"
            }
        ]
    ],
    "additionalItems": true,
    "items": {
        "$id": "#/items",
        "anyOf": [
            {
                "$id": "#/items/anyOf/0",
                "type": "object",
                "title": "The first anyOf schema",
                "description": "An explanation about the purpose of this instance.",
                "default": {},
                "examples": [
                    {
                        "comissaoId": 1,
                        "operacaoRMB": 562,
                        "propostaId": 25,
                        "valorComisssao": 500.41,
                        "dataHoraPagamento": null,
                        "cpfcnpj": "00000000001",
                        "cliente": "Fulano",
                        "tipoLancamento": 0,
                        "valorOperacao": 5400,
                        "valorLiquido": 4500,
                        "prazo": 1,
                        "produto": "Boleto",
                        "uf": "ES",
                        "percentualComissao": 5,
                        "empresa": "Crefaz",
                        "dataHoraComissao": "2021-06-25T18:20:21.759Z",
                        "matriz": "Crefaz"
                    }
                ],
                "required": [
                    "comissaoId",
                    "operacaoRMB",
                    "propostaId",
                    "valorComisssao",
                    "dataHoraPagamento",
                    "cpfcnpj",
                    "cliente",
                    "tipoLancamento",
                    "valorOperacao",
                    "valorLiquido",
                    "prazo",
                    "produto",
                    "uf",
                    "percentualComissao",
                    "empresa",
                    "dataHoraComissao",
                    "matriz"
                ],
                "properties": {
                    "comissaoId": {
                        "$id": "#/items/anyOf/0/properties/comissaoId",
                        "type": "integer",
                        "title": "The comissaoId schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            1
                        ]
                    },
                    "operacaoRMB": {
                        "$id": "#/items/anyOf/0/properties/operacaoRMB",
                        "type": "integer",
                        "title": "The operacaoRMB schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            562
                        ]
                    },
                    "propostaId": {
                        "$id": "#/items/anyOf/0/properties/propostaId",
                        "type": "integer",
                        "title": "The propostaId schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            25
                        ]
                    },
                    "valorComisssao": {
                        "$id": "#/items/anyOf/0/properties/valorComisssao",
                        "type": "number",
                        "title": "The valorComisssao schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0.0,
                        "examples": [
                            500.41
                        ]
                    },
                    "dataHoraPagamento": {
                        "$id": "#/items/anyOf/0/properties/dataHoraPagamento",
                        "type": "null",
                        "title": "The dataHoraPagamento schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": null,
                        "examples": [
                            null
                        ]
                    },
                    "cpfcnpj": {
                        "$id": "#/items/anyOf/0/properties/cpfcnpj",
                        "type": "string",
                        "title": "The cpfcnpj schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": "",
                        "examples": [
                            "00000000001"
                        ]
                    },
                    "cliente": {
                        "$id": "#/items/anyOf/0/properties/cliente",
                        "type": "string",
                        "title": "The cliente schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": "",
                        "examples": [
                            "Fulano"
                        ]
                    },
                    "tipoLancamento": {
                        "$id": "#/items/anyOf/0/properties/tipoLancamento",
                        "type": "integer",
                        "title": "The tipoLancamento schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            0
                        ]
                    },
                    "valorOperacao": {
                        "$id": "#/items/anyOf/0/properties/valorOperacao",
                        "type": "integer",
                        "title": "The valorOperacao schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            5400
                        ]
                    },
                    "valorLiquido": {
                        "$id": "#/items/anyOf/0/properties/valorLiquido",
                        "type": "integer",
                        "title": "The valorLiquido schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            4500
                        ]
                    },
                    "prazo": {
                        "$id": "#/items/anyOf/0/properties/prazo",
                        "type": "integer",
                        "title": "The prazo schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            1
                        ]
                    },
                    "produto": {
                        "$id": "#/items/anyOf/0/properties/produto",
                        "type": "string",
                        "title": "The produto schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": "",
                        "examples": [
                            "Boleto"
                        ]
                    },
                    "uf": {
                        "$id": "#/items/anyOf/0/properties/uf",
                        "type": "string",
                        "title": "The uf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": "",
                        "examples": [
                            "ES"
                        ]
                    },
                    "percentualComissao": {
                        "$id": "#/items/anyOf/0/properties/percentualComissao",
                        "type": "integer",
                        "title": "The percentualComissao schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            5
                        ]
                    },
                    "empresa": {
                        "$id": "#/items/anyOf/0/properties/empresa",
                        "type": "string",
                        "title": "The empresa schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": "",
                        "examples": [
                            "Crefaz"
                        ]
                    },
                    "dataHoraComissao": {
                        "$id": "#/items/anyOf/0/properties/dataHoraComissao",
                        "type": "string",
                        "title": "The dataHoraComissao schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": "",
                        "examples": [
                            "2021-06-25T18:20:21.759Z"
                        ]
                    },
                    "matriz": {
                        "$id": "#/items/anyOf/0/properties/matriz",
                        "type": "string",
                        "title": "The matriz schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": "",
                        "examples": [
                            "Crefaz"
                        ]
                    }
                },
                "additionalProperties": true
            }
        ]
    }
}',
            @routeOutput varchar(MAX) = '{}',
            @routeResponseId int = 2

    select @controllerId = Id from api.controller where [name] = @controllerName

    if (@controllerId IS NULL)
    BEGIN
        insert into api.controller
        (
            [user],
            [name],
            [description]
        )
        VALUES
        (
            @controllerUser,
            @controllerName,
            @controllerDesc
        )

        set @controllerId = @@IDENTITY
    END

    insert into api.route
    (
        [controller_id],
        [name],
        [description],
        [route],
        [method],
        [procedure],
        [input],
        [output],
        [response_type_id]
    ) values (
        @controllerId,
        @routeName,
        @routeDesc,
        @route,
        @routeMethod,
        @routeProcedure,
        @routeInput,
        @routeOutput,
        @routeResponseId
    )

END