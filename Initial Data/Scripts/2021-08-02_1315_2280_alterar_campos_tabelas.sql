

begin tran 

    alter table dbo.renda
    alter COLUMN TelefoneRH varchar(11);

    alter table dbo.PessoaFisica 
    alter COLUMN Documento varchar(15);

    alter table dbo.PessoaFisica 
    alter COLUMN Email varchar(100);

    alter table dbo.PessoaFisica
    drop CONSTRAINT FK_PessoaFisica_NaturalidadeUf;

    alter table dbo.PessoaFisica 
    drop COLUMN NaturalidadeUfId;

    alter table dbo.Proposta
    drop COLUMN Leitura;
    
    alter table dbo.Proposta
    drop COLUMN ContaContrato;

    alter table dbo.Proposta
    alter COLUMN Complemento varchar(50);

    alter table dbo.Endereco
    alter column Complemento varchar(50);

COMMIT

/*
alterar campos 
    Renda.TelefoneRH mudar para varchar(11)
    PessoaFisica.Documento para varchar(15)
    Remover PessoaFisica.NaturalidadeUfId (Avaliar impacto nos outros obejtos e realizar ajustes para a alteração ser possível)
    PessoaFisica.Email mudar para varchar(100)
    Remover de Proposta os campos Leitura e ContaContrato
*/


