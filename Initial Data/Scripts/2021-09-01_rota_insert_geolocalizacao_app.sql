BEGIN

    --
    -- Id do controller
    -- N<PERSON> necessário definir valor
    --
    declare @controllerId int 

    --
    -- Variáveis de inserção
    -- Defina os valores do controlador caso seja inserção
    -- Defina os valores da rota para inserção de nova rota
    --
    declare @controllerName varchar(50) = 'Propostas',
            @controllerUser varchar(50) = 'crefazapisqlstag',
            @controllerDesc varchar(MAX) = 'Acesso as propriedades das propostas',
            @routeName varchar(255) = 'Salvar geolocalização no app',
            @routeDesc varchar(MAX) = 'Armazena a geolocalização no app',
            @route varchar(100) = '/api/proposta/salvar-geolocalizacao-app',
            @routeMethod varchar(10) = 'POST',
            @routeProcedure varchar(255) = 'controller.stpProposta',
            @routeInput varchar(MAX) = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "propostaId": 2,
            "latitude": "38.8951",
            "longitude": "-77.0388"
        }
    ],
    "required": [
        "propostaId",
        "latitude",
        "longitude"
    ],
    "properties": {
        "propostaId": {
            "$id": "#/properties/propostaId",
            "type": "integer",
            "title": "The propostaId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                2
            ]
        },
        "latitude": {
            "$id": "#/properties/latitude",
            "type": "string",
            "title": "The latitude schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "38.8951"
            ]
        },
        "longitude": {
            "$id": "#/properties/longitude",
            "type": "string",
            "title": "The longitude schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "-77.0388"
            ]
        }
    },
    "additionalProperties": true
}',
            @routeOutput varchar(MAX) = '{}',
            @routeResponseId int = 2

    select @controllerId = Id from api.controller where [name] = @controllerName

    if (@controllerId IS NULL)
    BEGIN
        insert into api.controller
        (
            [user],
            [name],
            [description]
        )
        VALUES
        (
            @controllerUser,
            @controllerName,
            @controllerDesc
        )

        set @controllerId = @@IDENTITY
    END

    insert into api.route
    (
        [controller_id],
        [name],
        [description],
        [route],
        [method],
        [procedure],
        [input],
        [output],
        [response_type_id]
    ) values (
        @controllerId,
        @routeName,
        @routeDesc,
        @route,
        @routeMethod,
        @routeProcedure,
        @routeInput,
        @routeOutput,
        @routeResponseId
    )

END
-- mudança para criar um novo commit