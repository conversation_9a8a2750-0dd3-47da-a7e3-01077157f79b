UPDATE api.route
set [input] = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "id": 872,
            "adicionais": [
                {
                    "convenioDadosId": 12,
                    "convenioId": 1,
                    "nome": "N° da Instalação",
                    "valor": "432342342"
                },
                {
                    "convenioDadosId": 23,
                    "convenioId": 1,
                    "nome": "Data de Leitura",
                    "valor": "2021-06-03T13:23:22.240Z"
                }
            ]
        }
    ],
    "required": [
        "id",
        "adicionais"
    ],
    "properties": {
        "id": {
            "$id": "#/properties/id",
            "type": "integer",
            "title": "The id schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                872
            ]
        },
        "adicionais": {
            "$id": "#/properties/adicionais",
            "type": "array",
            "title": "The adicionais schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [
                    {
                        "convenioDadosId": 12,
                        "convenioId": 1,
                        "nome": "N° da Instalação",
                        "valor": "432342342"
                    },
                    {
                        "convenioDadosId": 23,
                        "convenioId": 1,
                        "nome": "Data de Leitura",
                        "valor": "2021-06-03T13:23:22.240Z"
                    }
                ]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/adicionais/items",
                "anyOf": [
                    {
                        "$id": "#/properties/adicionais/items/anyOf/0",
                        "type": "object",
                        "title": "The first anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": {},
                        "examples": [
                            {
                                "convenioDadosId": 12,
                                "convenioId": 1,
                                "nome": "N° da Instalação",
                                "valor": "432342342"
                            }
                        ],
                        "required": [
                            "convenioDadosId",
                            "convenioId",
                            "nome",
                            "valor"
                        ],
                        "properties": {
                            "convenioDadosId": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/convenioDadosId",
                                "type": "integer",
                                "title": "The convenioDadosId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    12
                                ]
                            },
                            "convenioId": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/convenioId",
                                "type": "integer",
                                "title": "The convenioId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1
                                ]
                            },
                            "nome": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/nome",
                                "type": "string",
                                "title": "The nome schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "N° da Instalação"
                                ]
                            },
                            "valor": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/valor",
                                "type": "string",
                                "title": "The valor schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "432342342"
                                ]
                            }
                        },
                        "additionalProperties": true
                    }
                ]
            }
        }
    },
    "additionalProperties": true
}'
where route = '/api/proposta/simulacao-crivo' and method = 'POST'