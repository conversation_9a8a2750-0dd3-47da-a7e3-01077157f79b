declare @produtoDocumento table(
    produtoId int,
    documentoId int,
    obrigatorio bit,
    impressao bit,
    etapaId int)

begin TRAN

insert into @produtoDocumento
select distinct
    b.Id produtoId,
    c.Id documentoTipoId,
    1 obrigatorio,
    1 impressao,
    1 etapaId
from VersaoLayoutContrato a
left join Produto b on b.Id = a.<PERSON>du<PERSON><PERSON><PERSON>
left join DocumentoTipo c on c.Tipo = a.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
left join Documento d on d.DocumentoTipoId = c.Id
left join ProdutoDocumento e on e.ProdutoId = b.Id
where e.ProdutoId Is null and a.DataFim IS NULL order by b.Id

insert into ProdutoDocumento
select * from @produtoDocumento

select * from ProdutoDocumento

commit
