declare @tabelaJurosEnergiaId int,
        @produtoEnergiaId int,
        @tabelaJurosLuzEmDiaId int,
        @produtoLuzEmDiaId int

select @tabelaJurosEnergiaId = Id from TabelaJuros with(nolock) where Nome = 'Tabela Energia Padrão'
select @produtoEnergiaId = Id from Produto with(nolock) where Nome = 'Energia'
select @produtoLuzEmDiaId = Id from Produto with(nolock) where Nome = 'Luz em Dia'

-- Solicitação vinda pelo ticket 62986
--Realizando insert na tabela de juros
insert into TabelaJuros (Nome, Tipo, CarenciaMedia, CarenciaMinima, CarenciaMaxima, Ativo, Promocional, VigenciaInicial, VigenciaFinal)
select 
    'Tabela Luz em Dia Padrão' Nome,
    Tipo,
    CarenciaMedia,
    CarenciaMinima,
    CarenciaMaxima,
    Ativo,
    Promocional,
    VigenciaInicial,
    VigenciaFinal
from TabelaJuros with(nolock)
where Id = @tabelaJurosEnergiaId
set @tabelaJurosLuzEmDiaId = @@IDENTITY

-- Insert nos valores da tabela de juros conforme configuração solicitada por ticket.
insert into TabelaJurosValores (TabelaJurosId, Valor, Plano, Juros)
select 
    @tabelaJurosLuzEmDiaId TabelaJurosId,
    Valor,
    Plano,
    Juros
from TabelaJurosValores with(nolock) 
where TabelaJurosId = @tabelaJurosEnergiaId 
and Valor = 700

-- Insert na produto tabela juros para o produto que a tabela de juros pertence.
insert into ProdutoTabelaJuros values (@tabelaJurosLuzEmDiaId, @produtoLuzEmDiaId)

----------------------------------------------------------------------------------------------------------------------------------

-- Solicitação vinda pelo ticket 62988
insert into TabelaJurosValores values
(@tabelaJurosEnergiaId, 1300, 18, 13.15),
(@tabelaJurosEnergiaId, 1300, 20, 13.21),
(@tabelaJurosEnergiaId, 1300, 22, 13.08)