

declare @erro varchar (1000)

BEGIN TRAN
    BEGIN TRY

        insert into Parametro.Enum
        values ('tempoResidencia ', '[{"id": 0, "nome": "Menos que 1 ano"},  { "id": 1, "nome": "De 1 a 3 anos"},  { "id": 2,"nome":"De 3 a 5 anos"},  { "id": 3, "nome": "Mais que 5 anos"}]')

        update Parametro.Enum
        set Valor='[{"id":0, "nome":"Fatura"},{"id":1, "nome":"Renegociação"}]'
        where Id = 35
        
        COMMIT

    END TRY
    BEGIN CATCH
        
         set @erro = ERROR_MESSAGE()
            rollback
            RAISERROR(@erro,16,1)
            return
    END CATCH

