update VersaoLayoutContrato 
set layout = replace(replace(replace(layout,'{{BA<PERSON><PERSON><PERSON><PERSON>}}','{{BANC<PERSON>}}'),'{{BANCOAGENCIA}}',' {{AGENCIA}}'),'{{BANCOCONTA}}','{{CONTA}}')
where Layout like '%{{BANCONOME}}%'
    and TipoDocumento like 'CCB%'

update a
set Layout = STUFF(Layout,PATINDEX('%{{PPENAO}}%',Layout)+18,0,'Não') 
from VersaoLayoutContrato a
where Layout like '%{{PPENAO}}&nbsp;] <%'




