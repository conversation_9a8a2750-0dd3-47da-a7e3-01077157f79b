declare @visualizarId int,
        @editarId int,
        @criarId int,
        @deletarId int,
        @menuId int,
        @telaId int,
        @botaoId int,
        @objetoId int,
        @perfilCadastroId int,
        @perfilAnalistaId int,
        @acessoObjetoId int

select @visualizarId = Id from Seguranca.Acesso with(nolock) where Nome = 'Visualizar'
select @editarId = Id from Seguranca.Acesso with(nolock) where Nome = 'Editar'
select @criarId = Id from Seguranca.Acesso with(nolock) where Nome = 'Criar'
select @deletarId = Id from Seguranca.Acesso with(nolock) where Nome = 'Deletar'
select @menuId = Id from Seguranca.ObjetoGrupo with(nolock) where Nome = 'menu'
select @telaId = Id from Seguranca.ObjetoGrupo with(nolock) where Nome = 'tela'
select @botaoId = Id from Seguranca.ObjetoGrupo with(nolock) where Nome = 'botao'

select @perfilCadastroId = Id from Seguranca.Perfil with(nolock) where Nome = 'Cadastro'
select @perfilAnalistaId = Id from Seguranca.Perfil with(nolock) where Nome = 'Analista'

-- Bloco de cadastro referente a objeto
insert into Seguranca.Objeto values (@menuId, 'Cadastro de Hierarquia', 0, 'menu.cadastro.hierarquia')
set @objetoId = @@IDENTITY

insert into Seguranca.AcessoObjeto values (@visualizarId, @objetoId)
set @acessoObjetoId = @@IDENTITY
insert into Seguranca.PerfilConcessao values (@perfilAnalistaId, null, @acessoObjetoId)
insert into Seguranca.PerfilConcessao values (@perfilCadastroId, null, @acessoObjetoId)

-- Bloco de cadastro referente a objeto
insert into Seguranca.Objeto values (@telaId, 'Cadastro de Hierarquia', 0, 'tela.cadastro.hierarquia')
set @objetoId = @@IDENTITY

insert into Seguranca.AcessoObjeto values (@visualizarId, @objetoId)
set @acessoObjetoId = @@IDENTITY
insert into Seguranca.PerfilConcessao values (@perfilAnalistaId, null, @acessoObjetoId)
insert into Seguranca.PerfilConcessao values (@perfilCadastroId, null, @acessoObjetoId)

insert into Seguranca.AcessoObjeto values (@editarId, @objetoId)
set @acessoObjetoId = @@IDENTITY
insert into Seguranca.PerfilConcessao values (@perfilCadastroId, null, @acessoObjetoId)

insert into Seguranca.AcessoObjeto values (@criarId, @objetoId)
set @acessoObjetoId = @@IDENTITY
insert into Seguranca.PerfilConcessao values (@perfilCadastroId, null, @acessoObjetoId)


-- Bloco de cadastro referente a objeto
insert into Seguranca.Objeto values (@botaoId, 'Cadastro de Hierarquia', 0, 'botao.cadastro.hierarquia.maisHierarquia')
set @objetoId = @@IDENTITY

insert into Seguranca.AcessoObjeto values (@visualizarId, @objetoId)
set @acessoObjetoId = @@IDENTITY
insert into Seguranca.PerfilConcessao values (@perfilCadastroId, null, @acessoObjetoId)

insert into Seguranca.AcessoObjeto values (@criarId, @objetoId)
set @acessoObjetoId = @@IDENTITY
insert into Seguranca.PerfilConcessao values (@perfilCadastroId, null, @acessoObjetoId)

-- Bloco de cadastro referente a objeto
insert into Seguranca.Objeto values (@botaoId, 'Cadastro de Hierarquia', 0, 'botao.cadastro.hierarquia.editar')
set @objetoId = @@IDENTITY

insert into Seguranca.AcessoObjeto values (@visualizarId, @objetoId)
set @acessoObjetoId = @@IDENTITY
insert into Seguranca.PerfilConcessao values (@perfilCadastroId, null, @acessoObjetoId)

insert into Seguranca.AcessoObjeto values (@editarId, @objetoId)
set @acessoObjetoId = @@IDENTITY
insert into Seguranca.PerfilConcessao values (@perfilCadastroId, null, @acessoObjetoId)

-- Bloco de cadastro referente a objeto
insert into Seguranca.Objeto values (@botaoId, 'Cadastro de Hierarquia', 0, 'botao.cadastro.hierarquia.salvar')
set @objetoId = @@IDENTITY

insert into Seguranca.AcessoObjeto values (@visualizarId, @objetoId)
set @acessoObjetoId = @@IDENTITY
insert into Seguranca.PerfilConcessao values (@perfilCadastroId, null, @acessoObjetoId)

insert into Seguranca.AcessoObjeto values (@editarId, @objetoId)
set @acessoObjetoId = @@IDENTITY
insert into Seguranca.PerfilConcessao values (@perfilCadastroId, null, @acessoObjetoId)

insert into Seguranca.AcessoObjeto values (@criarId, @objetoId)
set @acessoObjetoId = @@IDENTITY
insert into Seguranca.PerfilConcessao values (@perfilCadastroId, null, @acessoObjetoId)