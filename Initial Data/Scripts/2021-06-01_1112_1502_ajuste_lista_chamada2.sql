BEGIN

    --
    -- Id do controller
    -- Não necessário definir valor
    --
    declare @controllerId int 

    --
    -- Variáveis de inserção
    -- Defina os valores do controlador caso seja inserção
    -- Defina os valores da rota para inserção de nova rota
    --
    declare @controllerName varchar(50) = 'Chamada',
            @controllerUser varchar(50) = 'crefazapisqlstag',
            @controllerDesc varchar(MAX) = 'Acesso a lista de situação de chamada',
            @routeName varchar(255) = 'Lista situação chamada',
            @routeDesc varchar(MAX) = 'Listagem de situação de chamada',
            @route varchar(100) = '/api/chamada/lista-situacao-chamada',
            @routeMethod varchar(10) = 'GET',
            @routeProcedure varchar(255) = 'controller.stpChamada',
            @routeInput varchar(MAX) = '{}'
            ,
            @routeOutput varchar(MAX) = '{}',
            @routeResponseId int = 2

    select @controllerId = Id from api.controller where [name] = @controllerName

    if (@controllerId IS NULL)
    BEGIN
        insert into api.controller
        (
            [user],
            [name],
            [description]
        )
        VALUES
        (
            @controllerUser,
            @controllerName,
            @controllerDesc
        )

        set @controllerId = @@IDENTITY
    END

    insert into api.route
    (
        [controller_id],
        [name],
        [description],
        [route],
        [method],
        [procedure],
        [input],
        [output],
        [response_type_id]
    ) values (
        @controllerId,
        @routeName,
        @routeDesc,
        @route,
        @routeMethod,
        @routeProcedure,
        @routeInput,
        @routeOutput,
        @routeResponseId
    )

END
-- mudança para criar um novo commit