BEGIN

    --
    -- Id do controller
    -- Não necessário definir valor
    --
    declare @controllerId int

    --
    -- Variáveis de inserção
    -- Defina os valores do controlador caso seja inserção
    -- Defina os valores da rota para inserção de nova rota
    --
    declare @controllerName varchar(50) = 'Imagem',
            @controllerUser varchar(50) = 'crefazapisqlstag',
            @controllerDesc varchar(MAX) = 'Acesso as imagens carregadas no sistema em caráter temporário',
            @routeName varchar(255) = 'Download Imagem',
            @routeDesc varchar(MAX) = 'Retorna imagens pra download',
            @route varchar(100) = '/api/imagem/proposta/{propostaImagemId}',
            @routeMethod varchar(10) = 'GET',
            @routeProcedure varchar(255) = 'controller.stpCarregaImagem',
            @routeInput varchar(MAX) = '{}',
            @routeOutput varchar(MAX) = '{}',
            @routeResponseId int = 2

    select @controllerId = Id from api.controller where [name] = @controllerName

    if (@controllerId IS NULL)
    BEGIN
        insert into api.controller
        (
            [user],
            [name],
            [description]
        )
        VALUES
        (
            @controllerUser,
            @controllerName,
            @controllerDesc
        )

        set @controllerId = @@IDENTITY
    END

    insert into api.route
    (
        [controller_id],
        [name],
        [description],
        [route],
        [method],
        [procedure],
        [input],
        [output],
        [response_type_id]
    ) values (
        @controllerId,
        @routeName,
        @routeDesc,
        @route,
        @routeMethod,
        @routeProcedure,
        @routeInput,
        @routeOutput,
        @routeResponseId
    )

END
