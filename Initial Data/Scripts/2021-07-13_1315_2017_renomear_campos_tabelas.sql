


declare @erro varchar (1000)

BEGIN TRAN
    BEGIN TRY
        EXEC sp_rename 'dbo.Renda.TempoEmpregoId', 'TempoEmprego', 'COLUMN';

        EXEC sp_rename 'dbo.PessoaFisica.GrauInstrucao', 'GrauInstrucaoId', 'COLUMN';

        ALTER TABLE dbo.Unidade ADD CONSTRAINT FK_Unidade_Cidade FOREIGN KEY (CidadeId)
        REFERENCES dbo.Cidade(Id)

        COMMIT
    END TRY
    BEGIN CATCH
        
         set @erro = ERROR_MESSAGE()
            rollback
            RAISERROR(@erro,16,1)
            return
    END CATCH












