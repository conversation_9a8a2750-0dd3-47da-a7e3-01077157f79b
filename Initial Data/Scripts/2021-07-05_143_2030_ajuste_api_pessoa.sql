-- removido a propriedade email de contatosExtra
update api.route
set [input] = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [{
        "pessoa": {
            "id": 15,
            "nome": "Pedro Pedre<PERSON>",
            "nascimento": "1985-04-16",
            "cpf": "59234850017",
            "documentoEmissor": "SSP",
            "documentoUFId": 8,
            "documentoEmissao": "2000-01-01",
            "pep": false,
            "sexo": 1,
            "estadoCivil": 1,
            "paisId": 1,
            "naturalidadeUfId": 8,
            "naturalidadeCidadeId": 1905,
            "grauInstrucao": 3,
            "nomeMae": "<PERSON>",
            "nomeConjuge": "<PERSON><PERSON><PERSON> <PERSON>",
            "rg": "5768499",
            "pessoaFisicaId": 8
        },
        "endereco": {
            "cep": "29156123",
            "logradouro": "Rua <PERSON>ão Coelho",
            "numero": "201",
            "ufid": 8,
            "cidadeId": 2332,
            "bairro": "Cariacica Sede",
            "complemento": null,
            "enderecoId": null
        },
        "profissao": {
            "empresa": "Crefaz",
            "ocupacaoId": 1,
            "profissaoId": 1,
            "tempoempregoId": 1,
            "renda": 3500,
            "outrasRendas": 0,
            "tipoOutrasRendas": null,
            "telefoneRH": "27985642311",
            "rendaId": null,
            "pis": "93578"
        },
        "contatos": {
            "telefoneFixo": "2732021462",
            "telefoneCelular": "99999546216",
            "email": "<EMAIL>",
            "contatosExtras": [{
                "contatoId": 346,
                "pessoaId": 15,
                "funcao": 1,
                "telefone": "27998511602"
            }]
        },
        "referencias": [{
            "referenciaId": 156,
            "pessoaId": 15,
            "nome": "Maicon Jhonson da Costa",
            "telefone": "27997227489",
            "grau": 0
        }, {
            "referenciaId": 157,
            "pessoaId": 15,
            "nome": "Anderson silva",
            "telefone": "27994857900",
            "grau": 1
        }],
        "dadosConvenio": [{
            "propostaId": 7,
            "ativo": true,
            "nomeConvenio": "ENEL SP",
            "dadosAdicionais": [{
                "nome": "N° da Instalação",
                "tipo": 2,
                "formato": "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d]",
                "mensagem": "N° da instalação inválido. Informe o N° da instalação com nove dígitos.",
                "valor": "121212121"
            }, {
                "nome": "Data de Leitura",
                "tipo": 4,
                "formato": null,
                "mensagem": "Data inválida.",
                "valor": "2021-06-08T20:58:29.280Z"
            }]
        }, {
            "propostaId": 20,
            "ativo": true,
            "nomeConvenio": "Coelba",
            "dadosAdicionais": [{
                "nome": "Conta Contrato",
                "tipo": 2,
                "formato": "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d]",
                "mensagem": "Conta contrato inválida. Informe a conta com doze dígitos",
                "valor": "121222121222"
            }, {
                "nome": "Data de Leitura",
                "tipo": 4,
                "formato": null,
                "mensagem": null,
                "valor": "2021-06-16T13:32:24.373Z"
            }]
        }, {
            "propostaId": 30,
            "ativo": true,
            "nomeConvenio": "CPFL",
            "dadosAdicionais": [{
                "nome": "Lote",
                "tipo": 2,
                "formato": "[\\d],[\\d]",
                "mensagem": "Lote inválido. Informe o Lote com dois dígitos",
                "valor": "11"
            }, {
                "nome": "PN",
                "tipo": 2,
                "formato": "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d]",
                "mensagem": "PN inválido. Informe o PN com nove dígitos",
                "valor": "452052455"
            }, {
                "nome": "Seu Código",
                "tipo": 2,
                "formato": "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d]",
                "mensagem": "Seu Código inválido. Informe o Seu Código com oito dígitos",
                "valor": "16546513"
            }]
        }, {
            "propostaId": 31,
            "ativo": true,
            "nomeConvenio": "ENEL RJ",
            "dadosAdicionais": [{
                "nome": "Rota",
                "tipo": 2,
                "formato": "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],\" \",[\\d],[\\d],\" \",[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],\" \",\"-\",\" \",[\\d]",
                "mensagem": "Rota inválida. Informe a Rota com dezesseis dígitos",
                "valor": "4651321 03 543651 - 3"
            }, {
                "nome": "N° da instalação",
                "tipo": 2,
                "formato": "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],\" \",\"-\",\" \",[\\d]",
                "mensagem": "N° da instalação inválido. Informe o N° da instalação com oito dígitos.",
                "valor": "6546354 - 6"
            }, {
                "nome": "N° do cliente",
                "tipo": 2,
                "formato": "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],\" \",\"-\",\" \",[\\d]",
                "mensagem": "N° do cliente inválido. Informe o N° do cliente com oito dígitos.",
                "valor": "6454616 - 5"
            }]
        }]
    }],
    "required": ["pessoa", "endereco", "profissao", "contatos", "referencias"],
    "properties": {
        "pessoa": {
            "$id": "#/properties/pessoa",
            "type": "object",
            "title": "The pessoa schema",
            "description": "An explanation about the purpose of this instance.",
            "default": {},
            "examples": [{
                "id": 15,
                "nome": "Pedro Pedreiro",
                "nascimento": "1985-04-16",
                "cpf": "59234850017",
                "documentoEmissor": "SSP",
                "documentoUFId": 8,
                "documentoEmissao": "2000-01-01",
                "pep": false,
                "sexo": 1,
                "estadoCivil": 1,
                "paisId": 1,
                "naturalidadeUfId": 8,
                "naturalidadeCidadeId": 1905,
                "grauInstrucao": 3,
                "nomeMae": "Elaine Espatuleta",
                "nomeConjuge": "Mulher do Pedro",
                "rg": "5768499",
                "pessoaFisicaId": 8
            }],
            "required": ["id", "nome", "nascimento", "cpf", "documentoEmissor", "documentoUFId", "documentoEmissao", "pep", "sexo", "estadoCivil", "paisId", "naturalidadeCidadeId", "grauInstrucao", "nomeMae", "rg", "pessoaFisicaId"],
            "properties": {
                "id": {
                    "$id": "#/properties/pessoa/properties/id",
                    "type": "integer",
                    "title": "The id schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [15]
                },
                "nome": {
                    "$id": "#/properties/pessoa/properties/nome",
                    "type": "string",
                    "title": "The nome schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": ["Pedro Pedreiro"]
                },
                "nascimento": {
                    "$id": "#/properties/pessoa/properties/nascimento",
                    "type": "string",
                    "title": "The nascimento schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": ["1985-04-16"]
                },
                "cpf": {
                    "$id": "#/properties/pessoa/properties/cpf",
                    "type": "string",
                    "title": "The cpf schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": ["59234850017"]
                },
                "documentoEmissor": {
                    "$id": "#/properties/pessoa/properties/documentoEmissor",
                    "type": "string",
                    "title": "The documentoEmissor schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": ["SSP"]
                },
                "documentoUFId": {
                    "$id": "#/properties/pessoa/properties/documentoUFId",
                    "type": "integer",
                    "title": "The documentoUFId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [8]
                },
                "documentoEmissao": {
                    "$id": "#/properties/pessoa/properties/documentoEmissao",
                    "type": "string",
                    "title": "The documentoEmissao schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": ["2000-01-01"]
                },
                "pep": {
                    "$id": "#/properties/pessoa/properties/pep",
                    "type": "boolean",
                    "title": "The pep schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": false,
                    "examples": [false]
                },
                "sexo": {
                    "$id": "#/properties/pessoa/properties/sexo",
                    "type": "integer",
                    "title": "The sexo schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [1]
                },
                "estadoCivil": {
                    "$id": "#/properties/pessoa/properties/estadoCivil",
                    "type": "integer",
                    "title": "The estadoCivil schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [1]
                },
                "paisId": {
                    "$id": "#/properties/pessoa/properties/paisId",
                    "type": "integer",
                    "title": "The paisId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [1]
                },
                "naturalidadeUfId": {
                    "$id": "#/properties/pessoa/properties/naturalidadeUfId",
                    "type": "integer",
                    "title": "The naturalidadeUfId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [8]
                },
                "naturalidadeCidadeId": {
                    "$id": "#/properties/pessoa/properties/naturalidadeCidadeId",
                    "type": "integer",
                    "title": "The naturalidadeCidadeId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [1905]
                },
                "grauInstrucao": {
                    "$id": "#/properties/pessoa/properties/grauInstrucao",
                    "type": "integer",
                    "title": "The grauInstrucao schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [3]
                },
                "nomeMae": {
                    "$id": "#/properties/pessoa/properties/nomeMae",
                    "type": "string",
                    "title": "The nomeMae schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": ["Elaine Espatuleta"]
                },
                "nomeConjuge": {
                    "$id": "#/properties/pessoa/properties/nomeConjuge",
                    "type": "string",
                    "title": "The nomeConjuge schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": ["Mulher do Pedro"]
                },
                "rg": {
                    "$id": "#/properties/pessoa/properties/rg",
                    "type": "string",
                    "title": "The rg schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": ["5768499"]
                },
                "pessoaFisicaId": {
                    "$id": "#/properties/pessoa/properties/pessoaFisicaId",
                    "type": "integer",
                    "title": "The pessoaFisicaId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [8]
                }
            },
            "additionalProperties": true
        },
        "endereco": {
            "$id": "#/properties/endereco",
            "type": "object",
            "title": "The endereco schema",
            "description": "An explanation about the purpose of this instance.",
            "default": {},
            "examples": [{
                "cep": "29156123",
                "logradouro": "Rua Irene Brandão Coelho",
                "numero": "201",
                "ufid": 8,
                "cidadeId": 2332,
                "bairro": "Cariacica Sede",
                "complemento": null,
                "enderecoId": null
            }],
            "required": ["cep", "logradouro", "numero", "cidadeId", "bairro", "enderecoId"],
            "properties": {
                "cep": {
                    "$id": "#/properties/endereco/properties/cep",
                    "type": "string",
                    "title": "The cep schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": ["29156123"]
                },
                "logradouro": {
                    "$id": "#/properties/endereco/properties/logradouro",
                    "type": "string",
                    "title": "The logradouro schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": ["Rua Irene Brandão Coelho"]
                },
                "numero": {
                    "$id": "#/properties/endereco/properties/numero",
                    "type": "string",
                    "title": "The numero schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": ["201"]
                },
                "ufid": {
                    "$id": "#/properties/endereco/properties/ufid",
                    "type": "integer",
                    "title": "The ufid schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [8]
                },
                "cidadeId": {
                    "$id": "#/properties/endereco/properties/cidadeId",
                    "type": "integer",
                    "title": "The cidadeId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [2332]
                },
                "bairro": {
                    "$id": "#/properties/endereco/properties/bairro",
                    "type": "string",
                    "title": "The bairro schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": ["Cariacica Sede"]
                },
                "complemento": {
                    "$id": "#/properties/endereco/properties/complemento",
                    "type": "null",
                    "title": "The complemento schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": null,
                    "examples": [null]
                },
                "enderecoId": {
                    "$id": "#/properties/endereco/properties/enderecoId",
                    "type": "null",
                    "title": "The enderecoId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": null,
                    "examples": [null]
                }
            },
            "additionalProperties": true
        },
        "profissao": {
            "$id": "#/properties/profissao",
            "type": "object",
            "title": "The profissao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": {},
            "examples": [{
                "empresa": "Crefaz",
                "ocupacaoId": 1,
                "profissaoId": 1,
                "tempoempregoId": 1,
                "renda": 3500,
                "outrasRendas": 0,
                "tipoOutrasRendas": null,
                "telefoneRH": "27985642311",
                "rendaId": null,
                "pis": "93578"
            }],
            "required": ["empresa", "ocupacaoId", "profissaoId", "tempoempregoId", "renda", "telefoneRH", "rendaId", "pis"],
            "properties": {
                "empresa": {
                    "$id": "#/properties/profissao/properties/empresa",
                    "type": "string",
                    "title": "The empresa schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": ["Crefaz"]
                },
                "ocupacaoId": {
                    "$id": "#/properties/profissao/properties/ocupacaoId",
                    "type": "integer",
                    "title": "The ocupacaoId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [1]
                },
                "profissaoId": {
                    "$id": "#/properties/profissao/properties/profissaoId",
                    "type": "integer",
                    "title": "The profissaoId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [1]
                },
                "tempoempregoId": {
                    "$id": "#/properties/profissao/properties/tempoempregoId",
                    "type": "integer",
                    "title": "The tempoempregoId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [1]
                },
                "renda": {
                    "$id": "#/properties/profissao/properties/renda",
                    "type": "integer",
                    "title": "The renda schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [3500]
                },
                "outrasRendas": {
                    "$id": "#/properties/profissao/properties/outrasRendas",
                    "type": "integer",
                    "title": "The outrasRendas schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [0]
                },
                "tipoOutrasRendas": {
                    "$id": "#/properties/profissao/properties/tipoOutrasRendas",
                    "type": "null",
                    "title": "The tipoOutrasRendas schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": null,
                    "examples": [null]
                },
                "telefoneRH": {
                    "$id": "#/properties/profissao/properties/telefoneRH",
                    "type": "string",
                    "title": "The telefoneRH schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": ["27985642311"]
                },
                "rendaId": {
                    "$id": "#/properties/profissao/properties/rendaId",
                    "type": "null",
                    "title": "The rendaId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": null,
                    "examples": [null]
                },
                "pis": {
                    "$id": "#/properties/profissao/properties/pis",
                    "type": "string",
                    "title": "The pis schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": ["93578"]
                }
            },
            "additionalProperties": true
        },
        "contatos": {
            "$id": "#/properties/contatos",
            "type": "object",
            "title": "The contatos schema",
            "description": "An explanation about the purpose of this instance.",
            "default": {},
            "examples": [{
                "telefoneFixo": "2732021462",
                "telefoneCelular": "99999546216",
                "email": "<EMAIL>",
                "contatosExtras": [{
                    "contatoId": 346,
                    "pessoaId": 15,
                    "funcao": 1,
                    "telefone": "27998511602"
                }]
            }],
            "required": ["telefoneFixo", "telefoneCelular", "email", "contatosExtras"],
            "properties": {
                "telefoneFixo": {
                    "$id": "#/properties/contatos/properties/telefoneFixo",
                    "type": "string",
                    "title": "The telefoneFixo schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": ["2732021462"]
                },
                "telefoneCelular": {
                    "$id": "#/properties/contatos/properties/telefoneCelular",
                    "type": "string",
                    "title": "The telefoneCelular schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": ["99999546216"]
                },
                "email": {
                    "$id": "#/properties/contatos/properties/email",
                    "type": "string",
                    "title": "The email schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": ["<EMAIL>"]
                },
                "contatosExtras": {
                    "$id": "#/properties/contatos/properties/contatosExtras",
                    "type": "array",
                    "title": "The contatosExtras schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": [],
                    "examples": [
                        [{
                            "contatoId": 346,
                            "pessoaId": 15,
                            "funcao": 1,
                            "telefone": "27998511602"
                        }]
                    ],
                    "additionalItems": true,
                    "items": {
                        "$id": "#/properties/contatos/properties/contatosExtras/items",
                        "anyOf": [{
                            "$id": "#/properties/contatos/properties/contatosExtras/items/anyOf/0",
                            "type": "object",
                            "title": "The first anyOf schema",
                            "description": "An explanation about the purpose of this instance.",
                            "default": {},
                            "examples": [{
                                "contatoId": 346,
                                "pessoaId": 15,
                                "funcao": 1,
                                "telefone": "27998511602",
                                "email": "<EMAIL>"
                            }],
                            "required": ["contatoId", "pessoaId", "funcao", "telefone"],
                            "properties": {
                                "contatoId": {
                                    "$id": "#/properties/contatos/properties/contatosExtras/items/anyOf/0/properties/contatoId",
                                    "type": "integer",
                                    "title": "The contatoId schema",
                                    "description": "An explanation about the purpose of this instance.",
                                    "default": 0,
                                    "examples": [346]
                                },
                                "pessoaId": {
                                    "$id": "#/properties/contatos/properties/contatosExtras/items/anyOf/0/properties/pessoaId",
                                    "type": "integer",
                                    "title": "The pessoaId schema",
                                    "description": "An explanation about the purpose of this instance.",
                                    "default": 0,
                                    "examples": [15]
                                },
                                "funcao": {
                                    "$id": "#/properties/contatos/properties/contatosExtras/items/anyOf/0/properties/funcao",
                                    "type": "integer",
                                    "title": "The funcao schema",
                                    "description": "An explanation about the purpose of this instance.",
                                    "default": 0,
                                    "examples": [1]
                                },
                                "telefone": {
                                    "$id": "#/properties/contatos/properties/contatosExtras/items/anyOf/0/properties/telefone",
                                    "type": "string",
                                    "title": "The telefone schema",
                                    "description": "An explanation about the purpose of this instance.",
                                    "default": "",
                                    "examples": ["27998511602"]
                                }
                            },
                            "additionalProperties": true
                        }]
                    }
                }
            },
            "additionalProperties": true
        },
        "referencias": {
            "$id": "#/properties/referencias",
            "type": "array",
            "title": "The referencias schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [{
                    "referenciaId": 156,
                    "pessoaId": 15,
                    "nome": "Maicon Jhonson da Costa",
                    "telefone": "27997227489",
                    "grau": 0
                }, {
                    "referenciaId": 157,
                    "pessoaId": 15,
                    "nome": "Anderson silva",
                    "telefone": "27994857900",
                    "grau": 1
                }]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/referencias/items",
                "anyOf": [{
                    "$id": "#/properties/referencias/items/anyOf/0",
                    "type": "object",
                    "title": "The first anyOf schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": {},
                    "examples": [{
                        "referenciaId": 156,
                        "pessoaId": 15,
                        "nome": "Maicon Jhonson da Costa",
                        "telefone": "27997227489",
                        "grau": 0
                    }],
                    "required": ["referenciaId", "pessoaId", "nome", "telefone", "grau"],
                    "properties": {
                        "referenciaId": {
                            "$id": "#/properties/referencias/items/anyOf/0/properties/referenciaId",
                            "type": "integer",
                            "title": "The referenciaId schema",
                            "description": "An explanation about the purpose of this instance.",
                            "default": 0,
                            "examples": [156]
                        },
                        "pessoaId": {
                            "$id": "#/properties/referencias/items/anyOf/0/properties/pessoaId",
                            "type": "integer",
                            "title": "The pessoaId schema",
                            "description": "An explanation about the purpose of this instance.",
                            "default": 0,
                            "examples": [15]
                        },
                        "nome": {
                            "$id": "#/properties/referencias/items/anyOf/0/properties/nome",
                            "type": "string",
                            "title": "The nome schema",
                            "description": "An explanation about the purpose of this instance.",
                            "default": "",
                            "examples": ["Maicon Jhonson da Costa"]
                        },
                        "telefone": {
                            "$id": "#/properties/referencias/items/anyOf/0/properties/telefone",
                            "type": "string",
                            "title": "The telefone schema",
                            "description": "An explanation about the purpose of this instance.",
                            "default": "",
                            "examples": ["27997227489"]
                        },
                        "grau": {
                            "$id": "#/properties/referencias/items/anyOf/0/properties/grau",
                            "type": "integer",
                            "title": "The grau schema",
                            "description": "An explanation about the purpose of this instance.",
                            "default": 0,
                            "examples": [0]
                        }
                    },
                    "additionalProperties": true
                }]
            }
        },
        "dadosConvenio": {
            "$id": "#/properties/dadosConvenio",
            "type": "array",
            "title": "The dadosConvenio schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [{
                    "propostaId": 7,
                    "ativo": true,
                    "nomeConvenio": "ENEL SP",
                    "dadosAdicionais": [{
                        "nome": "N° da Instalação",
                        "tipo": 2,
                        "formato": "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d]",
                        "mensagem": "N° da instalação inválido. Informe o N° da instalação com nove dígitos.",
                        "valor": "121212121"
                    }, {
                        "nome": "Data de Leitura",
                        "tipo": 4,
                        "formato": null,
                        "mensagem": "Data inválida.",
                        "valor": "2021-06-08T20:58:29.280Z"
                    }]
                }, {
                    "propostaId": 20,
                    "ativo": true,
                    "nomeConvenio": "Coelba",
                    "dadosAdicionais": [{
                        "nome": "Conta Contrato",
                        "tipo": 2,
                        "formato": "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d]",
                        "mensagem": "Conta contrato inválida. Informe a conta com doze dígitos",
                        "valor": "121222121222"
                    }, {
                        "nome": "Data de Leitura",
                        "tipo": 4,
                        "formato": null,
                        "mensagem": null,
                        "valor": "2021-06-16T13:32:24.373Z"
                    }]
                }]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/dadosConvenio/items",
                "anyOf": [{
                    "$id": "#/properties/dadosConvenio/items/anyOf/0",
                    "type": "object",
                    "title": "The first anyOf schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": {},
                    "examples": [{
                        "propostaId": 7,
                        "ativo": true,
                        "nomeConvenio": "ENEL SP",
                        "dadosAdicionais": [{
                            "nome": "N° da Instalação",
                            "tipo": 2,
                            "formato": "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d]",
                            "mensagem": "N° da instalação inválido. Informe o N° da instalação com nove dígitos.",
                            "valor": "121212121"
                        }, {
                            "nome": "Data de Leitura",
                            "tipo": 4,
                            "formato": null,
                            "mensagem": "Data inválida.",
                            "valor": "2021-06-08T20:58:29.280Z"
                        }]
                    }],
                    "required": ["propostaId", "ativo", "dadosAdicionais"],
                    "properties": {
                        "propostaId": {
                            "$id": "#/properties/dadosConvenio/items/anyOf/0/properties/propostaId",
                            "type": "integer",
                            "title": "The propostaId schema",
                            "description": "An explanation about the purpose of this instance.",
                            "default": 0,
                            "examples": [7]
                        },
                        "ativo": {
                            "$id": "#/properties/dadosConvenio/items/anyOf/0/properties/ativo",
                            "type": "boolean",
                            "title": "The ativo schema",
                            "description": "An explanation about the purpose of this instance.",
                            "default": false,
                            "examples": [true]
                        },
                        "nomeConvenio": {
                            "$id": "#/properties/dadosConvenio/items/anyOf/0/properties/nomeConvenio",
                            "type": "string",
                            "title": "The nomeConvenio schema",
                            "description": "An explanation about the purpose of this instance.",
                            "default": "",
                            "examples": ["ENEL SP"]
                        },
                        "dadosAdicionais": {
                            "$id": "#/properties/dadosConvenio/items/anyOf/0/properties/dadosAdicionais",
                            "type": "array",
                            "title": "The dadosAdicionais schema",
                            "description": "An explanation about the purpose of this instance.",
                            "default": [],
                            "examples": [
                                [{
                                    "nome": "N° da Instalação",
                                    "tipo": 2,
                                    "formato": "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d]",
                                    "mensagem": "N° da instalação inválido. Informe o N° da instalação com nove dígitos.",
                                    "valor": "121212121"
                                }, {
                                    "nome": "Data de Leitura",
                                    "tipo": 4,
                                    "formato": null,
                                    "mensagem": "Data inválida.",
                                    "valor": "2021-06-08T20:58:29.280Z"
                                }]
                            ],
                            "additionalItems": true,
                            "items": {
                                "$id": "#/properties/dadosConvenio/items/anyOf/0/properties/dadosAdicionais/items",
                                "anyOf": [{
                                    "$id": "#/properties/dadosConvenio/items/anyOf/0/properties/dadosAdicionais/items/anyOf/0",
                                    "type": "object",
                                    "title": "The first anyOf schema",
                                    "description": "An explanation about the purpose of this instance.",
                                    "default": {},
                                    "examples": [{
                                        "nome": "N° da Instalação",
                                        "tipo": 2,
                                        "formato": "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d]",
                                        "mensagem": "N° da instalação inválido. Informe o N° da instalação com nove dígitos.",
                                        "valor": "121212121"
                                    }],
                                    "required": ["valor"],
                                    "properties": {
                                        "nome": {
                                            "$id": "#/properties/dadosConvenio/items/anyOf/0/properties/dadosAdicionais/items/anyOf/0/properties/nome",
                                            "type": "string",
                                            "title": "The nome schema",
                                            "description": "An explanation about the purpose of this instance.",
                                            "default": "",
                                            "examples": ["N° da Instalação"]
                                        },
                                        "tipo": {
                                            "$id": "#/properties/dadosConvenio/items/anyOf/0/properties/dadosAdicionais/items/anyOf/0/properties/tipo",
                                            "type": "integer",
                                            "title": "The tipo schema",
                                            "description": "An explanation about the purpose of this instance.",
                                            "default": 0,
                                            "examples": [2]
                                        },
                                        "formato": {
                                            "$id": "#/properties/dadosConvenio/items/anyOf/0/properties/dadosAdicionais/items/anyOf/0/properties/formato",
                                            "type": "string",
                                            "title": "The formato schema",
                                            "description": "An explanation about the purpose of this instance.",
                                            "default": "",
                                            "examples": ["[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d]"]
                                        },
                                        "mensagem": {
                                            "$id": "#/properties/dadosConvenio/items/anyOf/0/properties/dadosAdicionais/items/anyOf/0/properties/mensagem",
                                            "type": "string",
                                            "title": "The mensagem schema",
                                            "description": "An explanation about the purpose of this instance.",
                                            "default": "",
                                            "examples": ["N° da instalação inválido. Informe o N° da instalação com nove dígitos."]
                                        },
                                        "valor": {
                                            "$id": "#/properties/dadosConvenio/items/anyOf/0/properties/dadosAdicionais/items/anyOf/0/properties/valor",
                                            "type": "string",
                                            "title": "The valor schema",
                                            "description": "An explanation about the purpose of this instance.",
                                            "default": "",
                                            "examples": ["121212121"]
                                        }
                                    },
                                    "additionalProperties": true
                                }]
                            }
                        }
                    },
                    "additionalProperties": true
                }]
            }
        }
    },
    "additionalProperties": true
}'
where method = 'PUT' and route = '/api/pessoa/Cadastro-Pessoa-Atualiza/{id}'