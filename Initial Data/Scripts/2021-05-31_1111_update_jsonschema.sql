declare @json varchar(max) = 
'{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "id": 1,
            "status": 1,
            "confirmacao": true
        }
    ],
    "required": [
        "id",
        "status"
    ],
    "properties": {
        "id": {
            "$id": "#/properties/id",
            "type": "integer",
            "title": "The id schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "status": {
            "$id": "#/properties/status",
            "type": "integer",
            "title": "The status schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "confirmacao": {
            "$id": "#/properties/confirmacao",
            "type": "boolean",
            "title": "The confirmacao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": false,
            "examples": [
                true
            ]
        }
    },
    "additionalProperties": true
}'
update a 
set [input] = @json
from api.route a 
where method = 'put' 
    and route like '/api/chamada/{id}'