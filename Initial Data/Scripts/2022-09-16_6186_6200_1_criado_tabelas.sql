alter table Seguranca.<PERSON><PERSON>ey add constraint PK_Seguranca_APIKey primary key (Id);

create table dbo.Canal
(
    Id tinyint identity(1,1) not null,
    <PERSON><PERSON> varchar(100) not null,
    <PERSON><PERSON><PERSON><PERSON> varchar(1000) null,
    <PERSON><PERSON> bit not null
);
alter table dbo.<PERSON> add constraint <PERSON><PERSON>_<PERSON> primary key (Id);

create table Seguranca.UnidadeAPIKey
(
    Id int identity not null,
    UnidadeId int not null ,/*(FK tabela dbo.Unidade)*/
    APIKeyId int null /*(FK tabela Seguranca.APIKey)*/
);
alter table Seguranca.UnidadeAPIKey add constraint PK_Seguranca_UnidadeAPIKey primary key (Id);

ALTER TABLE Seguranca.UnidadeAPIKey ADD CONSTRAINT FK_UnidadeAPIKey_Unidade FOREIGN KEY (UnidadeId)    REFERENCES dbo.Unidade(Id);
ALTER TABLE Seguranca.UnidadeAPIKey ADD CONSTRAINT FK_UnidadeAPIKey_APIKey FOREIGN KEY (APIKeyId)    REFERENCES Seguranca.APIKey(Id);

alter table Seguranca.UsuarioToken add APIKeyId int null; /*(FK tabela Seguranca.APIKey)*/
ALTER TABLE Seguranca.UsuarioToken ADD CONSTRAINT FK_UsuarioToken_APIKey FOREIGN KEY (APIKeyId)    REFERENCES Seguranca.APIKey(Id);

alter table dbo.Proposta Add CanalId tinyint null; /*(FK tabela dbo.Canal)*/
ALTER TABLE dbo.Proposta ADD CONSTRAINT FK_Proposta_Canal FOREIGN KEY (CanalId)    REFERENCES dbo.Canal(Id);
