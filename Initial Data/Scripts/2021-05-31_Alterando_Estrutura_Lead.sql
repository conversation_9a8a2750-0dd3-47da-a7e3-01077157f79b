IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Lead]') AND type in (N'U'))
DROP TABLE [dbo].[Lead]

CREATE TABLE [dbo].[Lead](
	[Id] [int] IDENTITY(1,1) NOT NULL,
    [NomeCompleto] [varchar](100) NOT NULL,
    [CPF] [varchar](11) NOT NULL,
	[Telefone] [varchar](11) NOT NULL,
	[PropostaId] [int] NULL,
	[PessoaId] [int] NULL,
    [DataHora] [DATETIME] NOT NULL
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

ALTER TABLE [dbo].[Lead]  WITH CHECK ADD  CONSTRAINT [FK_Pessoa_Id] FOREIGN KEY([PessoaId])
REFERENCES [dbo].[Pessoa] ([Id])

ALTER TABLE [dbo].[Lead] CHECK CONSTRAINT [FK_Pessoa_Id]

ALTER TABLE [dbo].[Lead]  WITH CHECK ADD  CONSTRAINT [FK_Proposta_Id] FOREIGN KEY([PropostaId])
REFERENCES [dbo].[Proposta] ([Id])

ALTER TABLE [dbo].[Lead] CHECK CONSTRAINT [FK_Proposta_Id]

