declare @valor varchar(max) = '[{"DataPrimeiroContrato": true},{"DataUltimoContrato": true},{"bloqueioCpf": true},{"FonePessoal": true},{"ParcelasPagas90Dias": true},{"ParcelasPagas180Dias": true},{"ParcelasPagas270Dias": true},{"ParcelasPagas360Dias": true},{"ParcelasPagas720Dias": true},{"ParcelasPagas1080Dias": true},{"ParcelasPagasVidaInteira": true},{"scoreNegadosUltimos15Dias": true},{"quantidadeDeContratosTomadosPorProduto": true},{"quantidadeDeContratosEmCursoPorProduto": true},{"quantidadeDeDiasAtrasoPorProduto": true},{"quantidadeDiasDesdaUltimaAlteracaoRenda": true},{"quantidadeDiasDeClientela": true},{"porcentualPagamentoPrimeiroContrato": true},{"porcentualPagamentoContratoEmCurso": true},{"maiorValorContratado": true},{"quantidadePropostasFinaizadas": true},{"UltimoAtrasoDiaProduto": true},{"estaDeAcordoComCobrancaEmCurso": true},{"quantidadeDiasAtrasoDeAcordoCobranca": true},{"ComprometimentoRenda": true}]'
declare @parametroId  int 

select @parametroId = id from parametro.parametro 
where codigo = 'RBM_VARIAVEIS_COMPORTAMENTAIS'

update parametro.parametro set valor = @valor where id = @parametroId 

insert into parametro.ParametroHistorico values (@parametroId, dbo.getdateBR(),@valor)
