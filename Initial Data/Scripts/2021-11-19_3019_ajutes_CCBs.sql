
declare @errado varchar(255),
        @novo varchar(255)

set @errado = 'BANCÁRIO Nº.<'
set @novo =   'BANCÁRIO Nº. {{CONTRATO}}<'
UPDATE a set layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica],
        Tipo */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'
and  TipoDocumento like 'ccb%' 
 
set @errado = 'BANCÁRIA N°.<'
set @novo =   'BANCÁRIO Nº. {{CONTRATO}}<'
UPDATE a set layout = replace(Layout,@errado,@novo) /* [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica],
        Tipo */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'
and  TipoDocumento like 'ccb%'
