declare @id int, @valor varchar(255), @GrupoId int

select @GrupoId = Id from Parametro.ParametroGrupo where Nome = 'Crivo - Oferta de Produtos'

update Parametro.Parametro set Valor = '"Driver"."Crefaz"."API Datacob - Divida Ativa Negociação - PF"."Valor - Dívida atualizada - contrato $numero$"' where Codigo = 'SALDO_DEVEDOR'

set @valor = '"Driver"."Crefaz"."API Datacob - Divida Ativa Negociação - PF"."Discreta - Id agrupamento - contrato $numero$"'
insert into Parametro.Parametro (ParametroGrupoId, Tipo, Codigo, Nome, NotaTecnica, Ordem, Valor) values
(@GrupoId,1,'ID_AGRUPAMENTO','ID AGRUPAMENTO','retorna id agrupamento do contrato',1,@valor)
set @id = @@IDENTITY
 
insert into Parametro.ParametroHistorico values (@id,dbo.getdateBR(),@valor)

set @valor = '"Driver"."Crefaz"."API Datacob - Divida Ativa Negociação - PF"."Data - Vencimento - última parcela - contrato $numero$"'
insert into Parametro.Parametro (ParametroGrupoId, Tipo, Codigo, Nome, NotaTecnica, Ordem, Valor) values
(@GrupoId,1,'ULTIMO_VENCIMENTO','ULTIMO VENCIMENTO','retorna vencimento da ultima parcela do contrato',1,@valor)
set @id = @@IDENTITY
 
insert into Parametro.ParametroHistorico values (@id,dbo.getdateBR(),@valor)

set @valor = '"Driver"."Crefaz"."API Datacob - Divida Ativa Negociação - PF"."Quantidade - Parcelas - contrato $numero$"'
insert into Parametro.Parametro (ParametroGrupoId, Tipo, Codigo, Nome, NotaTecnica, Ordem, Valor) values
(@GrupoId,1,'QUANTIDADE_PARCELAS','QUANTIDADE PARCELAS','retorna quantidade de parcelas do contrato',1,@valor)
set @id = @@IDENTITY
 
insert into Parametro.ParametroHistorico values (@id,dbo.getdateBR(),@valor)

set @valor = '"Driver"."Crefaz"."API Datacob - Divida Ativa Negociação - PF"."Data - Vencimento - parcela 1 - contrato $numero$"'
insert into Parametro.Parametro (ParametroGrupoId, Tipo, Codigo, Nome, NotaTecnica, Ordem, Valor) values
(@GrupoId,1,'VENCIMENTO_PRIMEIRA_PARCELA','VENCIMENTO PRIMEIRA PARCELA','retorna venciemnto da primeira parcelas do contrato',1,@valor)
set @id = @@IDENTITY
 
insert into Parametro.ParametroHistorico values (@id,dbo.getdateBR(),@valor)

