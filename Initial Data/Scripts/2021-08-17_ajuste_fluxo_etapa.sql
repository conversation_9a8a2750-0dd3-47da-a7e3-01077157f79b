-- ajuste para permitir alteração de etapa manual
DECLARE @PropostaEtapaId INT, @PropostaProximaEtapaId int
select @PropostaEtapaId = Id from PropostaEtapa where Nome = 'Aguard. Validação'

select @PropostaProximaEtapaId = Id from PropostaEtapa where Nome = 'Análise Validação'


update PropostaEtapaFluxo
set AcaoManual = 1 
where PropostaEtapaId = @PropostaEtapaId 
and PropostaProximaEtapaId = @PropostaProximaEtapaId
and AcaoManual = 0


--- ajuste json

UPDATE api.route
set route = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "id": 6,
            "checados": [
                1,
                5,
                8,
                45
            ],
            "situacao": {
                "etapaId": 1,
                "decisaoId": null,
                "motivos": [
                    1,
                    5,
                    75,
                    23,
                    65
                ]
            },
            "operacao": {
                "produtoId": 1,
                "diaRecebimento": 1,
                "vencimento": "2021-06-02",
                "tabelaJurosId": 1,
                "taxa": 17,
                "operacao": 2074.96,
                "valorContratado": 1500,
                "prazo": 1,
                "prestacao": 2074.96,
                "limitePrestacao": 2075.0
            },
            "debitosConveniada": [
                {
                    "id": 1,
                    "codigo": "1234567890",
                    "dataReferencia": "02/2021",
                    "dataVencimento": "20210410",
                    "valor": 180.58,
                    "tipo": 1,
                    "consumo": 91.4,
                    "situacao": 1,
                    "codigoBarras": "00190000090287490201436287135176186160000021366",
                    "comprovanteId": null,
                    "comprovante": null,
                    "descontar": true,
                    "participa": true
                }
            ],
            "cliente": {
                "cpf": "62073998038",
                "nome": "Edson Alves",
                "nascimento": "2021-02-09",
                "rg": "6712090",
                "rgEmissor": "SESP",
                "rgUfId": 8,
                "rgEmissao": "2021-03-26",
                "sexo": 1,
                "estadoCivil": 0,
                "grauInstrucaoId": 1,
                "nacionalidadeId": 1,
                "naturalidadeCidadeId": 1906,
                "nomeMae": "Maria Cristina Alves",
                "nomeConjugue": null
            },
            "contatos": {
                "contato": {
                    "email": "<EMAIL>",
                    "telefone": "33989225953",
                    "telefoneFixo": "1284520729",
                    "telefoneExtra": null
                },
                "referencia": [
                    {
                        "id": 61,
                        "nome": "Daniel Vinicius Enrico Fernandes",
                        "telefone": "3325785379",
                        "grau": 0,
                        "logAlterados": []
                    },
                    {
                        "id": 62,
                        "nome": "Alexandre Renato Bernardo Jesus",
                        "telefone": "3199925634",
                        "grau": 0,
                        "logAlterados": []
                    }
                ]
            },
            "endereco": {
                "cep": "29090640",
                "logradouro": "Av. Vitória",
                "numero": 1012,
                "cidadeId": 1906,
                "bairro": "Bento Ferreira",
                "complemento": null
            },
            "bancario": {
                "bancoId": null,
                "agencia": 1,
                "numero": "828168-5",
                "conta": 1,
                "tipoConta": 1,
                "tipoOperacao": null,
                "tempoConta": 5
            },
            "profissional": {
                "empresa": "Coden App",
                "ocupacaoId": 1,
                "profissaoId": 1,
                "tempoEmpregoAtual": 1,
                "telefoneRH": "2732239950",
                "pisPasep": null,
                "renda": 2500,
                "outrasRenda": null,
                "tipoOutrasRenda": null
            },
            "anexo": [
                {
                    "id": null,
                    "imagemId": 87,
                    "documentoId": 1
                },
                {
                    "id": 1,
                    "imagemId": 65,
                    "documentoId": 1
                }
            ],
            "unidade": {
                "nomeVendedor": "Edson Alves",
                "cpfVendedor": "12345678910",
                "celularVendedor": "12345678910"
            },
            "historicoChamada": [
                {
                    "id": 20,
                    "telefoniaId": 6,
                    "chamadaId": "1",
                    "ramal": 1,
                    "usuarioId": 1,
                    "dataHora": "2021-05-09T12:01:14",
                    "propostaId": 6,
                    "telefone": "27999999999",
                    "duracao": "09:00",
                    "situacao": 1,
                    "confirmacao": false
                }
            ]
        }
    ],
    "required": [
        "id",
        "checados",
        "situacao",
        "operacao",
        "cliente",
        "contatos",
        "endereco",
        "bancario",
        "profissional",
        "anexo",
        "unidade"
    ],
    "properties": {
        "id": {
            "$id": "#/properties/id",
            "type": "integer",
            "title": "The id schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                6
            ]
        },
        "checados": {
            "$id": "#/properties/checados",
            "type": "array",
            "title": "The checados schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [
                    1,
                    5
                ]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/checados/items",
                "anyOf": [
                    {
                        "$id": "#/properties/checados/items/anyOf/0",
                        "type": "integer",
                        "title": "The first anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            1,
                            5
                        ]
                    }
                ]
            }
        },
        "situacao": {
            "$id": "#/properties/situacao",
            "type": "object",
            "title": "The situacao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": {},
            "examples": [
                {
                    "etapaId": 1,
                    "decisaoId": null,
                    "motivos": [
                        1,
                        5,
                        75,
                        23,
                        65
                    ]
                }
            ],
            "required": [
                "etapaId",
                "decisaoId",
                "motivos"
            ],
            "properties": {
                "etapaId": {
                    "$id": "#/properties/situacao/properties/etapaId",
                    "type": "integer",
                    "title": "The etapaId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1
                    ]
                },
                "decisaoId": {
                    "$id": "#/properties/situacao/properties/decisaoId",
                    "type": "null",
                    "title": "The decisaoId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": null,
                    "examples": [
                        null
                    ]
                },
                "motivos": {
                    "$id": "#/properties/situacao/properties/motivos",
                    "type": "array",
                    "title": "The motivos schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": [],
                    "examples": [
                        [
                            1,
                            5
                        ]
                    ],
                    "additionalItems": true,
                    "items": {
                        "$id": "#/properties/situacao/properties/motivos/items",
                        "anyOf": [
                            {
                                "$id": "#/properties/situacao/properties/motivos/items/anyOf/0",
                                "type": "integer",
                                "title": "The first anyOf schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1,
                                    5
                                ]
                            }
                        ]
                    }
                }
            },
            "additionalProperties": true
        },
        "operacao": {
            "$id": "#/properties/operacao",
            "type": "object",
            "title": "The operacao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": {},
            "examples": [
                {
                    "produtoId": 1,
                    "diaRecebimento": 1,
                    "vencimento": "2021-06-02",
                    "tabelaJurosId": 1,
                    "taxa": 17,
                    "operacao": 2074.96,
                    "valorContratado": 1500,
                    "prazo": 1,
                    "prestacao": 2074.96,
                    "limitePrestacao": 2075.0
                }
            ],
            "required": [
                "produtoId",
                "diaRecebimento",
                "vencimento",
                "tabelaJurosId",
                "taxa",
                "operacao",
                "valorContratado",
                "prazo",
                "prestacao",
                "limitePrestacao"
            ],
            "properties": {
                "produtoId": {
                    "$id": "#/properties/operacao/properties/produtoId",
                    "type": "integer",
                    "title": "The produtoId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1
                    ]
                },
                "diaRecebimento": {
                    "$id": "#/properties/operacao/properties/diaRecebimento",
                    "type": "integer",
                    "title": "The diaRecebimento schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1
                    ]
                },
                "vencimento": {
                    "$id": "#/properties/operacao/properties/vencimento",
                    "type": "string",
                    "title": "The vencimento schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "2021-06-02"
                    ]
                },
                "tabelaJurosId": {
                    "$id": "#/properties/operacao/properties/tabelaJurosId",
                    "type": "integer",
                    "title": "The tabelaJurosId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1
                    ]
                },
                "taxa": {
                    "$id": "#/properties/operacao/properties/taxa",
                    "type": "integer",
                    "title": "The taxa schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        17
                    ]
                },
                "operacao": {
                    "$id": "#/properties/operacao/properties/operacao",
                    "type": "number",
                    "title": "The operacao schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0.0,
                    "examples": [
                        2074.96
                    ]
                },
                "valorContratado": {
                    "$id": "#/properties/operacao/properties/valorContratado",
                    "type": "integer",
                    "title": "The valorContratado schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1500
                    ]
                },
                "prazo": {
                    "$id": "#/properties/operacao/properties/prazo",
                    "type": "integer",
                    "title": "The prazo schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1
                    ]
                },
                "prestacao": {
                    "$id": "#/properties/operacao/properties/prestacao",
                    "type": "number",
                    "title": "The prestacao schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0.0,
                    "examples": [
                        2074.96
                    ]
                },
                "limitePrestacao": {
                    "$id": "#/properties/operacao/properties/limitePrestacao",
                    "type": "number",
                    "title": "The limitePrestacao schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0.0,
                    "examples": [
                        2075.0
                    ]
                }
            },
            "additionalProperties": true
        },
        "debitosConveniada": {
            "$id": "#/properties/debitosConveniada",
            "type": "array",
            "title": "The debitosConveniada schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [
                    {
                        "id": 1,
                        "codigo": "1234567890",
                        "dataReferencia": "02/2021",
                        "dataVencimento": "20210410",
                        "valor": 180.58,
                        "tipo": 1,
                        "consumo": 91.4,
                        "situacao": 1,
                        "codigoBarras": "00190000090287490201436287135176186160000021366",
                        "comprovanteId": null,
                        "comprovante": null,
                        "descontar": true,
                        "participa": true
                    }
                ]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/debitosConveniada/items",
                "anyOf": [
                    {
                        "$id": "#/properties/debitosConveniada/items/anyOf/0",
                        "type": "object",
                        "title": "The first anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": {},
                        "examples": [
                            {
                                "id": 1,
                                "codigo": "1234567890",
                                "dataReferencia": "02/2021",
                                "dataVencimento": "20210410",
                                "valor": 180.58,
                                "tipo": 1,
                                "consumo": 91.4,
                                "situacao": 1,
                                "codigoBarras": "00190000090287490201436287135176186160000021366",
                                "comprovanteId": null,
                                "comprovante": null,
                                "descontar": true,
                                "participa": true
                            }
                        ],
                        "required": [],
                        "properties": {
                            "id": {
                                "$id": "#/properties/debitosConveniada/items/anyOf/0/properties/id",
                                "type": "integer",
                                "title": "The id schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1
                                ]
                            },
                            "codigo": {
                                "$id": "#/properties/debitosConveniada/items/anyOf/0/properties/codigo",
                                "type": "string",
                                "title": "The codigo schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "1234567890"
                                ]
                            },
                            "dataReferencia": {
                                "$id": "#/properties/debitosConveniada/items/anyOf/0/properties/dataReferencia",
                                "type": "string",
                                "title": "The dataReferencia schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "02/2021"
                                ]
                            },
                            "dataVencimento": {
                                "$id": "#/properties/debitosConveniada/items/anyOf/0/properties/dataVencimento",
                                "type": "string",
                                "title": "The dataVencimento schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "20210410"
                                ]
                            },
                            "valor": {
                                "$id": "#/properties/debitosConveniada/items/anyOf/0/properties/valor",
                                "type": "number",
                                "title": "The valor schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0.0,
                                "examples": [
                                    180.58
                                ]
                            },
                            "tipo": {
                                "$id": "#/properties/debitosConveniada/items/anyOf/0/properties/tipo",
                                "type": "integer",
                                "title": "The tipo schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1
                                ]
                            },
                            "consumo": {
                                "$id": "#/properties/debitosConveniada/items/anyOf/0/properties/consumo",
                                "type": "number",
                                "title": "The consumo schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0.0,
                                "examples": [
                                    91.4
                                ]
                            },
                            "situacao": {
                                "$id": "#/properties/debitosConveniada/items/anyOf/0/properties/situacao",
                                "type": "integer",
                                "title": "The situacao schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1
                                ]
                            },
                            "codigoBarras": {
                                "$id": "#/properties/debitosConveniada/items/anyOf/0/properties/codigoBarras",
                                "type": "string",
                                "title": "The codigoBarras schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "00190000090287490201436287135176186160000021366"
                                ]
                            },
                            "comprovanteId": {
                                "$id": "#/properties/debitosConveniada/items/anyOf/0/properties/comprovanteId",
                                "type": "null",
                                "title": "The comprovanteId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": null,
                                "examples": [
                                    null
                                ]
                            },
                            "comprovante": {
                                "$id": "#/properties/debitosConveniada/items/anyOf/0/properties/comprovante",
                                "type": "null",
                                "title": "The comprovante schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": null,
                                "examples": [
                                    null
                                ]
                            },
                            "descontar": {
                                "$id": "#/properties/debitosConveniada/items/anyOf/0/properties/descontar",
                                "type": "boolean",
                                "title": "The descontar schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": false,
                                "examples": [
                                    true
                                ]
                            },
                            "participa": {
                                "$id": "#/properties/debitosConveniada/items/anyOf/0/properties/participa",
                                "type": "boolean",
                                "title": "The participa schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": false,
                                "examples": [
                                    true
                                ]
                            }
                        },
                        "additionalProperties": true
                    }
                ]
            }
        },
        "cliente": {
            "$id": "#/properties/cliente",
            "type": "object",
            "title": "The cliente schema",
            "description": "An explanation about the purpose of this instance.",
            "default": {},
            "examples": [
                {
                    "cpf": "62073998038",
                    "nome": "Edson Alves",
                    "nascimento": "2021-02-09",
                    "rg": "6712090",
                    "rgEmissor": "SESP",
                    "rgUfId": 8,
                    "rgEmissao": "2021-03-26",
                    "sexo": 1,
                    "estadoCivil": 0,
                    "grauInstrucaoId": 1,
                    "nacionalidadeId": 1,
                    "naturalidadeCidadeId": 1906,
                    "nomeMae": "Maria Cristina Alves",
                    "nomeConjugue": null
                }
            ],
            "required": [
                "cpf",
                "nome",
                "nascimento",
                "rg",
                "rgEmissor",
                "rgUfId",
                "rgEmissao",
                "sexo",
                "estadoCivil",
                "grauInstrucaoId",
                "nacionalidadeId",
                "naturalidadeCidadeId",
                "nomeMae",
                "nomeConjugue"
            ],
            "properties": {
                "cpf": {
                    "$id": "#/properties/cliente/properties/cpf",
                    "type": "string",
                    "title": "The cpf schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "62073998038"
                    ]
                },
                "nome": {
                    "$id": "#/properties/cliente/properties/nome",
                    "type": "string",
                    "title": "The nome schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "Edson Alves"
                    ]
                },
                "nascimento": {
                    "$id": "#/properties/cliente/properties/nascimento",
                    "type": "string",
                    "title": "The nascimento schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "2021-02-09"
                    ]
                },
                "rg": {
                    "$id": "#/properties/cliente/properties/rg",
                    "type": "string",
                    "title": "The rg schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "6712090"
                    ]
                },
                "rgEmissor": {
                    "$id": "#/properties/cliente/properties/rgEmissor",
                    "type": "string",
                    "title": "The rgEmissor schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "SESP"
                    ]
                },
                "rgUfId": {
                    "$id": "#/properties/cliente/properties/rgUfId",
                    "type": "integer",
                    "title": "The rgUfId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        8
                    ]
                },
                "rgEmissao": {
                    "$id": "#/properties/cliente/properties/rgEmissao",
                    "type": "string",
                    "title": "The rgEmissao schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "2021-03-26"
                    ]
                },
                "sexo": {
                    "$id": "#/properties/cliente/properties/sexo",
                    "type": "integer",
                    "title": "The sexo schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1
                    ]
                },
                "estadoCivil": {
                    "$id": "#/properties/cliente/properties/estadoCivil",
                    "type": "integer",
                    "title": "The estadoCivil schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        0
                    ]
                },
                "grauInstrucaoId": {
                    "$id": "#/properties/cliente/properties/grauInstrucaoId",
                    "type": "integer",
                    "title": "The grauInstrucaoId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1
                    ]
                },
                "nacionalidadeId": {
                    "$id": "#/properties/cliente/properties/nacionalidadeId",
                    "type": "integer",
                    "title": "The nacionalidadeId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1
                    ]
                },
                "naturalidadeCidadeId": {
                    "$id": "#/properties/cliente/properties/naturalidadeCidadeId",
                    "type": "integer",
                    "title": "The naturalidadeCidadeId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1906
                    ]
                },
                "nomeMae": {
                    "$id": "#/properties/cliente/properties/nomeMae",
                    "type": "string",
                    "title": "The nomeMae schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "Maria Cristina Alves"
                    ]
                },
                "nomeConjugue": {
                    "$id": "#/properties/cliente/properties/nomeConjugue",
                    "type": "null",
                    "title": "The nomeConjugue schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": null,
                    "examples": [
                        null
                    ]
                }
            },
            "additionalProperties": true
        },
        "contatos": {
            "$id": "#/properties/contatos",
            "type": "object",
            "title": "The contatos schema",
            "description": "An explanation about the purpose of this instance.",
            "default": {},
            "examples": [
                {
                    "contato": {
                        "email": "<EMAIL>",
                        "telefone": "33989225953",
                        "telefoneFixo": "1284520729",
                        "telefoneExtra": null
                    },
                    "referencia": [
                        {
                            "id": 61,
                            "nome": "Daniel Vinicius Enrico Fernandes",
                            "telefone": "3325785379",
                            "grau": 0,
                            "logAlterados": []
                        },
                        {
                            "id": 62,
                            "nome": "Alexandre Renato Bernardo Jesus",
                            "telefone": "3199925634",
                            "grau": 0,
                            "logAlterados": []
                        }
                    ]
                }
            ],
            "required": [
                "contato",
                "referencia"
            ],
            "properties": {
                "contato": {
                    "$id": "#/properties/contatos/properties/contato",
                    "type": "object",
                    "title": "The contato schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": {},
                    "examples": [
                        {
                            "email": "<EMAIL>",
                            "telefone": "33989225953",
                            "telefoneFixo": "1284520729",
                            "telefoneExtra": null
                        }
                    ],
                    "required": [
                        "email",
                        "telefone",
                        "telefoneFixo",
                        "telefoneExtra"
                    ],
                    "properties": {
                        "email": {
                            "$id": "#/properties/contatos/properties/contato/properties/email",
                            "type": "string",
                            "title": "The email schema",
                            "description": "An explanation about the purpose of this instance.",
                            "default": "",
                            "examples": [
                                "<EMAIL>"
                            ]
                        },
                        "telefone": {
                            "$id": "#/properties/contatos/properties/contato/properties/telefone",
                            "type": "string",
                            "title": "The telefone schema",
                            "description": "An explanation about the purpose of this instance.",
                            "default": "",
                            "examples": [
                                "33989225953"
                            ]
                        },
                        "telefoneFixo": {
                            "$id": "#/properties/contatos/properties/contato/properties/telefoneFixo",
                            "type": "string",
                            "title": "The telefoneFixo schema",
                            "description": "An explanation about the purpose of this instance.",
                            "default": "",
                            "examples": [
                                "1284520729"
                            ]
                        },
                        "telefoneExtra": {
                            "$id": "#/properties/contatos/properties/contato/properties/telefoneExtra",
                            "type": "null",
                            "title": "The telefoneExtra schema",
                            "description": "An explanation about the purpose of this instance.",
                            "default": null,
                            "examples": [
                                null
                            ]
                        }
                    },
                    "additionalProperties": true
                },
                "referencia": {
                    "$id": "#/properties/contatos/properties/referencia",
                    "type": "array",
                    "title": "The referencia schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": [],
                    "examples": [
                        [
                            {
                                "id": 61,
                                "nome": "Daniel Vinicius Enrico Fernandes",
                                "telefone": "3325785379",
                                "grau": 0,
                                "logAlterados": []
                            },
                            {
                                "id": 62,
                                "nome": "Alexandre Renato Bernardo Jesus",
                                "telefone": "3199925634",
                                "grau": 0,
                                "logAlterados": []
                            }
                        ]
                    ],
                    "additionalItems": true,
                    "items": {
                        "$id": "#/properties/contatos/properties/referencia/items",
                        "anyOf": [
                            {
                                "$id": "#/properties/contatos/properties/referencia/items/anyOf/0",
                                "type": "object",
                                "title": "The first anyOf schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": {},
                                "examples": [
                                    {
                                        "id": 61,
                                        "nome": "Daniel Vinicius Enrico Fernandes",
                                        "telefone": "3325785379",
                                        "grau": 0,
                                        "logAlterados": []
                                    }
                                ],
                                "required": [
                                    "id",
                                    "nome",
                                    "telefone",
                                    "grau"
                                ],
                                "properties": {
                                    "id": {
                                        "$id": "#/properties/contatos/properties/referencia/items/anyOf/0/properties/id",
                                        "type": "integer",
                                        "title": "The id schema",
                                        "description": "An explanation about the purpose of this instance.",
                                        "default": 0,
                                        "examples": [
                                            61
                                        ]
                                    },
                                    "nome": {
                                        "$id": "#/properties/contatos/properties/referencia/items/anyOf/0/properties/nome",
                                        "type": "string",
                                        "title": "The nome schema",
                                        "description": "An explanation about the purpose of this instance.",
                                        "default": "",
                                        "examples": [
                                            "Daniel Vinicius Enrico Fernandes"
                                        ]
                                    },
                                    "telefone": {
                                        "$id": "#/properties/contatos/properties/referencia/items/anyOf/0/properties/telefone",
                                        "type": "string",
                                        "title": "The telefone schema",
                                        "description": "An explanation about the purpose of this instance.",
                                        "default": "",
                                        "examples": [
                                            "3325785379"
                                        ]
                                    },
                                    "grau": {
                                        "$id": "#/properties/contatos/properties/referencia/items/anyOf/0/properties/grau",
                                        "type": "integer",
                                        "title": "The grau schema",
                                        "description": "An explanation about the purpose of this instance.",
                                        "default": 0,
                                        "examples": [
                                            0
                                        ]
                                    },
                                    "logAlterados": {
                                        "$id": "#/properties/contatos/properties/referencia/items/anyOf/0/properties/logAlterados",
                                        "type": "array",
                                        "title": "The logAlterados schema",
                                        "description": "An explanation about the purpose of this instance.",
                                        "default": [],
                                        "examples": [
                                            []
                                        ],
                                        "additionalItems": true,
                                        "items": {
                                            "$id": "#/properties/contatos/properties/referencia/items/anyOf/0/properties/logAlterados/items"
                                        }
                                    }
                                },
                                "additionalProperties": true
                            }
                        ]
                    }
                }
            },
            "additionalProperties": true
        },
        "endereco": {
            "$id": "#/properties/endereco",
            "type": "object",
            "title": "The endereco schema",
            "description": "An explanation about the purpose of this instance.",
            "default": {},
            "examples": [
                {
                    "cep": "29090640",
                    "logradouro": "Av. Vitória",
                    "numero": 1012,
                    "cidadeId": 1906,
                    "bairro": "Bento Ferreira",
                    "complemento": null
                }
            ],
            "required": [
                "cep",
                "logradouro",
                "numero",
                "cidadeId",
                "bairro"
            ],
            "properties": {
                "cep": {
                    "$id": "#/properties/endereco/properties/cep",
                    "type": "string",
                    "title": "The cep schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "29090640"
                    ]
                },
                "logradouro": {
                    "$id": "#/properties/endereco/properties/logradouro",
                    "type": "string",
                    "title": "The logradouro schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "Av. Vitória"
                    ]
                },
                "numero": {
                    "$id": "#/properties/endereco/properties/numero",
                    "type": "integer",
                    "title": "The numero schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1012
                    ]
                },
                "cidadeId": {
                    "$id": "#/properties/endereco/properties/cidadeId",
                    "type": "integer",
                    "title": "The cidadeId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1906
                    ]
                },
                "bairro": {
                    "$id": "#/properties/endereco/properties/bairro",
                    "type": "string",
                    "title": "The bairro schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "Bento Ferreira"
                    ]
                },
                "complemento": {
                    "$id": "#/properties/endereco/properties/complemento",
                    "type": "null",
                    "title": "The complemento schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": null,
                    "examples": [
                        null
                    ]
                }
            },
            "additionalProperties": true
        },
        "bancario": {
            "$id": "#/properties/bancario",
            "type": "object",
            "title": "The bancario schema",
            "description": "An explanation about the purpose of this instance.",
            "default": {},
            "examples": [
                {
                    "bancoId": null,
                    "agencia": 1,
                    "numero": "828168-5",
                    "conta": 1,
                    "tipoConta": 1,
                    "tipoOperacao": null,
                    "tempoConta": 5
                }
            ],
            "required": [
                "bancoId",
                "agencia",
                "numero",
                "conta",
                "tipoConta",
                "tipoOperacao",
                "tempoConta"
            ],
            "properties": {
                "bancoId": {
                    "$id": "#/properties/bancario/properties/bancoId",
                    "type": "null",
                    "title": "The bancoId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": null,
                    "examples": [
                        null
                    ]
                },
                "agencia": {
                    "$id": "#/properties/bancario/properties/agencia",
                    "type": "integer",
                    "title": "The agencia schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1
                    ]
                },
                "numero": {
                    "$id": "#/properties/bancario/properties/numero",
                    "type": "string",
                    "title": "The numero schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "828168-5"
                    ]
                },
                "conta": {
                    "$id": "#/properties/bancario/properties/conta",
                    "type": "integer",
                    "title": "The conta schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1
                    ]
                },
                "tipoConta": {
                    "$id": "#/properties/bancario/properties/tipoConta",
                    "type": "integer",
                    "title": "The tipoConta schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1
                    ]
                },
                "tipoOperacao": {
                    "$id": "#/properties/bancario/properties/tipoOperacao",
                    "type": "null",
                    "title": "The tipoOperacao schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": null,
                    "examples": [
                        null
                    ]
                },
                "tempoConta": {
                    "$id": "#/properties/bancario/properties/tempoConta",
                    "type": "integer",
                    "title": "The tempoConta schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        5
                    ]
                }
            },
            "additionalProperties": true
        },
        "profissional": {
            "$id": "#/properties/profissional",
            "type": "object",
            "title": "The profissional schema",
            "description": "An explanation about the purpose of this instance.",
            "default": {},
            "examples": [
                {
                    "empresa": "Coden App",
                    "ocupacaoId": 1,
                    "profissaoId": 1,
                    "tempoEmpregoAtual": 1,
                    "telefoneRH": "2732239950",
                    "pisPasep": null,
                    "renda": 2500,
                    "outrasRenda": null,
                    "tipoOutrasRenda": null
                }
            ],
            "required": [
                "empresa",
                "ocupacaoId",
                "profissaoId",
                "tempoEmpregoAtual",
                "telefoneRH",
                "pisPasep",
                "renda",
                "outrasRenda",
                "tipoOutrasRenda"
            ],
            "properties": {
                "empresa": {
                    "$id": "#/properties/profissional/properties/empresa",
                    "type": "string",
                    "title": "The empresa schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "Coden App"
                    ]
                },
                "ocupacaoId": {
                    "$id": "#/properties/profissional/properties/ocupacaoId",
                    "type": "integer",
                    "title": "The ocupacaoId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1
                    ]
                },
                "profissaoId": {
                    "$id": "#/properties/profissional/properties/profissaoId",
                    "type": "integer",
                    "title": "The profissaoId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1
                    ]
                },
                "tempoEmpregoAtual": {
                    "$id": "#/properties/profissional/properties/tempoEmpregoAtual",
                    "type": "integer",
                    "title": "The tempoEmpregoAtual schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1
                    ]
                },
                "telefoneRH": {
                    "$id": "#/properties/profissional/properties/telefoneRH",
                    "type": "string",
                    "title": "The telefoneRH schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "2732239950"
                    ]
                },
                "pisPasep": {
                    "$id": "#/properties/profissional/properties/pisPasep",
                    "type": "null",
                    "title": "The pisPasep schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": null,
                    "examples": [
                        null
                    ]
                },
                "renda": {
                    "$id": "#/properties/profissional/properties/renda",
                    "type": "integer",
                    "title": "The renda schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        2500
                    ]
                },
                "outrasRenda": {
                    "$id": "#/properties/profissional/properties/outrasRenda",
                    "type": "null",
                    "title": "The outrasRenda schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": null,
                    "examples": [
                        null
                    ]
                },
                "tipoOutrasRenda": {
                    "$id": "#/properties/profissional/properties/tipoOutrasRenda",
                    "type": "null",
                    "title": "The tipoOutrasRenda schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": null,
                    "examples": [
                        null
                    ]
                }
            },
            "additionalProperties": true
        },
        "anexo": {
            "$id": "#/properties/anexo",
            "type": "array",
            "title": "The anexo schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [
                    {
                        "id": null,
                        "imagemId": 87,
                        "documentoId": 1
                    },
                    {
                        "id": 1,
                        "imagemId": 65,
                        "documentoId": 1
                    }
                ]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/anexo/items",
                "anyOf": [
                    {
                        "$id": "#/properties/anexo/items/anyOf/0",
                        "type": "object",
                        "title": "The first anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": {},
                        "examples": [
                            {
                                "id": null,
                                "imagemId": 87,
                                "documentoId": 1
                            }
                        ],
                        "required": [
                            "id",
                            "imagemId",
                            "documentoId"
                        ],
                        "properties": {
                            "id": {
                                "$id": "#/properties/anexo/items/anyOf/0/properties/id",
                                "type": "null",
                                "title": "The id schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": null,
                                "examples": [
                                    null
                                ]
                            },
                            "imagemId": {
                                "$id": "#/properties/anexo/items/anyOf/0/properties/imagemId",
                                "type": "integer",
                                "title": "The imagemId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    87
                                ]
                            },
                            "documentoId": {
                                "$id": "#/properties/anexo/items/anyOf/0/properties/documentoId",
                                "type": "integer",
                                "title": "The documentoId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1
                                ]
                            }
                        },
                        "additionalProperties": true
                    }
                ]
            }
        },
        "unidade": {
            "$id": "#/properties/unidade",
            "type": "object",
            "title": "The unidade schema",
            "description": "An explanation about the purpose of this instance.",
            "default": {},
            "examples": [
                {
                    "nomeVendedor": "Edson Alves",
                    "cpfVendedor": "12345678910",
                    "celularVendedor": "12345678910"
                }
            ],
            "required": [
                "nomeVendedor",
                "cpfVendedor",
                "celularVendedor"
            ],
            "properties": {
                "nomeVendedor": {
                    "$id": "#/properties/unidade/properties/nomeVendedor",
                    "type": "string",
                    "title": "The nomeVendedor schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "Edson Alves"
                    ]
                },
                "cpfVendedor": {
                    "$id": "#/properties/unidade/properties/cpfVendedor",
                    "type": "string",
                    "title": "The cpfVendedor schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "12345678910"
                    ]
                },
                "celularVendedor": {
                    "$id": "#/properties/unidade/properties/celularVendedor",
                    "type": "string",
                    "title": "The celularVendedor schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "12345678910"
                    ]
                }
            },
            "additionalProperties": true
        },
        "historicoChamada": {
            "$id": "#/properties/historicoChamada",
            "type": "array",
            "title": "The historicoChamada schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [
                    {
                        "id": 20,
                        "telefoniaId": 6,
                        "chamadaId": "1",
                        "ramal": 1,
                        "usuarioId": 1,
                        "dataHora": "2021-05-09T12:01:14",
                        "propostaId": 6,
                        "telefone": "27999999999",
                        "duracao": "09:00",
                        "situacao": 1,
                        "confirmacao": false
                    }
                ]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/historicoChamada/items",
                "anyOf": [
                    {
                        "$id": "#/properties/historicoChamada/items/anyOf/0",
                        "type": "object",
                        "title": "The first anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": {},
                        "examples": [
                            {
                                "id": 20,
                                "telefoniaId": 6,
                                "chamadaId": "1",
                                "ramal": 1,
                                "usuarioId": 1,
                                "dataHora": "2021-05-09T12:01:14",
                                "propostaId": 6,
                                "telefone": "27999999999",
                                "duracao": "09:00",
                                "situacao": 1,
                                "confirmacao": false
                            }
                        ],
                        "required": [],
                        "properties": {
                            "id": {
                                "$id": "#/properties/historicoChamada/items/anyOf/0/properties/id",
                                "type": "integer",
                                "title": "The id schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    20
                                ]
                            },
                            "telefoniaId": {
                                "$id": "#/properties/historicoChamada/items/anyOf/0/properties/telefoniaId",
                                "type": "integer",
                                "title": "The telefoniaId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    6
                                ]
                            },
                            "chamadaId": {
                                "$id": "#/properties/historicoChamada/items/anyOf/0/properties/chamadaId",
                                "type": "string",
                                "title": "The chamadaId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "1"
                                ]
                            },
                            "ramal": {
                                "$id": "#/properties/historicoChamada/items/anyOf/0/properties/ramal",
                                "type": "integer",
                                "title": "The ramal schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1
                                ]
                            },
                            "usuarioId": {
                                "$id": "#/properties/historicoChamada/items/anyOf/0/properties/usuarioId",
                                "type": "integer",
                                "title": "The usuarioId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1
                                ]
                            },
                            "dataHora": {
                                "$id": "#/properties/historicoChamada/items/anyOf/0/properties/dataHora",
                                "type": "string",
                                "title": "The dataHora schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "2021-05-09T12:01:14"
                                ]
                            },
                            "propostaId": {
                                "$id": "#/properties/historicoChamada/items/anyOf/0/properties/propostaId",
                                "type": "integer",
                                "title": "The propostaId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    6
                                ]
                            },
                            "telefone": {
                                "$id": "#/properties/historicoChamada/items/anyOf/0/properties/telefone",
                                "type": "string",
                                "title": "The telefone schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "27999999999"
                                ]
                            },
                            "duracao": {
                                "$id": "#/properties/historicoChamada/items/anyOf/0/properties/duracao",
                                "type": "string",
                                "title": "The duracao schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "09:00"
                                ]
                            },
                            "situacao": {
                                "$id": "#/properties/historicoChamada/items/anyOf/0/properties/situacao",
                                "type": "integer",
                                "title": "The situacao schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1
                                ]
                            },
                            "confirmacao": {
                                "$id": "#/properties/historicoChamada/items/anyOf/0/properties/confirmacao",
                                "type": "boolean",
                                "title": "The confirmacao schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": false,
                                "examples": [
                                    false
                                ]
                            }
                        },
                        "additionalProperties": true
                    }
                ]
            }
        }
    },
    "additionalProperties": true
}'
where method = 'PUT'
and route = '/api/proposta/mesa-de-credito/{id}'
