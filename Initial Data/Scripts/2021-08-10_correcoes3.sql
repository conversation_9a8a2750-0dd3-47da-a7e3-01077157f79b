DELETE DocumentoTipo where Id >= 10

DECLARE @documentoTipoId INT
set @documentoTipoId = (select Id from DocumentoTipo where Tipo = 'autorizacao-debito-fatura-energia')

update Documento 
set DocumentoTipoId = @documentoTipoId
where Nome = 'TERMO DE AUTORIZAÇÃO DE COBRANÇA'

-----

set @documentoTipoId = (select Id from DocumentoTipo where Tipo = 'autorizacao-destinacao-valores')

update Documento 
set DocumentoTipoId = @documentoTipoId
where Nome = 'TERMO DE DESTINAÇÃO DE VALORES'


DECLARE @luzEmDiaId INT, @energiaId INT
set @luzEmDiaId = (select Id from Produto where Nome = 'Luz em Dia')

set @energiaId = (select Id from Produto where Nome = 'Energia')

insert into ProdutoDocumento
select @luzEmDiaId, DocumentoId, Obrigatorio, impressao, EtapaId
from ProdutoDocumento where ProdutoId = @energiaId

DECLARE @documentoId INT
set @documentoId = (select id from Documento where Nome = 'TERMO DE DESTINAÇÃO DE VALORES')

insert into ProdutoDocumento 
VALUES (@luzEmDiaId, @documentoId, 1, 1, 1)
