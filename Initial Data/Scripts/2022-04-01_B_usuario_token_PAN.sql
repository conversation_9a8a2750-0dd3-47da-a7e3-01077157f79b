declare @usuarioId int
insert into Seguranca.Usuario ([Login], Nome) values ('pan.integracao','Integração com banco Pan')
set @usuarioId = @@IDENTITY

insert into Seguranca.PerfilUsuario
select Id, @usuarioId from Seguranca.Perfil where Nome = 'Integração'

insert into Seguranca.IntegracaoToken (Codigo,nome, Token, UsuarioId) values ('PAN','Banco Pan','eyJMb2dpbiI6ImVkc29uLmFsdmVzIiwiVXNlcklkIjoiMiIsIm5iZiI6MTY0ODgzODUxNiwiZXhwIjoxNjQ4ODgxNzE2LCJpYXQiOjE2NDg4Mzg1MTZ9.BZwTPQFZilXx8uQMBHtpGzfp8Cif2Vw0XZ9WDE4-FsU',@usuarioId)