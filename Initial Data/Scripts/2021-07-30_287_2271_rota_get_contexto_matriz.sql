BEGIN

    --id do controler
    --não necessario inserir valor

    declare @controllerId INT

    declare @controllername varchar(50) = 'Comissao',
            @controllerUser varchar(50) = 'crefazapisqlstag',
            @controllerdesc varchar(max) = 'Retorna lista de Matriz',
            @routname varchar(255) = 'Lista de Matriz',
            @routedesc varchar(max) = 'Listagem de Matriz',
            @route varchar(100) = '/api/comissao/relatorio/matriz',
            @routemethod varchar(10) = 'GET',
            @routeprocedure varchar(255) = 'controller.stpComissao',
            @routeinput varchar(max) = '{}',
            @routeoutput varchar(max) = '{}',
            @routeresponseid int = 2

            select @controllerId = Id from api.controller where [name] = @controllername

            select @controllerId

            IF @controllerId is null
            BEGIN
                insert into api.controller([user],[name],[description])
                values(
                    @controllerUser,
                    @controllername,
                    @controllerdesc
                )

                set @controllerId = @@IDENTITY
            END

            insert into api.route
            (
                controller_id,
                [name],
                [description],
                [route],
                [method],
                [procedure],
                [input],
                [output],
                [response_type_id]
            ) 
            VALUES(
                @controllerId,
                @routname,
                @routedesc,
                @route,
                @routemethod,
                @routeprocedure,
                @routeinput,
                @routeoutput,
                @routeresponseid
            )

END