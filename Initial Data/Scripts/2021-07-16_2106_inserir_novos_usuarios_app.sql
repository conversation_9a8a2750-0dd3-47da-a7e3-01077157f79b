DECLARE @senha varchar(max)
declare @pessoa as table(
    id int   
)

BEGIN TRAN
  
    -------------
    insert into dbo.pessoa(Nome,Ativo,PrimeiraOperacao,UltimaOperacao,Cadastro,PessoaJuridica) 
    output inserted.id into @pessoa
    VALUES ('TOTO WOLFF',1,null,null,dbo.getdatebr(),0)

    insert into dbo.Endereco(PessoaId,CEP,UFId,CidadeId,Bairro,Logradouro,Numero,Complemento,TempoResidencia)
    select  a.id,b.<PERSON>P,b.<PERSON>d,b.<PERSON>de<PERSON>d,b.<PERSON>,b.<PERSON>,b.<PERSON>,b.<PERSON>,b.<PERSON>
    from @pessoa a
    join dbo.Endereco b on b.PessoaId=1

    insert into dbo.PessoaFisica(PessoaId,OcupacaoId,PaisId,CPF,Documento,DocumentoEmissor,DocumentoUFId,DocumentoEmissao,PIS,NaturalidadeUFId,NaturalidadeCidadeId,NomeMae,Nascimento,sexo,GrauInstrucaoId,pep,EstadoCivil,TelefoneFixo,TelefoneCelular,email,NomeConjuge)
    select a.id,OcupacaoId,PaisId,'03143184701',Documento,DocumentoEmissor,DocumentoUFId,DocumentoEmissao,PIS,NaturalidadeUFId,NaturalidadeCidadeId,NomeMae,'1978-03-30',sexo,GrauInstrucaoId,pep,EstadoCivil,TelefoneFixo,TelefoneCelular,email,NomeConjuge
    from dbo.Pessoa a
    join dbo.pessoafisica b on b.pessoaid = 1
    where a.id = (select id from @pessoa)

    insert into dbo.Contato(PessoaId,Funcao,Email,Telefone)
    select a.id,b.Funcao,b.Email,b.Telefone 
    from @pessoa a
    join dbo.Contato b on b.Pessoaid = 14
    where a.id = (select id from @pessoa)

    insert into dbo.Renda(PessoaId,ProfissaoId,OcupacaoId,Empresa,Renda,Admissao,OutrasRendas,TipoOutrasRendas,TempoEmprego,TelefoneRH,TipoRenda,DiaRecebimento)
    select a.id,b.ProfissaoId,b.OcupacaoId,b.Empresa,b.Renda,b.Admissao,b.OutrasRendas,TipoOutrasRendas,b.TempoEmprego,b.TelefoneRH,b.TipoRenda,b.DiaRecebimento
    from @pessoa a
    join dbo.Renda b on b.PessoaId=3
    where a.id = (select id from @pessoa)

    insert into dbo.Referencia(PessoaId,Nome,Telefone,Grau)
    select a.id,Nome,Telefone,Grau
    from  @pessoa a
    join dbo.Referencia b on b.PessoaId=2
    where a.id = (select id from @pessoa)

    delete from @pessoa


    ------------
    insert into dbo.pessoa(Nome,Ativo,PrimeiraOperacao,UltimaOperacao,Cadastro,PessoaJuridica) 
    output inserted.id into @pessoa
    values('CHRISTIAN HORNER',1,null,null,dbo.getdatebr(),0)

    insert into dbo.Endereco(PessoaId,CEP,UFId,CidadeId,Bairro,Logradouro,Numero,Complemento,TempoResidencia)
    select  a.id,b.CEP,b.UFId,b.CidadeId,b.Bairro,b.Logradouro,b.Numero,b.Complemento,b.TempoResidencia
    from @pessoa a
    join dbo.Endereco b on b.PessoaId=2

    insert into dbo.PessoaFisica(PessoaId,OcupacaoId,PaisId,CPF,Documento,DocumentoEmissor,DocumentoUFId,DocumentoEmissao,PIS,NaturalidadeUFId,NaturalidadeCidadeId,NomeMae,Nascimento,sexo,GrauInstrucaoid,pep,EstadoCivil,TelefoneFixo,TelefoneCelular,email,NomeConjuge)
    select a.id,OcupacaoId,PaisId,'06819337477',Documento,DocumentoEmissor,DocumentoUFId,DocumentoEmissao,PIS,NaturalidadeUFId,NaturalidadeCidadeId,NomeMae,'1988-05-31',sexo,GrauInstrucaoID,pep,EstadoCivil,TelefoneFixo,TelefoneCelular,email,NomeConjuge
    from dbo.Pessoa a
    join dbo.pessoafisica b on b.pessoaid = 2
    where a.id = (select id from @pessoa)

    insert into dbo.Contato(PessoaId,Funcao,Email,Telefone)
    select a.id,b.Funcao,b.Email,b.Telefone 
    from @pessoa a
    join dbo.Contato b on b.Pessoaid = 13
    where a.id = (select id from @pessoa)

    insert into dbo.Renda(PessoaId,ProfissaoId,OcupacaoId,Empresa,Renda,Admissao,OutrasRendas,TipoOutrasRendas,TempoEmprego,TelefoneRH,TipoRenda,DiaRecebimento)
    values((select id from @pessoa),2,1,'Bebidas S.A.',2550.79,'2015-05-02',0,'Nenhum',4,'2734561234',null,null)

    insert into dbo.Referencia(PessoaId,Nome,Telefone,Grau)
    select a.id,Nome,Telefone,Grau
    from  @pessoa a
    join dbo.Referencia b on b.PessoaId=4
    where a.id = (select id from @pessoa)

    delete from @pessoa



    set @senha = convert(varchar(max), HASHBYTES('SHA2_256', '123456'), 1)

    insert into Seguranca.Usuario(PessoaId,login,Senha,CorrespondenteId,UnidadeId) values
    ((select top 1 PessoaId from PessoaFisica where cpf = '03143184701' order by id desc),'03143184701',@senha,null,null),
    ((select top 1 PessoaId from PessoaFisica where cpf = '06819337477' order by id desc),'06819337477',@senha,null,null)

COMMIT
