-- alteração regex

--1.	ENEL SP
--1.1.	N° da instalação (Campo chave) -> REGEX: ^([0-9]{9})$ 
update b
set Formato = '[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d]'
from Convenio a join ConvenioDados b on a.Id = b.ConvenioId 
where a.Nome = 'ENEL SP' and b.Nome = 'N° da instalação'

--2.	ENEL CE
--2.1.	N° do cliente (Campo chave) -> REGEX: ^([0-9]{8})$ 
update b
set Formato = '[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d]'
from Convenio a join ConvenioDados b on a.Id = b.ConvenioId 
where a.Nome = 'ENEL CE' and b.Nome = 'N° do cliente'

--2.2.	Rota -> REGEX: ^([[^A-Z0-9]{10})$
update b
set Formato = '[A-Z\d],[A-Z\d],[A-Z\d],[A-Z\d],[A-Z\d],[A-Z\d],[A-Z\d],[A-Z\d]," ","-"," ",[A-Z\d],[A-Z\d]'
from Convenio a join ConvenioDados b on a.Id = b.ConvenioId 
where a.Nome = 'ENEL CE' and b.Nome = 'Rota'


--3.	ENEL RJ
--3.1.	N° da instalação (Campo chave) -> REGEX: ^([0-9]{8})$
update b
set Formato = '[\d],[\d],[\d],[\d],[\d],[\d],[\d]," ","-"," ",[\d]'
from Convenio a join ConvenioDados b on a.Id = b.ConvenioId 
where a.Nome = 'ENEL RJ' and b.Nome = 'N° da instalação'

--3.2.	N° do cliente -> REGEX: ^([0-9]{8})$
update b
set Formato = '[\d],[\d],[\d],[\d],[\d],[\d],[\d]," ","-"," ",[\d]'
from Convenio a join ConvenioDados b on a.Id = b.ConvenioId 
where a.Nome = 'ENEL RJ' and b.Nome = 'N° do cliente'

--3.3.	Rota -> REGEX: ^([0-9]{16})$
update b
set Formato = '[\d],[\d],[\d],[\d],[\d],[\d],[\d]," ",[\d],[\d]," ",[\d],[\d],[\d],[\d],[\d],[\d]," ","-"," ",[\d]'
from Convenio a join ConvenioDados b on a.Id = b.ConvenioId 
where a.Nome = 'ENEL RJ' and b.Nome = 'Rota'


--4.	ENEL GO
--4.1.	N° da instalação (Campo chave) -> REGEX: ^([0-9]{11})$
update b
set Formato = '[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d]'
from Convenio a join ConvenioDados b on a.Id = b.ConvenioId 
where a.Nome = 'ENEL GO' and b.Nome = 'N° da instalação'


--5.	COELBA
--5.1.	Conta Contrato (Campo chave) -> REGEX: ^([0-9]{12})$
update b
set Formato = '[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d]'
from Convenio a join ConvenioDados b on a.Id = b.ConvenioId 
where a.Nome = 'COELBA' and b.Nome = 'Conta Contrato'


--6.	CPFL
--6.1.	Seu Código (Campo chave) -> REGEX: ^([0-9]{8})$
update b
set Formato = '[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d]'
from Convenio a join ConvenioDados b on a.Id = b.ConvenioId 
where a.Nome = 'CPFL' and b.Nome = 'Seu Código'

--6.2.	PN -> REGEX: ^([0-9]{9})$
update b
set Formato = '[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d]'
from Convenio a join ConvenioDados b on a.Id = b.ConvenioId 
where a.Nome = 'CPFL' and b.Nome = 'PN'

--6.3.	Lote -> REGEX: ^([0-9]{2})$
update b
set Formato = '[\d],[\d]'
from Convenio a join ConvenioDados b on a.Id = b.ConvenioId 
where a.Nome = 'CPFL' and b.Nome = 'Lote'


--7.	CELESC
--7.1.	Unidade Consumidora (Campo chave) -> REGEX: ^([0-9]{8})$
update b
set Formato = '[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d]'
from Convenio a join ConvenioDados b on a.Id = b.ConvenioId 
where a.Nome = 'CELESC' and b.Nome = 'Unidade Consumidora'

--7.2.	Etapa -> REGEX: ^([0-9]{2})$
update b
set Formato = '[\d],[\d]'
from Convenio a join ConvenioDados b on a.Id = b.ConvenioId 
where a.Nome = 'CELESC' and b.Nome = 'Etapa'


--8.	COSERN
--8.1.	Conta Contrato (Campo chave) -> REGEX: ^([0-9]{12})$
update b
set Formato = '[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d]'
from Convenio a join ConvenioDados b on a.Id = b.ConvenioId 
where a.Nome = 'COSERN' and b.Nome = 'Conta Contrato'


--9.	CELPE
--9.1.	Conta Contrato (Campo chave) -> REGEX: ^([0-9]{12})$
update b
set Formato = '[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d],[\d]'
from Convenio a join ConvenioDados b on a.Id = b.ConvenioId 
where a.Nome = 'CELPE' and b.Nome = 'Conta Contrato'
