update PropostaEtapa set Cor = '#B95900' where Nome = 'Em pré-Análise'
update PropostaEtapa set Cor = '#B95900' where Nome = 'Seleção Oferta'
update PropostaEtapa set Cor = '#B95900' where Nome = 'Digitando'
update PropostaEtapa set Cor = '#B95900' where Nome = 'Pendente Análise'

update PropostaEtapa set Cor = '#BC80EB' where Nome = 'Aguardando Análise'
update PropostaEtapa set Cor = '#BC80EB' where Nome = 'Em Análise'
update PropostaEtapa set Cor = '#BC80EB' where Nome = 'Fila Contato'
update PropostaEtapa set Cor = '#BC80EB' where Nome = 'Em Contato'
update PropostaEtapa set Cor = '#BC80EB' where Nome = 'Pendente Contato'
update PropostaEtapa set Cor = '#BC80EB' where Nome = 'Aguardando validação'
update PropostaEtapa set Cor = '#BC80EB' where Nome = 'Análise validação'
update PropostaEtapa set Cor = '#BC80EB' where Nome = 'Pendente validação'
update PropostaEtapa set Cor = '#BC80EB' where Nome = 'Fila Pagamento'
update PropostaEtapa set Cor = '#BC80EB' where Nome = 'Pago ao Cliente'

update PropostaEtapa set Cor = '#46AFBD' where Nome = 'Aguardando assinatura'
update PropostaEtapa set Cor = '#BF4C84' where Nome = 'Análise Promotora'

update PropostaDecisao set Cor = '#ED5565' where Cor = '#FF8F8F'
update PropostaDecisao set Cor = '#667788' where Cor = '#F8AC59'