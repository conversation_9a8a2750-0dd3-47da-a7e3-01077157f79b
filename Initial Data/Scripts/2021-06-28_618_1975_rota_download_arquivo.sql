
    declare @controllerId int
    declare @controllerName varchar(50) = 'Imagem',
            @controllerUser varchar(50) ,
            @controllerDesc varchar(MAX),
            @routeName varchar(255) = 'Download arquivo',
            @routeDesc varchar(MAX) = 'Restorna stream de bytes para download',
            @route varchar(100) = '/api/imagem/proposta/download/{propostaImagemId}',
            @routeMethod varchar(10) = 'GET',
            @routeProcedure varchar(255) = 'controller.stpCarregaImagem',
            @routeInput varchar(MAX) = '{}',
            @routeOutput varchar(MAX) = '{}',
            @routeResponseId int = 5

    select @controllerId = Id from api.controller where [name] = @controllerName

    if (@controllerId IS NULL)
    BEGIN
        insert into api.controller
        (
            [user],
            [name],
            [description]
        )
        VALUES
        (
            @controllerUser,
            @controllerName,
            @controllerDesc
        )

        set @controllerId = @@IDENTITY
    END

    insert into api.route
    (
        [controller_id],
        [name],
        [description],
        [route],
        [method],
        [procedure],
        [input],
        [output],
        [response_type_id]
    ) values (
        @controllerId,
        @routeName,
        @routeDesc,
        @route,
        @routeMethod,
        @routeProcedure,
        @routeInput,
        @routeOutput,
        @routeResponseId
    )
