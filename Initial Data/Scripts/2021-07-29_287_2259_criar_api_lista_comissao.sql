


delete from api.route where [route] = '/api/comissao/comissao-lista'

insert into api.route([controller_id],[name],[description],[route],[method],[procedure],[input],[output],[response_type_id])
select  [controller_id],'<PERSON><PERSON><PERSON><PERSON><PERSON>','Retorna lista de Comissões','/api/comissao/comissao-lista','POST','controller.stpComissao',
'{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "pagina": 1,
            "quantidadePorPagina": 10,
            "filtroComissaoPaga": "não pago",
            "filtroProduto": 0,
            "cpf": "00000000000",
            "codigoOperacao": "546",
            "matriz": "<PERSON>re<PERSON>z",
            "filtroDataPagamentoInicial": "2021-03-02T11:12:54.830",
            "filtroDataPagamentoFinal": "2021-03-02T11:12:54.830",
            "filtroDataComissaoInicial": "2021-03-02T11:12:54.830",
            "filtroDataComissaoFinal": "2021-03-02T11:12:54.830",
            "ordenacao": "id",
            "ordenacaoAsc": true
        }
    ],
    "required": [
        "pagina",
        "quantidadePorPagina",
        "ordenacao",
        "ordenacaoAsc"
    ],
    "properties": {
        "pagina": {
            "$id": "#/properties/pagina",
            "type": "integer",
            "title": "The pagina schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "quantidadePorPagina": {
            "$id": "#/properties/quantidadePorPagina",
            "type": "integer",
            "title": "The quantidadePorPagina schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                10
            ]
        },
        "filtroComissaoPaga": {
            "$id": "#/properties/filtroComissaoPaga",
            "type": "string",
            "title": "The filtroComissaoPaga schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "não pago"
            ]
        },
        "filtroProduto": {
            "$id": "#/properties/filtroProduto",
            "type": "integer",
            "title": "The filtroProduto schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                0
            ]
        },
        "cpf": {
            "$id": "#/properties/cpf",
            "type": "string",
            "title": "The cpf schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "00000000000"
            ]
        },
        "codigoOperacao": {
            "$id": "#/properties/codigoOperacao",
            "type": "string",
            "title": "The codigoOperacao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "546"
            ]
        },
        "matriz": {
            "$id": "#/properties/matriz",
            "type": "string",
            "title": "The matriz schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "Crefaz"
            ]
        },
        "filtroDataPagamentoInicial": {
            "$id": "#/properties/filtroDataPagamentoInicial",
            "type": "string",
            "title": "The filtroDataPagamentoInicial schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-03-02T11:12:54.830"
            ]
        },
        "filtroDataPagamentoFinal": {
            "$id": "#/properties/filtroDataPagamentoFinal",
            "type": "string",
            "title": "The filtroDataPagamentoFinal schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-03-02T11:12:54.830"
            ]
        },
        "filtroDataComissaoInicial": {
            "$id": "#/properties/filtroDataComissaoInicial",
            "type": "string",
            "title": "The filtroDataComissaoInicial schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-03-02T11:12:54.830"
            ]
        },
        "filtroDataComissaoFinal": {
            "$id": "#/properties/filtroDataComissaoFinal",
            "type": "string",
            "title": "The filtroDataComissaoFinal schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-03-02T11:12:54.830"
            ]
        },
        "ordenacao": {
            "$id": "#/properties/ordenacao",
            "type": "string",
            "title": "The ordenacao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "id"
            ]
        },
        "ordenacaoAsc": {
            "$id": "#/properties/ordenacaoAsc",
            "type": "boolean",
            "title": "The ordenacaoAsc schema",
            "description": "An explanation about the purpose of this instance.",
            "default": false,
            "examples": [
                true
            ]
        }
    },
    "additionalProperties": true
}'
,
'{}',2
from api.route
where [route] = '/api/relatorio-comissoes/notas'
and [method] = 'POST'




