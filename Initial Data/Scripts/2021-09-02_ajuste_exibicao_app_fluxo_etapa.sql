update PropostaEtapa
set ExibicaoAPP = 'aguard. validação'
where No<PERSON> in ('Aguard. Validação', 'An<PERSON><PERSON>e Validação', 'Validação Pendente')

----

DECLARE @PropostaEtapaAtualId int, @PropostaProximaEtapaId INT

select @PropostaEtapaAtualId = id from PropostaEtapa where Nome = 'Aguard. Assinatura'

select @PropostaProximaEtapaId = id from PropostaEtapa where Nome = 'Fila Pagamento'

insert into PropostaEtapaFluxo 
VALUES (@PropostaEtapaAtualId, @PropostaProximaEtapaId, null, null, null)