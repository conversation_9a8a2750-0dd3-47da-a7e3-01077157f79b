
create table Blocklist (
    Id int identity(1,1) not null,
    <PERSON>lusao datetime not null,
    UsuarioIdInclusao int not null,
    MotivoId tinyint not null,
    DescricaoInclusao varchar(500),
    Exclusao datetime,
    UsuarioIdExclusao int null,
    DescricaoExclusao varchar(500)
)

alter table Blocklist add constraint PK_Blocklist primary key (Id);
alter table Blocklist add constraint FK_Blocklist_Usuario_Inclusao FOREIGN key (UsuarioIdInclusao) references Seguranca.Usuario (Id);
alter table Blocklist add constraint FK_Blocklist_Usuario_Exclusao FOREIGN key (UsuarioIdExclusao) references Seguranca.Usuario (Id);
alter table Blocklist add constraint DF_Blocklist_Inclusao DEFAULT (dbo.getdateBR())  for Inclusao;



