declare @valor varchar(max)
declare @tipo tinyint -- 1 string, 2 inteiro, 3 numero e 4 data
declare @ParametroGrupo int

insert into Parametro.ParametroGrupo values
('Motivos', 14)

set @ParametroGrupo = @@IDENTITY

set @valor =
'Lembre-se de verificar se existem informações complementares dentro da proposta.'
set @tipo  = 1
insert into Parametro.Parametro values (@ParametroGrupo,@tipo,'MSG_RETORNO_MOTIVOS','Mensagem retorna junto a lista de motivos','Mensagem que é exibida logo após a lista de motivos marcados em cima de uma Negação, Aprovação ou Cancelamento',1,@valor)
insert into Parametro.ParametroHistorico values (@@IDENTITY, dbo.getdateBR(),@valor)
