DECLARE @dicionarioDadosId INT, @propostaMotivoId INT

DECLARE @propostaEtapa as TABLE (
    id INT,
    nome VARCHAR(500)
)

select @propostaMotivoId = Id from PropostaMotivo where nome = 'Atualizar calculo conforme orientações internas na proposta'

insert into @propostaEtapa
select Id, nome from PropostaEtapa where Nome in ('Aguard. Cadastro', 'Proposta Pendente', 'Contato Pendente', 'Validação Pendente')

---
insert into DicionarioDados 
VALUES ('Nome do Documento', 'anexo.nomeDocumento', 1, null)
set @dicionarioDadosId = @@IDENTITY

insert into CamposEditaveis 
values (@dicionarioDadosId, null, null, 1, 0, 0) 

INSERT into CamposEditaveis
select @dicionarioDadosId, id, null, 0, 0, 0 from @propostaEtapa

insert into CamposEditaveis
select @dicionarioDadosId, id, @propostaMotivoId, 0, 0, 0 from @propostaEtapa where nome = 'Proposta Pendente'

-----
insert into DicionarioDados 
VALUES ('Adicionar/Remover documento', 'anexo.adicionarRemover', 1, null)
set @dicionarioDadosId = @@IDENTITY

insert into CamposEditaveis 
values (@dicionarioDadosId, null, null, 1, 0, 0) 

INSERT into CamposEditaveis
select @dicionarioDadosId, id, null, 0, 0, 0 from @propostaEtapa

insert into CamposEditaveis
select @dicionarioDadosId, id, @propostaMotivoId, 0, 0, 0 from @propostaEtapa where nome = 'Proposta Pendente'

-----
select @dicionarioDadosId = id from DicionarioDados where tag = 'operacao.tipoModalidade'

insert into CamposEditaveis 
values (@dicionarioDadosId, null, null, 1, 0, 0) 

INSERT into CamposEditaveis
select @dicionarioDadosId, id, null, 0, 0, 0 from PropostaEtapa where Nome in ('Aguard. Cadastro', 'Aguard. Análise', 'Proposta Pendente', 'Fila Contato', 'Ligando')

insert into CamposEditaveis
select @dicionarioDadosId, id, @propostaMotivoId, 0, 0, 0 from @propostaEtapa where nome = 'Proposta Pendente'