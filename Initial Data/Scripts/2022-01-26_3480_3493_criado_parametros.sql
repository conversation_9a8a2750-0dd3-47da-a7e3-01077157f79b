DECLARE @id INT

insert into Parametro.Parametro
select 21 ParametroGrupoId, 1 tipo, 'SCORE_SPC' Codigo, 'Score do Bureau SPC' Nome, 'Score retornado pelo Crivo' NotaTecnica, 1 ordem, '"Driver"."SPC BrasilV2"."Consulta de score por CPF + protocolo - PF"."Valor - Score 12 meses"' valor
set @id = @@IDENTITY

INSERT Into Parametro.ParametroHistorico
SELECT @id ParametroId, dbo.getdateBR() ValidadeInicial, '"Driver"."SPC BrasilV2"."Consulta de score por CPF + protocolo - PF"."Valor - Score 12 meses"' valor

-----------------------------

insert into Parametro.Parametro
select 21 ParametroGrupoId, 1 tipo, 'SCORE_ACP' Codigo, 'Score do Bureau ACP' Nome, 'Score retornado pelo Crivo' NotaTecnica, 1 ordem, '"Driver"."ACP"."Score Positivo - PF"."Valor - Score"' valor
set @id = @@IDENTITY

INSERT Into Parametro.ParametroHistorico
SELECT @id ParametroId, dbo.getdateBR() ValidadeInicial, '"Driver"."ACP"."Score Positivo - PF"."Valor - Score"' valor
