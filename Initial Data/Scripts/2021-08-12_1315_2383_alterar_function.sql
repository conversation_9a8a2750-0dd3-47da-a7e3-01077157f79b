ALTER function [dbo].[fncTabelaJurosValoresPrestacao] (@tabelaJurosId int, @carencia smallint, @valor numeric(15,2)) returns @result table (id int, tabelaJurosId int, valor numeric(15,2), plano tinyint, juros numeric(8,2), prestacao numeric(15,2), valorOperacao numeric(15,2))
 as
 begin
     insert into @result
     select     a.id,
             tabelaJurosId,
             isnull(@valor,valor) as valor,
             plano,
             juros,
             dbo.fncCalculaValorPrestacao(isnull(@valor,valor),@carencia,plano,juros/100.0000) as prestacao,
             dbo.fncCalculaValorPrestacao(isnull(@valor,valor),@carencia,plano,juros/100.0000)*plano as valorOperacao
         from TabelaJurosValores a with (nolock)
        join TabelaJuros b with (nolock) on (a.<PERSON><PERSON><PERSON>JurosId = b.Id)
     where (TabelaJurosId = @tabelaJurosId or @tabelaJurosId is null) and (Valor = @valor or @valor is null or b.Tipo = 0)
 return
 end