update api.[route] set [input] =
'{ 
         "$schema": "http://json-schema.org/draft-07/schema", 
         "$id": "http://example.com/example.json", 
         "type": "object", 
         "title": "The root schema", 
         "description": "The root schema comprises the entire JSON document.", 
         "default": {}, 
         "examples": [ 
             { 
                 "propostaId": 3, 
                 "faturas": [ 
                     { 
                         "situacao": 0, 
                         "tipo": 0, 
                         "descontar": true, 
                         "valorFatura": 150.55, 
                         "dataVencimento": "2021-08-23T12:07:33.560" 
                     }, 
                     { 
                         "situacao": 1, 
                         "tipo": 1, 
                         "descontar": false, 
                         "valorFatura": 150.55, 
                         "dataVencimento": "2021-08-23T12:07:33.560" 
                     } 
                 ] 
             } 
         ], 
         "required": [ 
             "propostaId", 
             "faturas" 
         ], 
         "properties": { 
             "propostaId": { 
                 "$id": "#/properties/propostaId", 
                 "type": "integer", 
                 "title": "The propostaId schema", 
                 "description": "An explanation about the purpose of this instance.", 
                 "default": 0, 
                 "examples": [ 
                     3 
                 ] 
             }, 
             "faturas": { 
                 "$id": "#/properties/faturas", 
                 "type": "array", 
                 "title": "The faturas schema", 
                 "description": "An explanation about the purpose of this instance.", 
                 "default": [], 
                 "examples": [ 
                     [ 
                         { 
                             "situacao": 0, 
                             "tipo": 0, 
                             "descontar": true, 
                             "valorFatura": 150.55, 
                             "dataVencimento": "2021-08-23T12:07:33.560" 
                         }, 
                         { 
                             "situacao": 1, 
                             "tipo": 1, 
                             "descontar": false, 
                             "valorFatura": 150.55, 
                             "dataVencimento": "2021-08-23T12:07:33.560" 
                         } 
                     ] 
                 ], 
                 "additionalItems": true, 
                 "items": { 
                     "$id": "#/properties/faturas/items", 
                     "anyOf": [ 
                         { 
                             "$id": "#/properties/faturas/items/anyOf/0", 
                             "type": "object", 
                             "title": "The first anyOf schema", 
                             "description": "An explanation about the purpose of this instance.", 
                             "default": {}, 
                             "examples": [ 
                                 { 
                                     "situacao": "pago", 
                                     "tipo": "fatura", 
                                     "descontar": true, 
                                     "valorFatura": 150.55, 
                                     "dataVencimento": "2021-08-23T12:07:33.560" 
                                 } 
                             ], 
                             "required": [ 
                                 "situacao", 
                                 "tipo", 
                                 "descontar", 
                                 "valorFatura", 
                                 "dataVencimento" 
                             ], 
                             "properties": { 
                                 "situacao": { 
                                     "$id": "#/properties/faturas/items/anyOf/0/properties/situacao", 
                                     "type": "integer", 
                                     "title": "The situacao schema", 
                                     "description": "An explanation about the purpose of this instance.", 
                                     "default": "", 
                                     "examples": [ 
                                         0 
                                     ] 
                                 }, 
                                 "tipo": { 
                                     "$id": "#/properties/faturas/items/anyOf/0/properties/tipo", 
                                     "type": "integer", 
                                     "title": "The tipo schema", 
                                     "description": "An explanation about the purpose of this instance.", 
                                     "default": 0, 
                                     "examples": [ 
                                         1 
                                     ] 
                                 }, 
                                 "descontar": { 
                                     "$id": "#/properties/faturas/items/anyOf/0/properties/descontar", 
                                     "type": "boolean", 
                                     "title": "The descontar schema", 
                                     "description": "An explanation about the purpose of this instance.", 
                                     "default": false, 
                                     "examples": [ 
                                         true 
                                     ] 
                                 }, 
                                 "valorFatura": { 
                                     "$id": "#/properties/faturas/items/anyOf/0/properties/valorFatura", 
                                     "type": "number", 
                                     "title": "The valorFatura schema", 
                                     "description": "An explanation about the purpose of this instance.", 
                                     "default": 0.0, 
                                     "examples": [ 
                                         150.55 
                                     ] 
                                 }, 
                                 "dataVencimento": { 
                                     "$id": "#/properties/faturas/items/anyOf/0/properties/dataVencimento", 
                                     "type": "string", 
                                     "title": "The dataVencimento schema", 
                                     "description": "An explanation about the purpose of this instance.", 
                                     "default": "", 
                                     "examples": [ 
                                         "2021-08-23T12:07:33.560" 
                                     ] 
                                 } 
                             }, 
                             "additionalProperties": true 
                         } 
                     ] 
                 } 
             } 
         }, 
         "additionalProperties": true 
     }'
where route = '/api/proposta/valida-luz-em-dia' and method = 'POST'