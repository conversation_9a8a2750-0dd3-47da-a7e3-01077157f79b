DECLARE @IdParametroGrupo int 
select @idParametroGrupo = id from Parametro.ParametroGrupo where nome = 'Crédito'


INSERT INTO Parametro.Parametro VALUES 
(@idParametroGrupo,1,'URL_DOWNLOAD_ASSINATURA_SELFIE','URL para download','URL para download da assinatura e selfie da proposta',1,'/api/imagem/proposta/{propostaId}/assinatura/{propostaAssinaturaId}?tipo={tipoDocumento}');


INSERT INTO Parametro.Parametro VALUES 
(@idParametroGrupo,1,'URL_DOWNLOAD_DOCUMENTO','URL para download','URL para download dos documentos da proposta',1,'/api/imagem/proposta/{propostaImagemId}');


INSERT INTO Documento VALUES 
('ASSINATURA DO CLIENTE', 0,NULL),
('SELFIE CLIENTE', 0,NULL)