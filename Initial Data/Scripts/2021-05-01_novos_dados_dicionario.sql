begin

  declare @idDado int

  insert into dbo.DicionarioDados
  ([Descricao], [Tag]) values
  ('Estado Civil', 'cliente.estadoCivil')

  set @idDado = @@IDENTITY

  insert into dbo.ProdutoDado
  ([ProdutoId], [DadoEditavelId], [Editavel], [Obrigatorio])
  values
  (1, @idDado, 1, 0),
  (6, @idDado, 1, 0)

  insert into dbo.DicionarioDados
  ([Descricao], [Tag]) values
	('Pessoa Exposta Politicamente', 'cliente.pep')

  set @idDado = @@IDENTITY

  insert into dbo.ProdutoDado
  ([ProdutoId], [DadoEditavelId], [Editavel], [Obrigatorio])
  values
  (1, @idDado, 1, 0),
  (6, @idDado, 1, 0)

  insert into dbo.DicionarioDados
  ([Descricao], [Tag]) values
	('Id UF', 'endereco.ufId')

  set @idDado = @@IDENTITY

  insert into dbo.ProdutoDado
  ([ProdutoId], [DadoEditavelId], [Editavel], [Obrigatorio])
  values
  (1, @idDado, 1, 0),
  (6, @idDado, 1, 0)

  insert into dbo.DicionarioDados
  ([Descricao], [Tag]) values
	('Id Cidade', 'endereco.cidadeId')

  set @idDado = @@IDENTITY

  insert into dbo.ProdutoDado
  ([ProdutoId], [DadoEditavelId], [Editavel], [Obrigatorio])
  values
  (1, @idDado, 1, 0),
  (6, @idDado, 1, 0)

  insert into dbo.DicionarioDados
  ([Descricao], [Tag]) values
	('Tempo Conta', 'bancario.tempoConta')

  set @idDado = @@IDENTITY

  insert into dbo.ProdutoDado
  ([ProdutoId], [DadoEditavelId], [Editavel], [Obrigatorio])
  values
  (1, @idDado, 1, 0),
  (6, @idDado, 1, 0)

  insert into dbo.DicionarioDados
  ([Descricao], [Tag]) values
	('Id Ocupação', 'profissional.ocupacaoId')

  set @idDado = @@IDENTITY

  insert into dbo.ProdutoDado
  ([ProdutoId], [DadoEditavelId], [Editavel], [Obrigatorio])
  values
  (1, @idDado, 1, 0),
  (6, @idDado, 1, 0)

  insert into dbo.DicionarioDados
  ([Descricao], [Tag]) values
	('Renda', 'profissional.renda')

  set @idDado = @@IDENTITY

  insert into dbo.ProdutoDado
  ([ProdutoId], [DadoEditavelId], [Editavel], [Obrigatorio])
  values
  (1, @idDado, 1, 0),
  (6, @idDado, 1, 0)

  insert into dbo.DicionarioDados
  ([Descricao], [Tag]) values
	('Outras Rendas', 'profissional.outrasRenda')

  set @idDado = @@IDENTITY

  insert into dbo.ProdutoDado
  ([ProdutoId], [DadoEditavelId], [Editavel], [Obrigatorio])
  values
  (1, @idDado, 1, 0),
  (6, @idDado, 1, 0)

  insert into dbo.DicionarioDados
  ([Descricao], [Tag]) values
	('Tipo Outras Rendas','profissional.tipoOutrasRenda')

  set @idDado = @@IDENTITY

  insert into dbo.ProdutoDado
  ([ProdutoId], [DadoEditavelId], [Editavel], [Obrigatorio])
  values
  (1, @idDado, 1, 0),
  (6, @idDado, 1, 0)

  select @idDado = Id from dbo.DicionarioDados where Tag = 'operacao.tabelaJurosId'

  update dbo.ProdutoDado set Editavel = 0 where DadoEditavelId = @idDado

end
