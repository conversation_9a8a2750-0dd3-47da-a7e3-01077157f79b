BEGIN

    --id do controler
    --não necessario inserir valor

    declare @controllerId INT

    declare @controllername varchar(50) = 'Comissao',
            @controllerUser varchar(50) = 'crefazapisqlstag',
            @controllerdesc varchar(max) = 'Insere Comissões',
            @routname varchar(255) = 'Atualiza status verificação nota',
            @routedesc varchar(max) = 'Muda o status da verificacao da nota',
            @route varchar(100) = '/api/comissao/verifica-nota/{id}',
            @routemethod varchar(10) = 'PUT',
            @routeprocedure varchar(255) = 'controller.stpComissao',
            @routeinput varchar(max) = '{
            "$schema": "http://json-schema.org/draft-07/schema",
            "$id": "http://example.com/example.json",
            "type": "object",
            "title": "The root schema",
            "description": "The root schema comprises the entire JSON document.",
            "default": {},
            "examples": [
                {
                    "id": 2
                }
            ],
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "$id": "#/properties/id",
                    "type": "integer",
                    "title": "The id schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        2
                    ]
                }
            },
            "additionalProperties": true
        }',
            @routeoutput varchar(max) = '{}',
            @routeresponseid int = 2

            select @controllerId = Id from api.controller where [name] = @controllername

            IF @controllerId is null
            BEGIN
                insert into api.controller([user],[name],[description])
                values(
                    @controllerUser,
                    @controllername,
                    @controllerdesc
                )

                set @controllerId = @@IDENTITY
            END

            insert into api.route
            (
                controller_id,
                [name],
                [description],
                [route],
                [method],
                [procedure],
                [input],
                [output],
                [response_type_id]
            ) 
            VALUES(
                @controllerId,
                @routname,
                @routedesc,
                @route,
                @routemethod,
                @routeprocedure,
                @routeinput,
                @routeoutput,
                @routeresponseid
            )

END