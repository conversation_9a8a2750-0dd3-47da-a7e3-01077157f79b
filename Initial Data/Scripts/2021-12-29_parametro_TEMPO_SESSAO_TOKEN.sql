

declare @ParamentroGrupoId int , 
        @data datetime = getdate() , 
        @valor varchar(max) = '120'

select @ParamentroGrupoId = id from parametro.parametroGrupo where nome = 'Segurança'

insert into Parametro.Parametro (ParametroGrupoId, Tipo, Codigo, Nome,NotaTecnica,Ordem, Valor) values (@ParamentroGrupoId,2,'TEMPO_SESSAO_TOKEN','Tempo sessão de token','Te<PERSON>, em minutos, de sessão de token gerado pela API', 1,@valor )
insert into Parametro.ParametroHistorico values (@@IDENTITY, @data,@valor)
