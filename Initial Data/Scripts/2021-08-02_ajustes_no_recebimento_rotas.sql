


update api.route set [input] = 
'{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "pagina": 1,
            "quantidadePorPagina": 10,
            "filtroComissaoPaga": "não pago",
            "filtroProduto": 0,
            "cpf": "00000000000",
            "codigoOperacao": "546",
            "matriz": [
                0
            ],
            "filtroDataPagamentoInicial": "2021-03-02T11:12:54.830",
            "filtroDataPagamentoFinal": "2021-03-02T11:12:54.830",
            "filtroDataComissaoInicial": "2021-03-02T11:12:54.830",
            "filtroDataComissaoFinal": "2021-03-02T11:12:54.830",
            "ordenacao": "id",
            "ordenacaoAsc": true
        }
    ],
    "required": [
        "pagina",
        "quantidadePorPagina",
        "ordenacao",
        "ordenacaoAsc"
    ],
    "properties": {
        "pagina": {
            "$id": "#/properties/pagina",
            "type": "integer",
            "title": "The pagina schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "quantidadePorPagina": {
            "$id": "#/properties/quantidadePorPagina",
            "type": "integer",
            "title": "The quantidadePorPagina schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                10
            ]
        },
        "filtroComissaoPaga": {
            "$id": "#/properties/filtroComissaoPaga",
            "type": "string",
            "title": "The filtroComissaoPaga schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "não pago"
            ]
        },
        "filtroProduto": {
            "$id": "#/properties/filtroProduto",
            "type": "integer",
            "title": "The filtroProduto schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                0
            ]
        },
        "cpf": {
            "$id": "#/properties/cpf",
            "type": "string",
            "title": "The cpf schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "00000000000"
            ]
        },
        "codigoOperacao": {
            "$id": "#/properties/codigoOperacao",
            "type": "string",
            "title": "The codigoOperacao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "546"
            ]
        },
        "matriz": {
            "$id": "#/properties/matriz",
            "type": "array",
            "title": "The matriz schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [
                    0
                ]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/matriz/items",
                "anyOf": [
                    {
                        "$id": "#/properties/matriz/items/anyOf/0",
                        "type": "integer",
                        "title": "The first anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            0
                        ]
                    }
                ]
            }
        },
        "filtroDataPagamentoInicial": {
            "$id": "#/properties/filtroDataPagamentoInicial",
            "type": "string",
            "title": "The filtroDataPagamentoInicial schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-03-02T11:12:54.830"
            ]
        },
        "filtroDataPagamentoFinal": {
            "$id": "#/properties/filtroDataPagamentoFinal",
            "type": "string",
            "title": "The filtroDataPagamentoFinal schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-03-02T11:12:54.830"
            ]
        },
        "filtroDataComissaoInicial": {
            "$id": "#/properties/filtroDataComissaoInicial",
            "type": "string",
            "title": "The filtroDataComissaoInicial schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-03-02T11:12:54.830"
            ]
        },
        "filtroDataComissaoFinal": {
            "$id": "#/properties/filtroDataComissaoFinal",
            "type": "string",
            "title": "The filtroDataComissaoFinal schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-03-02T11:12:54.830"
            ]
        },
        "ordenacao": {
            "$id": "#/properties/ordenacao",
            "type": "string",
            "title": "The ordenacao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "id"
            ]
        },
        "ordenacaoAsc": {
            "$id": "#/properties/ordenacaoAsc",
            "type": "boolean",
            "title": "The ordenacaoAsc schema",
            "description": "An explanation about the purpose of this instance.",
            "default": false,
            "examples": [
                true
            ]
        }
    },
    "additionalProperties": true
}'
where [route] = '/api/comissao/comissao-lista' and [method] = 'POST'



update api.route set [input] = 
'{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "pagina": 1,
            "quantidadePorPagina": 10,
            "filtroComissaoPaga": "não pago",
            "filtroProduto": 0,
            "cpf": "00000000000",
            "codigoOperacao": "546",
            "matriz": [
                0
            ],
            "filtroDataPagamentoInicial": "2021-03-02T11:12:54.830",
            "filtroDataPagamentoFinal": "2021-03-02T11:12:54.830",
            "filtroDataComissaoInicial": "2021-03-02T11:12:54.830",
            "filtroDataComissaoFinal": "2021-03-02T11:12:54.830",
            "ordenacao": "id",
            "ordenacaoAsc": true
        }
    ],
    "required": [
        "ordenacao",
        "ordenacaoAsc"
    ],
    "properties": {
        "pagina": {
            "$id": "#/properties/pagina",
            "type": "integer",
            "title": "The pagina schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "quantidadePorPagina": {
            "$id": "#/properties/quantidadePorPagina",
            "type": "integer",
            "title": "The quantidadePorPagina schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                10
            ]
        },
        "filtroComissaoPaga": {
            "$id": "#/properties/filtroComissaoPaga",
            "type": "string",
            "title": "The filtroComissaoPaga schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "não pago"
            ]
        },
        "filtroProduto": {
            "$id": "#/properties/filtroProduto",
            "type": "integer",
            "title": "The filtroProduto schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                0
            ]
        },
        "cpf": {
            "$id": "#/properties/cpf",
            "type": "string",
            "title": "The cpf schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "00000000000"
            ]
        },
        "codigoOperacao": {
            "$id": "#/properties/codigoOperacao",
            "type": "string",
            "title": "The codigoOperacao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "546"
            ]
        },
        "matriz": {
            "$id": "#/properties/matriz",
            "type": "array",
            "title": "The matriz schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [
                    0
                ]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/matriz/items",
                "anyOf": [
                    {
                        "$id": "#/properties/matriz/items/anyOf/0",
                        "type": "integer",
                        "title": "The first anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            0
                        ]
                    }
                ]
            }
        },
        "filtroDataPagamentoInicial": {
            "$id": "#/properties/filtroDataPagamentoInicial",
            "type": "string",
            "title": "The filtroDataPagamentoInicial schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-03-02T11:12:54.830"
            ]
        },
        "filtroDataPagamentoFinal": {
            "$id": "#/properties/filtroDataPagamentoFinal",
            "type": "string",
            "title": "The filtroDataPagamentoFinal schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-03-02T11:12:54.830"
            ]
        },
        "filtroDataComissaoInicial": {
            "$id": "#/properties/filtroDataComissaoInicial",
            "type": "string",
            "title": "The filtroDataComissaoInicial schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-03-02T11:12:54.830"
            ]
        },
        "filtroDataComissaoFinal": {
            "$id": "#/properties/filtroDataComissaoFinal",
            "type": "string",
            "title": "The filtroDataComissaoFinal schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-03-02T11:12:54.830"
            ]
        },
        "ordenacao": {
            "$id": "#/properties/ordenacao",
            "type": "string",
            "title": "The ordenacao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "id"
            ]
        },
        "ordenacaoAsc": {
            "$id": "#/properties/ordenacaoAsc",
            "type": "boolean",
            "title": "The ordenacaoAsc schema",
            "description": "An explanation about the purpose of this instance.",
            "default": false,
            "examples": [
                true
            ]
        }
    },
    "additionalProperties": true
}'
where ([route] = '/api/comissao/comissao-lista/exportacao-csv' or [route] = '/api/comissao/comissao-lista/exportacao-pdf') and [method] = 'POST'


update api.route 
set [input] = 
'{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "pagina": 1,
            "quantidadePorPagina": 10,
            "filtroMesAnoReferencia": "",
            "filtroCNPJMatriz": "",
            "filtroMatriz": [
                0
            ],
            "filtroStatusVerificacao": null,
            "filtroDataInicial": "2021-03-02T11:12:54.830",
            "filtroDataFinal": "2021-03-02T11:12:54.830",
            "ordenacao": "id",
            "ordenacaoAsc": true
        }
    ],
    "required": [
        "pagina",
        "quantidadePorPagina",
        "ordenacao",
        "ordenacaoAsc"
    ],
    "properties": {
        "pagina": {
            "$id": "#/properties/pagina",
            "type": "integer",
            "title": "The pagina schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "quantidadePorPagina": {
            "$id": "#/properties/quantidadePorPagina",
            "type": "integer",
            "title": "The quantidadePorPagina schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                10
            ]
        },
        "filtroMesAnoReferencia": {
            "$id": "#/properties/filtroMesAnoReferencia",
            "type": "string",
            "title": "The filtroMesAnoReferencia schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                ""
            ]
        },
        "filtroCNPJMatriz": {
            "$id": "#/properties/filtroCNPJMatriz",
            "type": "string",
            "title": "The filtroCNPJMatriz schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                ""
            ]
        },
        "filtroMatriz": {
            "$id": "#/properties/filtroMatriz",
            "type": "array",
            "title": "The filtroMatriz schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [
                    0
                ]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/filtroMatriz/items",
                "anyOf": [
                    {
                        "$id": "#/properties/filtroMatriz/items/anyOf/0",
                        "type": "integer",
                        "title": "The first anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            0
                        ]
                    }
                ]
            }
        },
        "filtroStatusVerificacao": {
            "$id": "#/properties/filtroStatusVerificacao",
            "type": "null",
            "title": "The filtroStatusVerificacao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": null,
            "examples": [
                null
            ]
        },
        "filtroDataInicial": {
            "$id": "#/properties/filtroDataInicial",
            "type": "string",
            "title": "The filtroDataInicial schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-03-02T11:12:54.830"
            ]
        },
        "filtroDataFinal": {
            "$id": "#/properties/filtroDataFinal",
            "type": "string",
            "title": "The filtroDataFinal schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-03-02T11:12:54.830"
            ]
        },
        "ordenacao": {
            "$id": "#/properties/ordenacao",
            "type": "string",
            "title": "The ordenacao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "id"
            ]
        },
        "ordenacaoAsc": {
            "$id": "#/properties/ordenacaoAsc",
            "type": "boolean",
            "title": "The ordenacaoAsc schema",
            "description": "An explanation about the purpose of this instance.",
            "default": false,
            "examples": [
                true
            ]
        }
    },
    "additionalProperties": true
}'
where [route] = '/api/comissao/relatorio/notas' and [method] = 'POST'



