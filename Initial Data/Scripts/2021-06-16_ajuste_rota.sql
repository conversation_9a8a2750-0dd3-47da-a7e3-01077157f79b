UPDATE api.route set [input] = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "id": 6,
            "produtoId": 6,
            "convenioId": 1,
            "tabelaJurosId": 2,
            "vencimento": "2021-07-16",
            "valorContrato": 500,
            "adicionais": [
                {
                    "convenioDadosId": 12,
                    "convenioId": 1,
                    "nome": "N° da Instalação",
                    "tipo": 2,
                    "ordem": 1,
                    "formato": "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d]",
                    "mensagem": "N° da instalação inválido. Informe o N° da instalação com nove dígitos.",
                    "valor": "123456782"
                },
                {
                    "convenioDadosId": 23,
                    "convenioId": 1,
                    "nome": "Data de Leitura",
                    "tipo": 4,
                    "ordem": 2,
                    "mensagem": "Data inválida.",
                    "valor": "2021-06-16T12:42:56.818Z"
                }
            ]
        }
    ],
    "required": [
        "id",
        "produtoId",
        "convenioId",
        "tabelaJurosId",
        "vencimento",
        "valorContrato",
        "adicionais"
    ],
    "properties": {
        "id": {
            "$id": "#/properties/id",
            "type": "integer",
            "title": "The id schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                6
            ]
        },
        "produtoId": {
            "$id": "#/properties/produtoId",
            "type": "integer",
            "title": "The produtoId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                6
            ]
        },
        "convenioId": {
            "$id": "#/properties/convenioId",
            "type": "integer",
            "title": "The convenioId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "tabelaJurosId": {
            "$id": "#/properties/tabelaJurosId",
            "type": "integer",
            "title": "The tabelaJurosId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                2
            ]
        },
        "vencimento": {
            "$id": "#/properties/vencimento",
            "type": "string",
            "title": "The vencimento schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-07-16"
            ]
        },
        "valorContrato": {
            "$id": "#/properties/valorContrato",
            "type": "integer",
            "title": "The valorContrato schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                500
            ]
        },
        "adicionais": {
            "$id": "#/properties/adicionais",
            "type": "array",
            "title": "The adicionais schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [
                    {
                        "convenioDadosId": 12,
                        "convenioId": 1,
                        "nome": "N° da Instalação",
                        "tipo": 2,
                        "ordem": 1,
                        "formato": "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d]",
                        "mensagem": "N° da instalação inválido. Informe o N° da instalação com nove dígitos.",
                        "valor": "123456782"
                    },
                    {
                        "convenioDadosId": 23,
                        "convenioId": 1,
                        "nome": "Data de Leitura",
                        "tipo": 4,
                        "ordem": 2,
                        "mensagem": "Data inválida.",
                        "valor": "2021-06-16T12:42:56.818Z"
                    }
                ]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/adicionais/items",
                "anyOf": [
                    {
                        "$id": "#/properties/adicionais/items/anyOf/0",
                        "type": "object",
                        "title": "The first anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": {},
                        "examples": [
                            {
                                "convenioDadosId": 12,
                                "convenioId": 1,
                                "nome": "N° da Instalação",
                                "tipo": 2,
                                "ordem": 1,
                                "formato": "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d]",
                                "mensagem": "N° da instalação inválido. Informe o N° da instalação com nove dígitos.",
                                "valor": "123456782"
                            }
                        ],
                        "required": [
                            "convenioDadosId",
                            "convenioId",
                            "nome",
                            "tipo",
                            "ordem",
                            "formato",
                            "mensagem",
                            "valor"
                        ],
                        "properties": {
                            "convenioDadosId": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/convenioDadosId",
                                "type": "integer",
                                "title": "The convenioDadosId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    12
                                ]
                            },
                            "convenioId": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/convenioId",
                                "type": "integer",
                                "title": "The convenioId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1
                                ]
                            },
                            "nome": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/nome",
                                "type": "string",
                                "title": "The nome schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "N° da Instalação"
                                ]
                            },
                            "tipo": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/tipo",
                                "type": "integer",
                                "title": "The tipo schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    2
                                ]
                            },
                            "ordem": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/ordem",
                                "type": "integer",
                                "title": "The ordem schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1
                                ]
                            },
                            "formato": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/formato",
                                "type": "string",
                                "title": "The formato schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d]"
                                ]
                            },
                            "mensagem": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/mensagem",
                                "type": "string",
                                "title": "The mensagem schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "N° da instalação inválido. Informe o N° da instalação com nove dígitos."
                                ]
                            },
                            "valor": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/valor",
                                "type": "string",
                                "title": "The valor schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "123456782"
                                ]
                            }
                        },
                        "additionalProperties": true
                    },
                    {
                        "$id": "#/properties/adicionais/items/anyOf/1",
                        "type": "object",
                        "title": "The second anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": {},
                        "examples": [
                            {
                                "convenioDadosId": 23,
                                "convenioId": 1,
                                "nome": "Data de Leitura",
                                "tipo": 4,
                                "ordem": 2,
                                "mensagem": "Data inválida.",
                                "valor": "2021-06-16T12:42:56.818Z"
                            }
                        ],
                        "required": [
                            "convenioDadosId",
                            "convenioId",
                            "nome",
                            "tipo",
                            "ordem",
                            "mensagem",
                            "valor"
                        ],
                        "properties": {
                            "convenioDadosId": {
                                "$id": "#/properties/adicionais/items/anyOf/1/properties/convenioDadosId",
                                "type": "integer",
                                "title": "The convenioDadosId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    23
                                ]
                            },
                            "convenioId": {
                                "$id": "#/properties/adicionais/items/anyOf/1/properties/convenioId",
                                "type": "integer",
                                "title": "The convenioId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1
                                ]
                            },
                            "nome": {
                                "$id": "#/properties/adicionais/items/anyOf/1/properties/nome",
                                "type": "string",
                                "title": "The nome schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "Data de Leitura"
                                ]
                            },
                            "tipo": {
                                "$id": "#/properties/adicionais/items/anyOf/1/properties/tipo",
                                "type": "integer",
                                "title": "The tipo schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    4
                                ]
                            },
                            "ordem": {
                                "$id": "#/properties/adicionais/items/anyOf/1/properties/ordem",
                                "type": "integer",
                                "title": "The ordem schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    2
                                ]
                            },
                            "mensagem": {
                                "$id": "#/properties/adicionais/items/anyOf/1/properties/mensagem",
                                "type": "string",
                                "title": "The mensagem schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "Data inválida."
                                ]
                            },
                            "valor": {
                                "$id": "#/properties/adicionais/items/anyOf/1/properties/valor",
                                "type": "string",
                                "title": "The valor schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "2021-06-16T12:42:56.818Z"
                                ]
                            }
                        },
                        "additionalProperties": true
                    }
                ]
            }
        }
    },
    "additionalProperties": true
}' where [route] = '/api/proposta/simulacao-crivo'