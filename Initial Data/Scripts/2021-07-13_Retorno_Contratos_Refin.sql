declare @id int, @valor varchar(255), @GrupoId int

select @GrupoId = Id from Parametro.ParametroGrupo where Nome = 'Crivo - Oferta de Produtos'

set @valor = '"Driver"."Crefaz"."API Datacob - Divida Ativa Negociação - PF"."Quantidade - Contratos encontrados"'
insert into Parametro.Parametro (ParametroGrupoId, Tipo, Codigo, Nome, NotaTecnica, Ordem, Valor) values
(@GrupoId,1,'QUANTIDADE_CONTRATOS','QUANTIDADE CONTRATOS','retorna quantidade de contratos refin',1,@valor)
set @id = @@IDENTITY
 
insert into Parametro.ParametroHistorico values (@id,dbo.getdateBR(),@valor)


set @valor = '"Driver"."Crefaz"."API Datacob - Divida Ativa Negociação - PF"."Discreta - Permite negociar - contrato $numero$"'
insert into Parametro.Parametro (ParametroGrupoId, Tipo, Codigo, Nome, NotaTecnica, Ordem, Valor) values
(@GrupoId,1,'PERMITE_NEGOCIAR','PERMITE NEGOCIAR','retorna se o contrato pode ou não ser refinanciado',1,@valor)
set @id = @@IDENTITY
 
insert into Parametro.ParametroHistorico values (@id,dbo.getdateBR(),@valor)


set @valor = '"Driver"."Crefaz"."API Datacob - Divida Ativa Negociação - PF"."Discreta - Número - contrato $numero$"'
insert into Parametro.Parametro (ParametroGrupoId, Tipo, Codigo, Nome, NotaTecnica, Ordem, Valor) values
(@GrupoId,1,'NUMERO_CONTRATO','NUMERO CONTRATO','retorna numero contrato refin',1,@valor)
set @id = @@IDENTITY
 
insert into Parametro.ParametroHistorico values (@id,dbo.getdateBR(),@valor)

set @valor = '"Driver"."Crefaz"."API Datacob - Divida Ativa Negociação - PF"."Valor - Dívida - contrato $numero$"'
insert into Parametro.Parametro (ParametroGrupoId, Tipo, Codigo, Nome, NotaTecnica, Ordem, Valor) values
(@GrupoId,1,'SALDO_DEVEDOR','SALDO DEVEDOR','retorna saldo devedor do contrato',1,@valor)
set @id = @@IDENTITY
 
insert into Parametro.ParametroHistorico values (@id,dbo.getdateBR(),@valor)
