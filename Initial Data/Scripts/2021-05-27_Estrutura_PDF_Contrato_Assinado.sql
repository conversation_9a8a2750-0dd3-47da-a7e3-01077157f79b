CREATE TABLE [dbo].[PropostaContratoPDF](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[PropostaId] [int] NULL,
	[DataHora] [datetime] NOT NULL,
	[Conteudo] [varbinary](max) NULL,
 CONSTRAINT [PK_PropostaContratoPDF] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]

ALTER TABLE [dbo].[PropostaContratoPDF]  WITH CHECK ADD  CONSTRAINT [Fk_PropostaContratoPDF_Proposta] FOREIGN KEY([PropostaId])
REFERENCES [dbo].[Proposta] ([Id])

ALTER TABLE [dbo].[PropostaContratoPDF] CHECK CONSTRAINT [Fk_PropostaContratoPDF_Proposta]