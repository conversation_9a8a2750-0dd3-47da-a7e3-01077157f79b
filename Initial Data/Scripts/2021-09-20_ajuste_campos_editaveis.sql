DECLARE @dicionarioDadosId INT, @propostaEtapaId INT

select @propostaEtapaId = Id from PropostaEtapa where Nome = 'Ligando'

select @dicionarioDadosId = id from dicionariodados where tag = 'operacao.operacao'
DELETE CamposEditaveis where DicionarioDadosId = @dicionarioDadosId and EtapaId = @propostaEtapaId

select @dicionarioDadosId = Id from dicionariodados where tag = 'operacao.vencimento';

DELETE a from CamposEditaveis a join PropostaEtapa b on a.EtapaId = b.id where MotivoId is null and b.nome in ('Falha de Comunicação', 'Em Análise', 'Proposta Pendente', 'Análise Validação');

select * from dicionariodados where tag = 'operacao.prazo'
DELETE a from CamposEditaveis a join PropostaEtapa b on a.EtapaId = b.id where MotivoId is null and b.nome = 'Proposta Pendente';

select * from dicionariodados where tag = 'operacao.prestacao'
DELETE a from CamposEditaveis a join PropostaEtapa b on a.EtapaId = b.id where MotivoId is null and b.nome in ('Proposta Pendente', 'Ligando');
