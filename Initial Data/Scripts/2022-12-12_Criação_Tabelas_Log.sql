/*
	ESTRUTURAÇÃO DAS TABELAS COM BASE NOS SCRIPTS DA DATABASERS:
*/

--01 [Log].[stpAnotacaoCrud]
if not exists (SELECT * FROM  sys.tables  t  join sys.schemas s on s.schema_id = t.schema_id where t.name = 'stpAnotacaoCrud' and s.name = 'log')

CREATE TABLE [Log].[stpAnotacaoCrud]( 
 	[Id] uniqueidentifier NOT NULL  DEFAULT newid(),  
 	[DataHora] [datetime] NULL, 
 	[Posicao] int null 
 ) 


 --02 [Log].[stpOcupacaoGet]
if not exists (SELECT * FROM  sys.tables  t  join sys.schemas s on s.schema_id = t.schema_id where t.name = 'stpOcupacaoGet' and s.name = 'log')

CREATE TABLE [Log].[stpOcupacaoGet]( 
 	[Id] uniqueidentifier NOT NULL  DEFAULT newid(),  
 	[DataHora] [datetime] NULL, 
 	[Posicao] int null 
 ) 


 --03 [Log].[stpProdutoGet]
if not exists (SELECT * FROM  sys.tables  t  join sys.schemas s on s.schema_id = t.schema_id where t.name = 'stpProdutoGet' and s.name = 'log')

CREATE TABLE [Log].[stpProdutoGet]( 
 	[Id] uniqueidentifier NOT NULL  DEFAULT newid(),  
 	[DataHora] [datetime] NULL, 
 	[Posicao] int null 
 ) 


 --04 [Log].[stpRespostaCrivo]
if not exists (SELECT * FROM  sys.tables  t  join sys.schemas s on s.schema_id = t.schema_id where t.name = 'stpRespostaCrivo' and s.name = 'log')

CREATE TABLE [Log].[stpRespostaCrivo]( 
 	[Id] uniqueidentifier NOT NULL  DEFAULT newid(),  
 	[DataHora] [datetime] NULL, 
 	[Posicao] int null 
 ) 