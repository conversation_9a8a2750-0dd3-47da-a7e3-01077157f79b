if not exists (SELECT * FROM  sys.tables  t  join sys.schemas s on s.schema_id = t.schema_id where t.name = 'stpCadastroPessoaGet' and s.name = 'log')
BEGIN
    CREATE TABLE [Log].[stpCadastroPessoaGet](
        [Id] [uniqueidentifier] NOT NULL,
        [DataHora] [datetime] NULL,
        [Posicao] [int] NULL
    ) ON [PRIMARY]
end
-----
if not exists (SELECT * FROM  sys.tables  t  join sys.schemas s on s.schema_id = t.schema_id where t.name = 'stpCarregaImagemGet' and s.name = 'log')
BEGIN
    CREATE TABLE [Log].[stpCarregaImagemGet](
        [Id] [uniqueidentifier] NOT NULL,
        [DataHora] [datetime] NULL,
        [Posicao] [int] NULL
    ) ON [PRIMARY]
end
----
if not exists (SELECT * FROM  sys.tables  t  join sys.schemas s on s.schema_id = t.schema_id where t.name = 'stpCepGet' and s.name = 'log')
BEGIN
    CREATE TABLE [Log].[stpCepGet](
        [Id] [uniqueidentifier] NOT NULL,
        [DataHora] [datetime] NULL,
        [Posicao] [int] NULL
    ) ON [PRIMARY]
end
-----
if not exists (SELECT * FROM  sys.tables  t  join sys.schemas s on s.schema_id = t.schema_id where t.name = 'stpChamadaCrud' and s.name = 'log')
BEGIN
    CREATE TABLE [Log].[stpChamadaCrud](
        [Id] [uniqueidentifier] NOT NULL,
        [DataHora] [datetime] NULL,
        [Posicao] [int] NULL
    ) ON [PRIMARY]
end
----
if not exists (SELECT * FROM  sys.tables  t  join sys.schemas s on s.schema_id = t.schema_id where t.name = 'stpChamadaGet' and s.name = 'log')
BEGIN
    CREATE TABLE [Log].[stpChamadaGet](
        [Id] [uniqueidentifier] NOT NULL,
        [DataHora] [datetime] NULL,
        [Posicao] [int] NULL
    ) ON [PRIMARY]
end