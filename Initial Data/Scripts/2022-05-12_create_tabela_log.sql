CREATE TABLE [Log].[stpAcompanhamentoTempoGet](   [Id] uniqueidentifier NOT NULL  DEFAULT newid(),    [DataHora] [datetime] NULL,   [Posicao] int null /*Ponto dentro da procedure*/  )  
CREATE TABLE [Log].[stpBancoGet](   [Id] uniqueidentifier NOT NULL  DEFAULT newid(),    [DataHora] [datetime] NULL,   [Posicao] int null /*Ponto dentro da procedure*/  )  
CREATE TABLE [Log].[stpCarregaImagemCrud](   [Id] uniqueidentifier NOT NULL  DEFAULT newid(),    [DataHora] [datetime] NULL,   [Posicao] int null /*Ponto dentro da procedure*/  )  
CREATE TABLE [Log].[stpDocumentoGet](   [Id] uniqueidentifier NOT NULL  DEFAULT newid(),    [DataHora] [datetime] NULL,   [Posicao] int null /*Ponto dentro da procedure*/  )  
CREATE TABLE [Log].[stpGrauInstrucaoGet](   [Id] uniqueidentifier NOT NULL  DEFAULT newid(),    [DataHora] [datetime] NULL,   [Posicao] int null /*Ponto dentro da procedure*/  )  
CREATE TABLE [Log].[stpOcupacaoGet](   [Id] uniqueidentifier NOT NULL  DEFAULT newid(),    [DataHora] [datetime] NULL,   [Posicao] int null /*Ponto dentro da procedure*/  )  
CREATE TABLE [Log].[stpProdutoGet](   [Id] uniqueidentifier NOT NULL  DEFAULT newid(),    [DataHora] [datetime] NULL,   [Posicao] int null /*Ponto dentro da procedure*/  )  
CREATE TABLE [Log].[stpProfissaoGet](   [Id] uniqueidentifier NOT NULL  DEFAULT newid(),    [DataHora] [datetime] NULL,   [Posicao] int null /*Ponto dentro da procedure*/  )  
CREATE TABLE [Log].[stpPropostaCentralComunicacaoGet](   [Id] uniqueidentifier NOT NULL  DEFAULT newid(),    [DataHora] [datetime] NULL,   [Posicao] int null /*Ponto dentro da procedure*/  )  
CREATE TABLE [Log].[stpTabelaJurosGet](   [Id] uniqueidentifier NOT NULL  DEFAULT newid(),    [DataHora] [datetime] NULL,   [Posicao] int null /*Ponto dentro da procedure*/  )  


