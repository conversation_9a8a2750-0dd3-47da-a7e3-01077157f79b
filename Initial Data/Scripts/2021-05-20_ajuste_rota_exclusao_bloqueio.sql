DECLARE @json VARCHAR(MAX) = 
'{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "id": 1,
            "descricao": "descrição da remoção"
        }
    ],
    "required": [
        "id"
    ],
    "properties": {
        "id": {
            "$id": "#/properties/id",
            "type": "integer",
            "title": "The id schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "descricao": {
            "$id": "#/properties/descricao",
            "type": "string",
            "title": "The descricao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "descrição da remoção"
            ]
        }
    },
    "additionalProperties": true
}'



update api.route set [input] = @json
where method = 'PUT'
and route in ('/api/bloqueado/telefone/{id}',
 '/api/bloqueado/unidade-consumidora/{id}',
 '/api/bloqueado/cpf/{id}')