BEGIN

    --id do controler
    --não necessario inserir valor

    declare @controllerId INT

    declare @controllername varchar(50) = 'Comissao',
            @controllerUser varchar(50) = 'crefazapisqlstag',
            @controllerdesc varchar(max) = 'Retorno de arquivo de nota fiscal',
            @routname varchar(255) = 'Retorna arquivo de nota fiscal',
            @routedesc varchar(max) = 'Faz o retorno da nota fiscal',
            @route varchar(100) = '/api/comissao/download-nota/{id}',
            @routemethod varchar(10) = 'GET',
            @routeprocedure varchar(255) = 'controller.stpComissao',
            @routeinput varchar(max) = '{}',
            @routeoutput varchar(max) = '{}',
            @routeresponseid int = 5

            select @controllerId = Id from api.controller where [name] = @controllername

            select @controllerId

            IF @controllerId is null
            BEGIN
                insert into api.controller([user],[name],[description])
                values(
                    @controllerUser,
                    @controllername,
                    @controllerdesc
                )

                set @controllerId = @@IDENTITY
            END

            insert into api.route
            (
                controller_id,
                [name],
                [description],
                [route],
                [method],
                [procedure],
                [input],
                [output],
                [response_type_id]
            ) 
            VALUES(
                @controllerId,
                @routname,
                @routedesc,
                @route,
                @routemethod,
                @routeprocedure,
                @routeinput,
                @routeoutput,
                @routeresponseid
            )

END