--DR<PERSON><PERSON> CONSTRAINT
IF  EXISTS (SELECT *
FROM sys.all_objects
WHERE OBJECT_DEFINITION(object_id) LIKE '%TipoCanal%')
BEGIN
ALTER TABLE dbo.Correspondente DROP CONSTRAINT [D_Correspondente_TipoCanal] 
END

--### Correspondente
ALTER TABLE dbo.Correspondente
ALTER COLUMN TipoCanal tinyint not null; 

--### Unidade
ALTER TABLE dbo.unidade
ALTER COLUMN Tipo tinyint null;

--## HierarquiaNivel
ALTER TABLE dbo.HierarquiaNivel
ALTER COLUMN Ordem tinyint not null; 

-- Recriar Constraint
ALTER TABLE [dbo].[Correspondente] ADD  CONSTRAINT [DF_Correspondente_TipoCanal]  DEFAULT ((0)) FOR [TipoCanal]

