create table PropostaEtapaFluxo (
    Id int identity(1,1) not null, 
    PropostaEtapaId tinyint not null, 
    PropostaProximaEtapaId tinyint not null 
)
alter table PropostaEtapaFluxo add constraint PK_PropostaEtapaFluxo primary key (Id);
alter table PropostaEtapaFluxo add constraint FK_PropostaEtapaFluxo_PropostaEtapa foreign key (PropostaEtapaId) references PropostaEtapa(Id);
alter table PropostaEtapaFluxo add constraint FK_PropostaEtapaFluxo_PropostaProximaEtapa foreign key (PropostaProximaEtapaId) references PropostaEtapa(Id);
alter table PropostaEtapaFluxo add constraint UQ_PropostaEtapaFluxo unique (PropostaEtapaId,PropostaProximaEtapaId);

alter table PropostaEtapa add Modalidade tinyint

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Enum tipoModalidade' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'PropostaEtapa', @level2type=N'COLUMN',@level2name=N'Modalidade'

