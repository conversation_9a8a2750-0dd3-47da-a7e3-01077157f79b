
drop table OrdenacaoFallowup

drop table if exists FollowUp

create table FollowUp (
    [Id] [int] IDENTITY(1,1) NOT NULL,
	[SituacaoId] [smallint] NOT NULL,
    [Fl_Acumulativo] [bit] not null,
	[Ordem] [tinyint] NOT NULL
)

insert into FollowUp 
    values 
    ((select Id from PropostaEtapa with(nolock) where Nome = 'Aguard. Análise'),1,1),
    ((select Id from PropostaEtapa with(nolock) where Nome = 'Em Análise'),1,2),
    ((select Id from PropostaEtapa with(nolock) where Nome = 'Proposta Pendente'),1,3),
    ((select Id from PropostaEtapa with(nolock) where Nome = 'Aguard. Checagem'),1,4),
    ((select Id from PropostaEtapa with(nolock) where Nome = 'Aguard. Averbação'),1,5),
    ((select Id from PropostaEtapa with(nolock) where Nome = 'Fila Contato'),1,6),
    ((select Id from PropostaEtapa with(nolock) where Nome = 'Ligando'),1,7),
    ((select Id from PropostaEtapa with(nolock) where Nome = 'Contato Pendente'),1,8),
    ((select -Id from PropostaDecisao with(nolock) where Nome = 'Negada'),0,9),
    ((select -Id from PropostaDecisao with(nolock) where Nome = 'Cancelada'),0,10),
    ((select Id from PropostaEtapa with(nolock) where Nome = 'Aguard. Assinatura'),1,11),
    ((select Id from PropostaEtapa with(nolock) where Nome = 'Aguard. Validação'),1,12),
    ((select Id from PropostaEtapa with(nolock) where Nome = 'Análise Promotora'),1,13),
    ((select Id from PropostaEtapa with(nolock) where Nome = 'Fila Pagamento'),0,14),
    ((select Id from PropostaEtapa with(nolock) where Nome = 'Pago ao Cliente'),0,15)

delete from PropostaDecisao where Nome = 'Aprovada'

Alter table PropostaEtapa drop column Fl_FollowUp, FL_Acumulativo
Alter table PropostaDecisao drop column Fl_FollowUp, FL_Acumulativo


/* 
select concat('(((select Id from PropostaEtapa with(nolock) where Nome = ''',b.Nome,'''),',b.FL_Acumulativo,',',a.Ordem,'),'),Ordem from OrdenacaoFallowup a   
    join propostaEtapa b on a.EtapaDecisaoId = b.Id and a.Tipo = 1
union 
select concat('(((select -Id from PropostaDecisao with(nolock) where Nome = ''',b.Nome,'''),',b.FL_Acumulativo,',',a.Ordem,'),'),Ordem from OrdenacaoFallowup a   
    join PropostaDecisao b on a.EtapaDecisaoId = b.Id and a.Tipo = 2
order by Ordem 
*/
