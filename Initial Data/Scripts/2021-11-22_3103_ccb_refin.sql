declare @errado varchar(255),
        @novo varchar(255)

set @errado = '<divDocumentos:'
set @novo =   '<div>Documentos:'

update a set layout = replace(Layout,@errado,@novo)/* [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] ,
        tipo,
        TipoDocumento*/
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'
and ProdutoId in (select id from Produto where nome in ('CP Refin')) and TipoDocumento like 'CCB-%'

set @errado = '<div>Documentos:</div>'
set @novo =   '<div>Documentos: {{PARCELASREFIN}}</div>'

update a set layout = replace(Layout,@errado,@novo)/* [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] ,
        tipo,
        TipoDocumento*/
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'
and ProdutoId in (select id from Produto where nome in ('CP Refin')) and TipoDocumento like 'CCB-%'
                                   
set @errado = '<div>1°: R$ </div>'
set @novo =   ''

update a set layout = replace(Layout,@errado,@novo)/* [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,200) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,200),@errado,@novo) [como fica] ,
        tipo,
        TipoDocumento*/
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'
and ProdutoId in (select id from Produto where nome in ('CP Refin')) and TipoDocumento like 'CCB-%'
                                                                      
set @errado = '<span>2°: </span>'
set @novo =   ''

update a set layout = replace(Layout,@errado,@novo)/* [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,200) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,200),@errado,@novo) [como fica] ,
        tipo,
        TipoDocumento*/
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'
and ProdutoId in (select id from Produto where nome in ('CP Refin')) and TipoDocumento like 'CCB-%'
                                                                      
