declare @TabelaCheque int
declare @TabelaBoletoPadrao int
declare @TabelaBoletoPromo int
declare @ProdutoRefin int
declare @ProdutoCheque int

set @TabelaBoletoPadrao = (select Id from TabelaJuros where Nome = 'Tabela Boleto Padrão')
set @TabelaBoletoPromo = (select Id from TabelaJuros where Nome = 'Tabela Boleto Promocional')
set @ProdutoRefin = (select Id from Produto where Nome = 'CP Refin')
set @ProdutoCheque = (select Id from Produto where Nome = 'CP Cheque')

delete a 
from TabelaJurosValores a
join TabelaJuros b on (a.TabelaJurosId = b.Id)
where TabelaJurosId in (@TabelaBoletoPadrao,@TabelaBoletoPromo) and not (Plano between 8 and 9 or Plano between 11 and 24) 

update TabelaJurosValores set Juros = 15 where TabelaJurosId = @TabelaBoletoPadrao

delete from ProdutoTabelaJuros where TabelaJurosId = @TabelaBoletoPromo

insert into ProdutoTabelaJuros values (@TabelaBoletoPadrao, @ProdutoRefin)

insert into TabelaJuros values ('Tabela Cheque Padrão', 0, null, 5, 15, 1, 0, null, null)
set @TabelaCheque = @@identity

insert into ProdutoTabelaJuros values (@TabelaCheque, @ProdutoCheque)

insert into TabelaJurosValores values 
(@TabelaCheque, null, 8, 14),
(@TabelaCheque, null, 9, 14),
(@TabelaCheque, null, 11, 14),
(@TabelaCheque, null, 12, 14),
(@TabelaCheque, null, 13, 14),
(@TabelaCheque, null, 14, 14),
(@TabelaCheque, null, 15, 14),
(@TabelaCheque, null, 16, 14),
(@TabelaCheque, null, 17, 14),
(@TabelaCheque, null, 18, 14),
(@TabelaCheque, null, 19, 14),
(@TabelaCheque, null, 20, 14),
(@TabelaCheque, null, 21, 14),
(@TabelaCheque, null, 22, 14),
(@TabelaCheque, null, 23, 14),
(@TabelaCheque, null, 24, 14)