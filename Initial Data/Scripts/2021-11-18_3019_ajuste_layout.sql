declare @errado varchar(255),
        @novo varchar(255)

set @errado = 'a) Valor Líquido: '
set @novo =   'a) Valor Líquido: {{VALORLIQUIDO}}'
update a set Layout = replace(Layout,@errado,@novo)
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'
and ProdutoId = (select ID from Produto with(nolock) where Nome = 'CP Refin')


set @errado = '>Total: '
set @novo =   '>Total: {{TOTALPARCELAS}}'
update a set Layout = replace(Layout,@errado,@novo)
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'


set @errado = '<div>1°: R$ </div>'
set @novo =   ''
update a set Layout = replace(Layout,@errado,@novo)
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'

set @errado = '<span>2°: </span>'
set @novo =   ''
update a set Layout = replace(Layout,@errado,@novo)
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'

set @errado = '>Documentos:'
set @novo =   'Documentos: {{PARCELASREFIN}}'
update a set Layout = replace(Layout,@errado,@novo)
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'


set @errado = 'Residual: R$'
set @novo =   'Residual: {{VALORLIQUIDORESIDUAL}}'
update a set Layout = replace(Layout,@errado,@novo)
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'

