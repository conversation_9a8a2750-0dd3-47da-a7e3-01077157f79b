declare @erro varchar (1000), @propostaAbasId int

BEGIN TRAN
    BEGIN TRY

    
    select @propostaAbasId=id from dbo.PropostaAbas
    where Nome = 'Arquivo'


    delete from dbo.PropostaEtapaAbas where PropostaAbaId=@propostaAbasId

    delete from dbo.PropostaAbas
    where id = @propostaAbasId

    update dbo.PropostaAbas set Nome = 'Documentação' where Nome = 'Documentos da Loja'


    COMMIT
    END TRY
    BEGIN CATCH
        
         set @erro = ERROR_MESSAGE()
            rollback
            RAISERROR(@erro,16,1)
            return
    END CATCH
