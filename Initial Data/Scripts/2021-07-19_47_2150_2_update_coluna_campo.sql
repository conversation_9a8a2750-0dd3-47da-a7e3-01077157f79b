update DicionarioDados set Campo = 'Proposta.Nome' where Tag = 'cliente.nome'
update DicionarioDados set Campo = 'Proposta.Nascimento' where Tag = 'cliente.nascimento'
update DicionarioDados set Campo = 'Proposta.Documento' where Tag = 'cliente.rg'
update DicionarioDados set Campo = 'Proposta.DocumentoEmissor' where Tag = 'cliente.rgEmissor'
update DicionarioDados set Campo = 'Proposta.DocumentoUFId' where Tag = 'cliente.rgUfId'
update DicionarioDados set Campo = 'Proposta.DocumentoEmissao' where Tag = 'cliente.rgEmissao'
update DicionarioDados set Campo = 'Proposta.Sexo' where Tag = 'cliente.sexo'
update DicionarioDados set Campo = 'Proposta.NaturalidadeCidadeId' where Tag = 'cliente.naturalidadeCidadeId'
update DicionarioDados set Campo = 'Proposta.EstadoCivil' where Tag = 'cliente.estadoCivil'
update DicionarioDados set Campo = 'Proposta.NomeMae' where Tag = 'cliente.nomeMae'
update DicionarioDados set Campo = 'Proposta.CEP' where Tag = 'endereco.cep'
update DicionarioDados set Campo = 'Proposta.Logradouro' where Tag = 'endereco.logradouro'
update DicionarioDados set Campo = 'Proposta.EnderecoNumero' where Tag = 'endereco.numero'
update DicionarioDados set Campo = 'Proposta.Complemento' where Tag = 'endereco.complemento'
update DicionarioDados set Campo = 'Proposta.Bairro' where Tag = 'endereco.bairro'
update DicionarioDados set Campo = 'Proposta.NacionalidadeId' where Tag = 'cliente.nacionalidadeId'
update DicionarioDados set Campo = 'Proposta.Email' where Tag = 'contato.email'
update DicionarioDados set Campo = 'Proposta.ProfissaoId' where Tag = 'profissional.profissaoId'
update DicionarioDados set Campo = 'Proposta.NomeConjuge' where Tag = 'cliente.nomeConjugue'
update DicionarioDados set Campo = 'Proposta.OutrasRendas' where Tag = 'profissional.outrasRendas'
update DicionarioDados set Campo = 'Proposta.Vencimento' where Tag = 'operacao.vencimento'
update DicionarioDados set Campo = 'Proposta.BancoId' where Tag = 'bancario.bancoId'
update DicionarioDados set Campo = 'Proposta.TipoModalidade' where Tag = 'bancario.conta'
update DicionarioDados set Campo = 'Proposta.TipoConta' where Tag = 'bancario.tipoConta'
update DicionarioDados set Campo = 'Proposta.Agencia' where Tag = 'bancario.agencia'
update DicionarioDados set Campo = 'Proposta.AgenciaDigito' where Tag = 'bancario.digito'
update DicionarioDados set Campo = 'Proposta.ContaNumero' where Tag = 'bancario.numero'
update DicionarioDados set Campo = 'Proposta.TipoOperacao' where Tag = 'bancario.tipoOperacao'
update DicionarioDados set Campo = 'Proposta.TempoConta' where Tag = 'bancario.tempoConta'
update DicionarioDados set Campo = 'Proposta.PIS' where Tag = 'profissional.pisPasep'
update DicionarioDados set Campo = 'Proposta.GrauInstrucaoId' where Tag = 'cliente.grauInstrucaoId'
update DicionarioDados set Campo = 'Proposta.TelefoneFixo' where Tag = 'contato.telefoneFixo'
update DicionarioDados set Campo = 'Proposta.Telefone' where Tag = 'contato.telefone'
update DicionarioDados set Campo = 'Proposta.Empresa' where Tag = 'profissional.empresa'
update DicionarioDados set Campo = 'Proposta.TempoEmprego' where Tag = 'profissional.tempoEmpregoAtual'
update DicionarioDados set Campo = 'Proposta.TelefoneRH' where Tag = 'profissional.telefoneRH'
update DicionarioDados set Campo = 'Proposta.CPF' where Tag = 'cliente.cpf'
update DicionarioDados set Campo = 'Proposta.NomeVendedor' where Tag = 'vendedor.nome'
update DicionarioDados set Campo = 'Proposta.CPFVendedor' where Tag = 'vendedor.cpf'
update DicionarioDados set Campo = 'Proposta.CelularVendedor' where Tag = 'vendedor.telefone'
update DicionarioDados set Campo = 'HistoricoChamada.DataHora' where Tag = 'historico.data'
update DicionarioDados set Campo = 'HistoricoChamada.DataHora' where Tag = 'historico.hora'
update DicionarioDados set Campo = 'HistoricoChamada.UsuarioId' where Tag = 'historico.usuario'
update DicionarioDados set Campo = 'Referencia.Nome' where Tag = 'referencia.nome'
update DicionarioDados set Campo = 'Referencia.Telefone' where Tag = 'referencia.telefone'
update DicionarioDados set Campo = 'Convenio.Nome' where Tag = 'operacao.convenioNome'
update DicionarioDados set Campo = 'Proposta.DiaRecebimento' where Tag = 'operacao.diaRecebimento'
update DicionarioDados set Campo = 'Proposta.Valor' where Tag = 'operacao.valorContratado'
update DicionarioDados set Campo = 'Proposta.TabelaJurosId' where Tag = 'operacao.tabelaJurosId'
update DicionarioDados set Campo = 'Proposta.Plano' where Tag = 'operacao.prazo'
update DicionarioDados set Campo = 'TabelaJurosValores.Juros' where Tag = 'operacao.taxa'
update DicionarioDados set Campo = 'Proposta.Prestacao' where Tag = 'operacao.prestacao'
update DicionarioDados set Campo = 'Proposta.ValorOperacao' where Tag = 'operacao.operacao'
update DicionarioDados set Campo = 'Proposta.PEP' where Tag = 'cliente.pep'
update DicionarioDados set Campo = 'Proposta.CidadeId' where Tag = 'endereco.cidadeId'
update DicionarioDados set Campo = 'Proposta.OcupacaoId' where Tag = 'profissional.ocupacaoId'
update DicionarioDados set Campo = 'Proposta.Renda' where Tag = 'profissional.renda'
update DicionarioDados set Campo = 'Proposta.OutrasRendas' where Tag = 'profissional.outrasRenda'
update DicionarioDados set Campo = 'Proposta.TipoOutrasRendas' where Tag = 'profissional.tipoOutrasRenda'
