
declare @campoId int,
        @etapaId int,
        @motivoId int

-------------------------------------------------------------------------------------------------------
select @campoId = id from DicionarioDados WHERE Descricao = 'RENDA'

select @etapaId = id from PropostaEtapa where nome = 'Aguard. An<PERSON><PERSON><PERSON>'

insert into CamposEditaveis VALUES(@campoId,4,null,1/*inclui edicao*/,0)

select @etapaId = id from PropostaEtapa where nome = 'Aguard. Cadastro'

UPDATE CamposEditaveis set tipo = 1/*inclui edicao*/ where DicionarioDadosId = @campoId and EtapaId = @etapaId

select @etapaId = id from PropostaEtapa where nome = 'Em An<PERSON>lise'

UPDATE CamposEditaveis set tipo = 1/*inclui edicao*/ where DicionarioDadosId = @campoId and EtapaId = @etapaId

select @etapaId = id from PropostaEtapa where nome = 'Proposta Pendente'

UPDATE CamposEditaveis set tipo = 1/*inclui edicao*/ where DicionarioDadosId = @campoId and EtapaId = @etapaId
-------------------------------------------------------------------------------------------------------


-------------------------------------------------------------------------------------------------------solicitado pelo amber
select @motivoId = id from PropostaMotivo where Nome = 'ATUALIZAR CALCULO CONFORME ORIENTAÇÕES INTERNAS NA PROPOSTA'

select @etapaId = id from PropostaEtapa where nome = 'Proposta Pendente'

select @campoId = id from DicionarioDados WHERE Descricao = 'RENDA'

update CamposEditaveis set MotivoId = @motivoId where EtapaId = @etapaId AND MotivoId = @motivoId
-------------------------------------------------------------------------------------------------------solicitado pelo amber

-------------------------------------------------------------------------------------------------------
declare @campos table (camposId int) 
insert into @campos values
((select id from DicionarioDados where Descricao like 'Email')),
((select id from DicionarioDados where Descricao like 'Complemento')),
((select id from DicionarioDados where Descricao like 'Telefone RH Empresa')),
((select id from DicionarioDados where Descricao like 'PIS PASEP')),
((select id from DicionarioDados where Descricao like 'Outras Rendas'))

update a  
set obrigatorio = 0
from DicionarioDados a 
join @campos b on b.camposId = a.id

delete from @campos
-------------------------------------------------------------------------------------------------------

------------------------------------------------------------------------------------------------------- 
insert into @campos values
((select id from DicionarioDados where Descricao like 'Proposta')),
((select id from DicionarioDados where Descricao like 'Produto')),
((select id from DicionarioDados where Descricao like 'Prestação')),
((select id from DicionarioDados where Descricao like 'Valor Liberado')),
((select id from DicionarioDados where Descricao like 'CPF/CNPJ')),
((select id from DicionarioDados where Descricao like 'Nascimento')),
((select id from DicionarioDados where Descricao like 'CEP')),
((select id from DicionarioDados where Descricao like 'UF')),
((select id from DicionarioDados where Descricao like 'Id Cidade')),
((select id from DicionarioDados where tag like 'bancario.tipoOperacao')),
((select id from DicionarioDados where Descricao like 'Classificação Profissional')),
((select id from DicionarioDados where Descricao like 'Nome da Matriz')),
((select id from DicionarioDados where Descricao like 'Login do Agente')),
((select id from DicionarioDados where Descricao like 'Supervisor Comercial Crefaz'))

update a  
set tipo = 1
from CamposEditaveis a 
join @campos b on b.camposId = a.DicionarioDadosId
join DicionarioDados c on c.id = a.DicionarioDadosId
where a.EtapaId is null and a.MotivoId is null

delete from @campos
-------------------------------------------------------------------------------------------------------

------------------------------------------------------------------------------------------------------- 
insert into @campos values
((select id from DicionarioDados where Descricao like 'Dia do Recebimento')),
((select id from DicionarioDados where Descricao like '1º Vencimento')),
((select id from DicionarioDados where Descricao like 'Valor Contratado')),
((select id from DicionarioDados where Descricao like 'Tabela de Juros')),
((select id from DicionarioDados where Descricao like 'Prazo')),
((select id from DicionarioDados where Descricao like 'Nome')),
((select id from DicionarioDados where Descricao like 'Sexo')),
((select id from DicionarioDados where Descricao like 'RG')),
((select id from DicionarioDados where Descricao like 'Orgão RG')),
((select id from DicionarioDados where Descricao like 'UF RG')),
((select id from DicionarioDados where Descricao like 'Data emissão')),
((select id from DicionarioDados where Descricao like 'Estado civil')),
((select id from DicionarioDados where Descricao like 'Nacionalidade')),
((select id from DicionarioDados where Descricao like 'Naturalidade')),
((select id from DicionarioDados where Descricao like 'UF Naturalidade')),
((select id from DicionarioDados where Descricao like 'Grau de Instrução')),
((select id from DicionarioDados where Descricao like 'Nome da mãe')),
((select id from DicionarioDados where Descricao like 'Email')),
((select id from DicionarioDados where Descricao like 'Complemento')),
((select id from DicionarioDados where Descricao like 'Banco')),
((select id from DicionarioDados where Descricao like 'Conta (Individual, Conjunta)')),
((select id from DicionarioDados where Descricao like 'Digito')),
((select id from DicionarioDados where Descricao like 'Tipo conta')),
((select id from DicionarioDados where Descricao like 'Tempo Conta')),
((select id from DicionarioDados where Descricao like 'Referência')),
((select id from DicionarioDados where Descricao like 'Telefone da Referência')),
((select id from DicionarioDados where Descricao like 'Tipo da Referência')),
((select id from DicionarioDados where Descricao like 'Empresa Onde Trabalha')),
((select id from DicionarioDados where Descricao like 'Profissão')),
((select id from DicionarioDados where Descricao like 'Tempo de emprego atual')),
((select id from DicionarioDados where Descricao like 'Telefone RH Empresa')),
((select id from DicionarioDados where Descricao like 'PIS PASEP')),
((select id from DicionarioDados where Descricao like 'Outras Rendas')),
((select id from DicionarioDados where Descricao like 'Tipo Outras Rendas')),
((select id from DicionarioDados where Descricao like 'Nome do Vendedor')),
((select id from DicionarioDados where Descricao like 'CPF do Vendedor')),
((select id from DicionarioDados where Descricao like 'Celular do Vendedor'))

update a  
set tipo = 1
from CamposEditaveis a 
join @campos b on b.camposId = a.DicionarioDadosId
join DicionarioDados c on c.id = a.DicionarioDadosId
where a.EtapaId is null and a.MotivoId is null

delete from @campos
-------------------------------------------------------------------------------------------------------

