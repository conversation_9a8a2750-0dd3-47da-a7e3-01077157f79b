BEGIN

    --
    -- Id do controller
    -- N<PERSON> necessário definir valor
    --
    declare @controllerId int

    --
    -- Variáveis de inserção
    -- Defina os valores do controlador caso seja inserção
    -- Defina os valores da rota para inserção de nova rota
    --
    declare @controllerName varchar(50) = 'propostas',
            @controllerUser varchar(50) = 'crefazapisqlstag',
            @controllerDesc varchar(MAX) = 'Acesso as propriedades das propostas',
            @routeName varchar(255) = 'Buscar propostas em andamento',
            @routeDesc varchar(MAX) = 'Retorna as propostas em andamento por CPF especificado',
            @route varchar(100) = '/api/proposta/proposta-em-andamento',
            @routeMethod varchar(10) = 'POST',
            @routeProcedure varchar(255) = 'controller.stpProposta',
            @routeInput varchar(MAX) = '{
                "$schema": "http://json-schema.org/draft-07/schema",
                "$id": "http://example.com/example.json",
                "type": "object",
                "title": "The root schema",
                "description": "The root schema comprises the entire JSON document.",
                "default": {},
                "examples": [
                    {
                        "cpf": "00000000001"
                    }
                ],
                "required": [
                    "cpf"
                ],
                "properties": {
                    "cpf": {
                        "$id": "#/properties/cpf",
                        "type": "string",
                        "title": "The cpf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": "",
                        "examples": [
                            "00000000001"
                        ]
                    }
                },
                "additionalProperties": true
            }',
            @routeOutput varchar(MAX) = '{}',
            @routeResponseId int = 2

    select @controllerId = Id from api.controller where [name] = @controllerName

    if (@controllerId IS NULL)
    BEGIN
        insert into api.controller
        (
            [user],
            [name],
            [description]
        )
        VALUES
        (
            @controllerUser,
            @controllerName,
            @controllerDesc
        )

        set @controllerId = @@IDENTITY
    END

    insert into api.route
    (
        [controller_id],
        [name],
        [description],
        [route],
        [method],
        [procedure],
        [input],
        [output],
        [response_type_id]
    ) values (
        @controllerId,
        @routeName,
        @routeDesc,
        @route,
        @routeMethod,
        @routeProcedure,
        @routeInput,
        @routeOutput,
        @routeResponseId
    )

END

-- mudança para criar um novo commit
