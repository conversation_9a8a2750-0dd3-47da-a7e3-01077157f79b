CREATE NONCLUSTERED INDEX [IX_PessoaFisica_CPF_includes] ON [dbo].[PessoaFisica] ([CPF]) INCLUDE ([PessoaId]) WITH (ONLINE = ON);

drop index if exists IX_Seguranca_UsuarioToken_RefreshToken_TempoExpiracao on seguranca.usuarioToken;

CREATE NONCLUSTERED INDEX [IX_Seguranca_UsuarioToken_RefreshToken_TempoExpiracao] ON [Seguranca].[UsuarioToken] ([RefreshToken], [TempoExpiracao]) INCLUDE ([Id], [UsuarioTokenId]) WITH (ONLINE = ON);

CREATE NONCLUSTERED INDEX [IX_Externo_ConsultaDetalhes_ConsultaId_InformacaoId_includes] ON [externo].[ConsultaDetalhes] ([ConsultaId], [InformacaoId]) INCLUDE ([Valor]);

CREATE NONCLUSTERED INDEX [IX_Externo_Consulta_PropostaId_Chamada] ON [externo].[Consulta] ([PropostaId], [<PERSON><PERSON>]) WITH (ONLINE = ON)
