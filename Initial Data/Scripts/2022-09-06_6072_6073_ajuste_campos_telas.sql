DECLARE @propostaetapaEmAnaliseId INT,
        @propostaetapaAtuacaoId INT,
        @validaLuzId INT,
        @classificaEnergiaId int;

--seleciona os id´s das etapas
SELECT @propostaetapaEmAnaliseId=id FROM dbo.PropostaEtapa WITH(NOLOCK) WHERE Nome = 'Em Análise'; 
SELECT @propostaetapaAtuacaoId=id FROM dbo.PropostaEtapa WITH(NOLOCK) WHERE Nome = 'Atuação Cliente'; 

--Seleciona os id´s dos dicionario de dados 
SELECT @validaLuzId  =id FROM dbo.DicionarioDados WITH(NOLOCK) WHERE Tag='validarLuzEmdia'
SELECT @classificaEnergiaId =id FROM dbo.DicionarioDados WITH(NOLOCK) WHERE Tag='classificacaoFaturaEnergia'


SELECT b.* 
INTO #camposEditaveis
FROM dbo.DicionarioDados a WITH(NOLOCK) 
JOIN dbo.CamposEditaveis b WITH(NOLOCK) ON b.DicionarioDadosId = a.Id
WHERE b.EtapaId=@propostaetapaEmAnaliseId


UPDATE #camposEditaveis
SET etapaid=@propostaetapaAtuacaoId
WHERE etapaid=@propostaetapaEmAnaliseId

--caso já exista os campos de validarLuzEmdia e classificacaoFaturaEnergia os remove da temporaria para não serem inseridos novamente
IF EXISTS(SELECT * FROM dbo.CamposEditaveis WITH(NOLOCK) WHERE EtapaId=@propostaetapaAtuacaoId AND DicionarioDadosId IN(@validaLuzId,@classificaEnergiaId))
BEGIN
	DELETE FROM #camposEditaveis
	WHERE DicionarioDadosId IN (@classificaEnergiaId,@validaLuzId)
END 



INSERT INTO dbo.CamposEditaveis
SELECT a.DicionarioDadosId,
a.EtapaId,
a.MotivoId,
a.Tipo,
a.Checagem,
a.Tela
FROM #camposEditaveis a









