insert into ConvenioDados (ConvenioId, Nome, Tipo, Ordem, Chave, ConvenioDadosCategoriaId, Formato, Calculado, Mensagem, InserirPessoaConvenioDadosAdicionais, EnviadoRMB)
-- EDP ES
select (select top 1 id from Convenio with(nolock) where nome = 'EDP ES'), Nome, Tipo, Ordem, Chave, ConvenioDadosCategoriaId, Formato, Calculado, Mensagem, InserirPessoaConvenioDadosAdicionais, EnviadoRMB
from ConvenioDados
where ConvenioId = (select top 1 id from Convenio where nome = 'ENEL RJ') -- ENEL RJ
union
-- EDP SP
select (select top 1 id from Convenio where nome = 'EDP SP'), Nome, Tipo, Ordem, Chave, ConvenioDadosCategoriaId, Formato, Calculado, Mensagem, InserirPessoaConvenioDadosAdicionais, EnviadoRMB
from ConvenioDados
where ConvenioId = (select top 1 id from Convenio where nome = 'ENEL RJ') -- ENEL RJ
