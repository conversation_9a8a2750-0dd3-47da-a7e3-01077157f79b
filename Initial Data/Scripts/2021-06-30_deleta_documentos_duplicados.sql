declare @produtoDocumento table(
    produtoId int,
    documentoId int,
    obrigatorio bit,
    impressao bit,
    etapaId int)

begin TRAN

delete from dbo.Documento where Id > 13

delete from dbo.ProdutoDocumento

dbcc checkident('ProdutoDocumento', RESEED, 0)

insert into @produtoDocumento
select distinct
    b.Id produtoId,
    d.Id documentoTipoId,
    1 obrigatorio,
    1 impressao,
    1 etapaId
from VersaoLayoutContrato a
left join Produto b on b.Id = a.ProdutoId
left join DocumentoTipo c on c.Tipo = a.TipoDocumento
left join Documento d on d.DocumentoTipoId = c.Id
left join ProdutoDocumento e on e.ProdutoId = b.Id
where e.ProdutoId Is null and a.DataFim IS NULL and d.Id IS NOT NULL order by b.Id

insert into ProdutoDocumento
select * from @produtoDocumento

COMMIT
