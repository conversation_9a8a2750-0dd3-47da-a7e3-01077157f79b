
insert into CamposEditaveis
select a.Id, b.Id, null motivoId, 0 tipo, 0 checagem, 0 from DicionarioDados a 
cross join PropostaEtapa b 
where a.Tag = 'cliente.cpf'
and b.<PERSON> in ('Falha de Comunicação', 'Seleção Oferta')

insert into CamposEditaveis
select a.Id, b.Id, null motivoId, 0 tipo, 0 checagem, 0 from DicionarioDados a 
cross join PropostaEtapa b 
where a.Tag = 'historico.dadosHistoricoCia'
and b.<PERSON> in ('Seleção Oferta')