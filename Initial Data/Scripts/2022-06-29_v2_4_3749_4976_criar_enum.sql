-- 4976: <PERSON>ada<PERSON><PERSON>'s

if not exists (select * from Parametro.enum where codigo = 'classificacao')
begin
    insert into Parametro.enum
    select 'classificacao', '[{"id":1,"nome":"Call center"},{"id":2,"nome":"<PERSON>bri<PERSON>"},{"id":3,"nome":"Presencial"}]'
end

if not exists (select * from Parametro.enum where codigo = 'certificacao')
begin
    insert into Parametro.enum
    select 'certificacao', '[{"id":1,"nome":"ABECIP"},{"id":2,"nome":"ACREFI"}]'
end

if not exists (select * from Parametro.enum where codigo = 'tipoCertificacao')
begin
    insert into Parametro.enum
    select 'tipoCertificacao', '[{"id":1,"nome":"Comple<PERSON>"},{"id":2,"nome":"Consignado"}]'
end
