update VersaoLayoutContrato set Layout = '<!DOCTYPE html>
<html>

<head>
    <style type="text/css">
        @page {
            margin: 20px 50px;
            margin-bottom: 10px;
        }

        html,
        body {
            height: 100%;
            margin: 0px;
        }

        body {
            font-family: sans-serif;
            color: #111;
        }

        h2 {
            margin: 0px;
            font-size: 15px;
            font-weight: 700;
            text-transform: uppercase;
            margin-bottom: 12px;
            text-align: center;
            width: 100%;
        }

        p {
            text-align: justify;
            font-size: 13px;
            margin: 0px;
            line-height: 1.35em;
            letter-spacing: 0.02em;
        }

        .page {
            width: 100%;
            height: 100%;
        }

        .page .header {
            margin-bottom: 15px;
        }

        .page .header img.logo {
            width: 130px;
        }

        .page p.text-initial {
            line-height: 1.5em;
            margin-bottom: 20px;
        }

        .page p.info-list {
            line-height: 1.55em;
            text-align: left;
        }

        .page p.info-list span.title {
            margin-bottom: 5px;
            display: inline-block;
        }

        table.formulario {
            border-spacing: 0px;
        }

        table.formulario tr td p {
            border-bottom: 1px solid #111;
            display: inline-block;
            width: 100%;
        }


        table.titular-conta {
            width: 100%;
            margin-top: 4px;
            margin-bottom: 10px;
        }

        table.titular-conta tr {}

        table.titular-conta tr td {}

        table.titular-conta tr td:nth-child(1) {
            width: 75%;
            padding-right: 10px;
        }

        .numero-cliente-pn {
            text-align: center;
            margin-bottom: 15px;
        }

        .numero-cliente-pn .table {
            width: 30%;
            margin: 0 20px;
            display: inline-block;
        }

        .numero-cliente-pn .table table {
            border-collapse: collapse;
            width: 100%;
        }

        .numero-cliente-pn .table table tbody {
            width: 100%;
        }

        .numero-cliente-pn .table table tr {
            width: 100%;
        }

        .numero-cliente-pn .table table tr td {
            width: 100%;
            height: 25px;
            font-size: 12.5px;
            margin: 0px;
            line-height: 1.25em;
        }

        .numero-cliente-pn .table table tr:nth-child(2) td {
            vertical-align: top;
            font-weight: 700;
            text-transform: uppercase;
        }

        table.datas {
            margin-bottom: 6px;
        }

        table.datas tr {}

        table.datas tr td {
            padding-right: 20px;
        }

        table.datas tr td span.title {
            text-transform: uppercase;
        }

        table.datas tr td span.data {
            padding: 0 8px;
        }

        table.informacoes-comprador {
            width: 100%;
            margin-bottom: 10px;
        }

        table.informacoes-comprador tr {}

        table.informacoes-comprador tr td {}

        table.informacoes-comprador tr td span.title {
            text-transform: uppercase;
        }

        table.informacoes-comprador tr:nth-child(2) td:nth-child(3) {
            width: 28%;
        }

        .filiacao {
            margin-bottom: 30px;
        }

        .filiacao h5 {
            margin: 0px;
            margin-bottom: 4px;
            font-size: 13px;
            letter-spacing: 0.02em;
            font-weight: 400;
            text-transform: uppercase;
        }

        .filiacao table {
            width: 100%;
        }

        .filiacao table tr td span.data {
            padding: 0 8px;
        }

        .filiacao table tr:nth-child(3) td {
            width: 35%;
        }

        .filiacao table tr:nth-child(3) td p {
            width: auto;
        }

        .filiacao table tr td span.title {
            text-transform: uppercase;
        }

        .signature {
            border-top: 1px solid #111;
            padding-top: 3px;
            width: 50%;
            margin-bottom: 55px;
        }

        .signature h5 {
            text-align: center;
            text-transform: uppercase;
            font-weight: 700;
            font-size: 13px;
            margin: 0px;
        }

        table.vendedor {
            width: 100%;
            margin-top: 4px;
            margin-bottom: 14px;
        }

        table.vendedor tr {}

        table.vendedor tr td {}

        table.vendedor tr td:nth-child(1) {
            width: 75%;
            padding-right: 10px;
        }

        .page h4.sac {
            margin: 0px;
            font-size: 17px;
            font-weight: 700;
            text-transform: uppercase;
            margin-bottom: 40px;
            text-align: center;
            width: 100%;
        }

        .page .footer {
            width: 100%;
            border-top: 2px solid #111;
            padding-top: 3px;
            margin-top: 10px;
        }

        .page .footer hr {
            width: 100%;
            margin: 0px;
            border: none;
            border-top: 1px solid #111;
            margin-bottom: 0px;
        }

        .page .footer p {
            font-family: serif;
            font-size: 12.5px;
            display: inline-block;
            text-transform: uppercase;
        }

        .signatureCli {
            width: 100%;
            height: 100px;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .signatureCli div {
            -webkit-transform: translateX(-50%);
        }
    </style>
</head>

<body>
    <div class="page" id="first-page">
        <div class="header"> <img alt="Embedded Image" class="logo"
                src="data:image/png;base64,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" />
        </div>
        <h2>Autorização para Débito na Fatura de Energia Elétrica</h2>
        <p class="text-initial"> Eu autorizo a a debitar mensalmente da conta de energia elétrica da qual sou titular, o
            valor de {{VALOR}} em {{PLANO}} ({{PLANOEXTENSO}}) parcela(s) de relativo a crédito pessoal. </p>
        <p class="info-list"> <span class="title">Reconheço ter ciência dos seguintes pontos:</span> <br> a) Terei o
            valor acima adicionado ao valor do meu consumo de energia elétrica;<br> b) Ao mudar-me de endereço e
            solicitar o desligamento do fornecimento de energia elétrica, deverei comunicar à Crefaz no telefone
            0800-052-5051, pois a cobrança é vinculada ao endereço informado, assim, deverei refazer a autorização para
            o novo endereço ou apresentar outra unidade consumidora;<br> c) Na situação de troca de endereço ou de
            unidade consumidora, o valor acima autorizado da parcela terá o processo de cobrança acordado entre as
            partes.<br> d) A solicitação de devolução de valores pagos indevidamente deve ser feita diretamente à
            empresa vendedora do produto ou serviço, CREFAZ SOCIEDADE DE CREDITO AO MICROEMPREENDEDOR E A EMPRESA DE
            PEQUENO PORTE LTDA - EPP CNPJ/CPF: 18.188.384/0001-83. </p>
        <table class="titular-conta formulario">
            <tr>
                <td>
                    <p> <span class="title">NOME (Titular da conta):</span> {{NOMECLIENTE}} </p>
                </td>
                <td>
                    <p> <span class="title">CPF:</span> {{CPF}} </p>
                </td>
            </tr>
        </table>
        <div class="numero-cliente-pn">
            <div class="table">
                <table border="1" cellspacing="0">
                    <tr>
                        <td>{{UNIDADECONSUMIDORA}}</td>
                    </tr>
                    <tr>
                        <td class="title">Número Cliente</td>
                    </tr>
                </table>
            </div>
            <div class="table">
                <table border="1" cellspacing="0">
                    <tr>
                        <td>{{PN}}</td>
                    </tr>
                    <tr>
                        <td class="title">PN</td>
                    </tr>
                </table>
            </div>
        </div>
        <table class="datas formulario">
            <tr>
                <td>
                    <p> <span class="title">Vencimento da Primeira Parcela</span> <span
                            class="data">{{VENCIMENTO}}</span> </p>
                </td>
                <td>
                    <p> <span class="title">Data de leitura:</span> <span class="data">{{LEITURA}}</span> </p>
                </td>
            </tr>
        </table>
        <table class="informacoes-comprador formulario">
            <tr>
                <td colspan="3">
                    <p> <span class="title">Nome do Comprador:</span> {{NOMECLIENTE}} </p>
                </td>
            </tr>
            <tr>
                <td>
                    <p> <span class="title">RG:</span> {{RG}} </p>
                </td>
                <td>
                    <p> <span class="title">CPF:</span> {{CPF}} </p>
                </td>
                <td>
                    <p> <span class="title">Dt. de Nasc.:</span> {{NASCIMENTO}} </p>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <p> <span class="title">Endereço:</span> {{ENDERECO}} </p>
                </td>
                <td>
                    <p> <span class="title">Número:</span> {{ENDERECONUMERO}} </p>
                </td>
            </tr>
            <tr>
                <td colspan="3">
                    <p> <span class="title">Ponto de Referência:</span> {{PONTOREFERENCIA}} </p>
                </td>
            </tr>
            <tr>
                <td>
                    <p> <span class="title">Bairro:</span> {{BAIRRO}} </p>
                </td>
                <td>
                    <p> <span class="title">Cidade:</span> {{CIDADE}} </p>
                </td>
                <td>
                    <p> <span class="title">CEP:</span> {{CEP}} </p>
                </td>
            </tr>
            <tr>
                <td colspan="3">
                    <p> <span class="title">Telefones para Contato:</span> {{TELEFONES}} </p>
                </td>
            </tr>
        </table>
        <div class="filiacao">
            <h5>Filiação:</h5>
            <table class="pai-mae-data-venda formulario">
                <tr>
                    <td colspan="3">
                        <p> <span class="title">Pai:</span> {{NOMEPAI}} </p>
                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <p> <span class="title">Mãe:</span> {{NOMEMAE}} </p>
                    </td>
                </tr>
                <tr>
                    <td>
                        <p> <span class="title">Data da Venda:</span> <span class="data">{{DATAVENDA}}</span> </p>
                    </td>
                </tr>
            </table>
        </div>
        <div>
            <div style="width: 50%; text-align: center;" class="signatureCli"> {{ASSINATURA DO CLIENTE}} </div>
            <div class="signature">
                <h5>Assinatura do Titular da Conta</h5>
            </div>
        </div>
        <table class="vendedor formulario">
            <tr>
                <td>
                    <p> <span class="title">Vendedor:</span> {{NOMEVENDEDOR}} </p>
                </td>
                <td>
                    <p> <span class="title">CPF:</span> {{CPFVENDEDOR}} </p>
                </td>
            </tr>
        </table>
        <h4 class="sac">SAC - Crefaz 0800-052-5051</h4>
        <div class="footer">
            <hr>
            <p>V - Out/2019 - Autorização para Débito na Fatura de Energia Elétrica</p>
        </div>
    </div>
</body>

</html>
' where ProdutoId = 12 and TipoDocumento = 'autorizacao-debito-fatura-energia'
