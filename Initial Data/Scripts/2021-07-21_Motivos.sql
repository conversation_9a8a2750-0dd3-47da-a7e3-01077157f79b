ALTER TABLE [PropostaDecisaoPropostaMotivo] NOCHECK CONSTRAINT [FK_PropostaDecisaoPropostaMotivo_PropostaMotivo]
ALTER TABLE [PropostaMotivoGrupoHistorico] NOCHECK CONSTRAINT [FK_PropostaMotivoGrupoHistorico_PropostaMotivo]
ALTER TABLE [PropostaEtapaPropostaMotivo] NOCHECK CONSTRAINT [FK_PropostaEtapaPropostaMotivo_PropostaMotivo]
delete from PropostaMotivo
DBCC CHECKIDENT('PropostaMotivo',RESEED,0)

insert into PropostaMotivo values
('ANEXAR CCB VIA NEGOCIÁVEL'),
('SEM CONTATO COM O CLIENTE'),
('COMPROVANTE DA RESIDÊNCIA'),
('CPF PENDENTE, REGULARIZAR NA RECEITA FEDERAL'),
('CLIENTE SOLICITOU O CANCELAMENTO '),
('PENDENTE DOCUMENTO'),
('EXTRATO BANCÁRIO '),
('ESPÉCIE DO BENEFICIO NÃO ATENDIDO PARA ESSE PRODUTO'),
('TELEFONE INVÁLIDO'),
('CPF DO CLIENTE INCORRETO NA CIA. VERIFICAR ORIENTAÇÕES INTERNAS NA PROPOSTA'),
('CLIENTE NÃO ESTÁ CIENTE DOS VALORES'),
('CLIENTE NÃO ESTÁ CIENTE DA ASSINATURA POR APP '),
('ASSINATURA DIVERGENTE '),
('ASSINATURA OU SELFIE FORA DOS PADRÕES, REFAZER PROCESSO NO APP'),
('ATUALIZAR CALCULO CONFORME ORIENTAÇÕES INTERNAS NA PROPOSTA'),
('CIA ELÉTRICA APRESENTA INSTABILIDADE, AGUARDANDO NORMALIZAÇÃO PARA ANÁLISE'),
('CLASSIFICAÇÃO PROFISSIONAL NÃO ELEGIVEL AO PRODUTO'),
('CLIENTE ALEGA NÃO TER ASSINADO O CONTRATO'),
('CLIENTE ANALFABETO NÃO É ELEGÍVEL A MODALIDADE DIGITAL'),
('CLIENTE COM HISTÓRICO DE QUITAÇÃO ANTECIPADA'),
('CLIENTE DEVE ENTRAR EM CONTATO COM O SAC: 0800 052 5051'),
('CLIENTE EM PROCESSO DE TROCA DE TITULARIDADE'),
('CLIENTE JÁ POSSUI PROPOSTA EM ANDAMENTO '),
('CLIENTE NÃO RESIDE NO ENDEREÇO CADASTRADO '),
('CLIENTE POSSUI EMPRÉSTIMO RECENTE'),
('CLIENTE PULOU A ETAPA DE ASSINATURA, REFAZER PROCESSO NO APP'),
('CLIENTE PULOU ETAPA DA SELFIE, REFAZER PROCESSO NO APP'),
('CLIENTE SE RECUSA A REALIZAR AS CONFIRMAÇÕES '),
('CLIENTE SEM FORNECIMENTO DE ENERGIA, SUSPENSO OU INATIVO'),
('COMPROVANTE DO TEMPO DE ATIVIDADE'),
('CONTRACHEQUE'),
('CONTRATO ANEXADO SEM ASSINATURA '),
('CONTRATO DIVERGENTE'),
('CPF SUSPENSO NA RECEITA FEDERAL'),
('DADOS BANCÁRIOS DIVERGENTES'),
('DATA DE NASCIMENTO DIVERGENTE '),
('DETALHAMENTO DE CRÉDITO'),
('FORA DA POLÍTICA DE CRÉDITO'),
('HISTÓRICO DE CONSUMO INDISPONÍVEL, ORIENTAR AO CLIENTE A ATUALIZAÇÃO DO CADASTRO JUNTO A CIA ELÉTRICA'),
('HISTÓRICO NA CIA ELÉTRICA INCOMPATÍVEL COM POLÍTICA DE CRÉDITO'),
('INCONSISTÊNCIA NA CONFIRMAÇÃO'),
('LAUDO MÉDICO'),
('MARGEM INSUFICIENTE PARA NOVO EMPRÉSTIMO'),
('PROPOSTA LIBERADA PARA ASSINATURA NO APP'),
('PROPOSTA RECUSADA NA ANÁLISE DE DOCUMENTOS'),
('SEM CONTATO COM O RH '),
('SEM CONTATO COM REFRENCIAS '),
('SOLICITADO PELA LOJA'),
('TEMPO MÍNIMO DE TRABALHO'),
('UNIDADE CONSUMIDORA INCORRETA'),
('UNIDADE CONSUMIDORA NÃO PERTENCE AO CLIENTE'),
('VALOR AJUSTADO PARA O LUZ EM DIA, VALIDAR FATURAS PARA PAGAMENTO DENTRO DA PROPOSTA')
ALTER TABLE [PropostaDecisaoPropostaMotivo] CHECK CONSTRAINT [FK_PropostaDecisaoPropostaMotivo_PropostaMotivo]
ALTER TABLE [PropostaMotivoGrupoHistorico] CHECK CONSTRAINT [FK_PropostaMotivoGrupoHistorico_PropostaMotivo]
ALTER TABLE [PropostaEtapaPropostaMotivo] CHECK CONSTRAINT [FK_PropostaEtapaPropostaMotivo_PropostaMotivo]

