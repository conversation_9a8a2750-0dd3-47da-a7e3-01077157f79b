BEGIN

    --
    -- Id do controller
    -- N<PERSON> necessário definir valor
    --
    declare @controllerId int 

    --
    -- Variáveis de inserção
    -- Defina os valores do controlador caso seja inserção
    -- Defina os valores da rota para inserção de nova rota
    --
    declare @controllerName varchar(50) = 'Blocklist',
            @controllerUser varchar(50) = 'crefazapisqlstag',
            @controllerDesc varchar(MAX) = 'Lista de motivos bloqueio',
            @routeName varchar(255) = 'Lista de Motivos Bloqueio (Unid. Consumidora)',
            @routeDesc varchar(MAX) = 'Lista de motivos bloqueio',
            @route varchar(100) = '/api/bloqueado/unidade-consumidora',
            @routeMethod varchar(10) = 'GET',
            @routeProcedure varchar(255) = 'controller.stpBlocklist',
            @routeInput varchar(MAX) = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "motivo": [
                {
                    "id": 1,
                    "nome": "motivo 1"
                }
            ]
        }
    ],
    "required": [],
    "properties": {
        "motivo": {
            "$id": "#/properties/motivo",
            "type": "array",
            "title": "The motivo schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [
                    {
                        "id": 1,
                        "nome": "motivo 1"
                    }
                ]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/motivo/items",
                "anyOf": [
                    {
                        "$id": "#/properties/motivo/items/anyOf/0",
                        "type": "object",
                        "title": "The first anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": {},
                        "examples": [
                            {
                                "id": 1,
                                "nome": "motivo 1"
                            }
                        ],
                        "required": [
                            "id",
                            "nome"
                        ],
                        "properties": {
                            "id": {
                                "$id": "#/properties/motivo/items/anyOf/0/properties/id",
                                "type": "integer",
                                "title": "The id schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1
                                ]
                            },
                            "nome": {
                                "$id": "#/properties/motivo/items/anyOf/0/properties/nome",
                                "type": "string",
                                "title": "The nome schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "motivo 1"
                                ]
                            }
                        },
                        "additionalProperties": true
                    }
                ]
            }
        }
    },
    "additionalProperties": true
}',
            @routeOutput varchar(MAX) = '{}',
            @routeResponseId int = 2

    select @controllerId = Id from api.controller where [name] = @controllerName

    if (@controllerId IS NULL)
    BEGIN
        insert into api.controller
        (
            [user],
            [name],
            [description]
        )
        VALUES
        (
            @controllerUser,
            @controllerName,
            @controllerDesc
        )

        set @controllerId = @@IDENTITY
    END

    insert into api.route
    (
        [controller_id],
        [name],
        [description],
        [route],
        [method],
        [procedure],
        [input],
        [output],
        [response_type_id]
    ) values (
        @controllerId,
        @routeName,
        @routeDesc,
        @route,
        @routeMethod,
        @routeProcedure,
        @routeInput,
        @routeOutput,
        @routeResponseId
    )

END
-- mudança para criar um novo commit