-- atualização json input
UPDATE api.route
set [input] = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "filtroNome": "Edson Alves",
            "filtroCPF": "12345678910",
            "filtroTelefone": "2733370002",
            "filtroUC": "ENEL SP",
            "ordenacao": "Nome",
            "ordenacaoAsc": false,
            "pagina": 1
        }
    ],
    "required": [
        "ordenacao",
        "ordenacaoAsc",
        "pagina"
    ],
    "properties": {
        "filtroNome": {
            "$id": "#/properties/filtroNome",
            "type": "string",
            "title": "The filtroNome schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "Edson Alves"
            ]
        },
        "filtroCPF": {
            "$id": "#/properties/filtroCPF",
            "type": "string",
            "title": "The filtroCPF schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "12345678910"
            ]
        },
        "filtroTelefone": {
            "$id": "#/properties/filtroTelefone",
            "type": "string",
            "title": "The filtroTelefone schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2733370002"
            ]
        },
        "filtroUC": {
            "$id": "#/properties/filtroUC",
            "type": "string",
            "title": "The filtroUC schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "ENEL SP"
            ]
        },
        "ordenacao": {
            "$id": "#/properties/ordenacao",
            "type": "string",
            "title": "The ordenacao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "Nome"
            ]
        },
        "ordenacaoAsc": {
            "$id": "#/properties/ordenacaoAsc",
            "type": "boolean",
            "title": "The ordenacaoAsc schema",
            "description": "An explanation about the purpose of this instance.",
            "default": false,
            "examples": [
                false
            ]
        },
        "pagina": {
            "$id": "#/properties/pagina",
            "type": "integer",
            "title": "The pagina schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        }
    },
    "additionalProperties": true
}'
where method = 'POST'
and route = '/api/Pessoa/Cadastro-Pessoa-Lista'