

declare @errado varchar(255) = 'cientede',
        @novo varchar(255) = 'ciente de'

update a 
set Layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'

set @errado = '{{}}'
set @novo = ''
update a 
set Layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'

set @errado = '( BACEN)'
set @novo = '(BACEN)'
update a 
set Layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'

set @errado = 'e sua alterações'
set @novo = 'e suas alterações'
update a 
set Layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'

set @errado = 'tmou ciência'
set @novo = 'tomou ciência'
update a 
set Layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'

set @errado = 'Importo'
set @novo = 'Imposto'
update a 
set Layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'

set @errado = 'valor do empréstimo será cobrado de'
set @novo = 'valor do empréstimo, será cobrado de'
update a 
set Layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'

