CREATE FUNCTION dbo.fncCalculoTaxaAderencia(@analistaId int)
RETURNS NUMERIC(15,2) AS
BEGIN
    declare @horaAtiva NUMERIC(15,2) = 0, @horaInativa NUMERIC(15,2) = 0, @horaTotal NUMERIC(15,2) = 0, @taxaAderencia NUMERIC(15,2) = 0

    declare @tempoAtivo as table (
        horaAtiva NUMERIC(15,2)
    )

    declare @tempoInativo as table (
        horaInativa NUMERIC(15,2)
    )

    INSERT into @tempoAtivo
    select DATEDIFF(MINUTE, Inicio, isnull(Fim, dbo.getdateBR()))
    from Operatividade where AnalistaId = @analistaId
    and Inicio >= cast(dbo.getdateBR() as date)
    ORDER BY Id

    UPDATE @tempoAtivo
    set horaAtiva = isnull(horaAtiva, 0)/60
    

    set @horaAtiva = (select sum(horaAtiva) from @tempoAtivo)


    INSERT into @tempoInativo
    select DATEDIFF(MINUTE, iif(YEAR(tempoAnterior) > '1950', tempoAnterior, Inicio), Inicio)
    from (select LAG(Fim, 1,0) OVER (ORDER BY id) as  tempoAnterior, Inicio, id
    from Operatividade where AnalistaId = @analistaId
    and Inicio >= cast(dbo.getdateBR() as date)) a
    ORDER BY Id ASC 

    UPDATE @tempoInativo
    set horaInativa = ISNULL(horaInativa, 0)/60


    set @horaInativa = (select sum(horaInativa) from @tempoInativo)

    set @horaTotal = @horaAtiva + @horaInativa

    if isnull(@horaTotal, 0) > 0
        set @taxaAderencia = @horaAtiva/@horaTotal
    ELSE
        set @taxaAderencia = 0    

    RETURN @taxaAderencia
END        