

create table UnidadeProduto(
    Id int identity(1,1),
    UnidadeId int not null, 
    ProdutoId int not null 
)

alter table UnidadeProduto add constraint PK_UnidadeProduto primary key (Id);
alter table UnidadeProduto add constraint FK_UnidadeProduto_UnidadeId foreign key (UnidadeId) references Unidade(Id);
alter table UnidadeProduto add constraint FK_UnidadeProduto_ProdutoId foreign key (ProdutoId) references Produto(Id);


create table UnidadeConvenio(
    Id int identity(1,1),
    UnidadeId int not null,
    ConvenioId int not null
)

alter table UnidadeConvenio add constraint PK_UnidadeConvenio primary key (Id);
alter table UnidadeConvenio add constraint FK_UnidadeConvenio_UnidadeId foreign key (UnidadeId) references Unidade(Id);
alter table UnidadeConvenio add constraint FK_UnidadeConvenio_ConvenioId foreign key (ConvenioId) references Convenio(Id);


create table UnidadeTabelaJuros (
    Id int identity(1,1),
    UnidadeId int not null,
    TabelaJurosId int not null
)

alter table UnidadeTabelaJuros add constraint PK_UnidadeTabelaJuros primary key (Id);
alter table UnidadeTabelaJuros add constraint FK_UnidadeTabelaJuros_UnidadeId foreign key (UnidadeId) references Unidade(Id);
alter table UnidadeTabelaJuros add constraint FK_UnidadeTabelaJuros_TabelaJurosId foreign key (TabelaJurosId) references TabelaJuros(Id);
