update api.route set [input]=
'{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "id": 176,
            "produtoId": 1,
            "convenioId": 2,
            "tabelaJurosId": 2,
            "valor": 247.86,
            "plano": 15,
            "prestacao": 1000,
            "vencimento": "2021-05-31",
            "diaRecebimento": 5,
            "renda": 1500,
            "tipoRenda": 1,
            "tipoCalculo": 0,
            "adicionais": [
                {
                    "convenioDadosId": 12,
                    "valor": "2165"
                },
                {
                    "convenioDadosId": 12,
                    "valor": "3219682"
                }
            ],
            "contratosRefin": [
                {
                    "contrato": 1525462,
                    "produto": "<PERSON>leto",
                    "saldoDevedor": 523.48,
                    "proximoVencimento": "2021-07-14T00:00:00",
                    "vencimentoUltimaParcela": "2021-12-14T00:00:00",
                    "parcelasRestantes": 6,
                    "dataContrato": "2022-03-26T00:00:00",
                    "agrupamento": 1560
                },
                {
                    "contrato": 1525496,
                    "produto": "Energia",
                    "saldoDevedor": 794.21,
                    "proximoVencimento": "2021-08-26T00:00:00",
                    "vencUltimaParcela": "2022-03-26T00:00:00",
                    "parcelasRestantes": 7,
                    "dataContrato": "2022-03-26T00:00:00",
                    "agrupamento": 1560
                }
            ]
        }
    ],
    "required": [
        "id",
        "produtoId",
        "tabelaJurosId",
        "valor",
        "plano",
        "prestacao",
        "vencimento",
        "diaRecebimento",
        "renda",
        "tipoRenda",
        "tipoCalculo"
    ],
    "properties": {
        "id": {
            "$id": "#/properties/id",
            "type": "integer",
            "title": "The id schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                176
            ]
        },
        "produtoId": {
            "$id": "#/properties/produtoId",
            "type": "integer",
            "title": "The produtoId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "convenioId": {
            "$id": "#/properties/convenioId",
            "type": "integer",
            "title": "The convenioId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                2
            ]
        },
        "tabelaJurosId": {
            "$id": "#/properties/tabelaJurosId",
            "type": "integer",
            "title": "The tabelaJurosId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                2
            ]
        },
        "valor": {
            "$id": "#/properties/valor",
            "type": "number",
            "title": "The valor schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0.0,
            "examples": [
                247.86
            ]
        },
        "plano": {
            "$id": "#/properties/plano",
            "type": "integer",
            "title": "The plano schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                15
            ]
        },
        "prestacao": {
            "$id": "#/properties/prestacao",
            "type": "integer",
            "title": "The prestacao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1000
            ]
        },
        "vencimento": {
            "$id": "#/properties/vencimento",
            "type": "string",
            "title": "The vencimento schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-05-31"
            ]
        },
        "diaRecebimento": {
            "$id": "#/properties/diaRecebimento",
            "type": "integer",
            "title": "The diaRecebimento schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                5
            ]
        },
        "renda": {
            "$id": "#/properties/renda",
            "type": "integer",
            "title": "The renda schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1500
            ]
        },
        "tipoRenda": {
            "$id": "#/properties/tipoRenda",
            "type": "integer",
            "title": "The tipoRenda schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "tipoCalculo": {
            "$id": "#/properties/tipoCalculo",
            "type": "integer",
            "title": "The tipoCalculo schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                0
            ]
        },
        "adicionais": {
            "$id": "#/properties/adicionais",
            "type": "array",
            "title": "The adicionais schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [
                    {
                        "convenioDadosId": 12,
                        "valor": "2165"
                    },
                    {
                        "convenioDadosId": 12,
                        "valor": "3219682"
                    }
                ]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/adicionais/items",
                "anyOf": [
                    {
                        "$id": "#/properties/adicionais/items/anyOf/0",
                        "type": "object",
                        "title": "The first anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": {},
                        "examples": [
                            {
                                "convenioDadosId": 12,
                                "valor": "2165"
                            }
                        ],
                        "required": [],
                        "properties": {
                            "convenioDadosId": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/convenioDadosId",
                                "type": "integer",
                                "title": "The convenioDadosId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    12
                                ]
                            },
                            "valor": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/valor",
                                "type": "string",
                                "title": "The valor schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "2165"
                                ]
                            }
                        },
                        "additionalProperties": true
                    }
                ]
            }
        },
        "contratosRefin": {
            "$id": "#/properties/contratosRefin",
            "type": "array",
            "title": "The contratosRefin schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [
                    {
                        "contrato": 1525462,
                        "produto": "Boleto",
                        "saldoDevedor": 523.48,
                        "proximoVencimento": "2021-07-14T00:00:00",
                        "vencimentoUltimaParcela": "2021-12-14T00:00:00",
                        "parcelasRestantes": 6,
                        "dataContrato": "2022-03-26T00:00:00",
                        "agrupamento": 1560
                    },
                    {
                        "contrato": 1525496,
                        "produto": "Energia",
                        "saldoDevedor": 794.21,
                        "proximoVencimento": "2021-08-26T00:00:00",
                        "vencUltimaParcela": "2022-03-26T00:00:00",
                        "parcelasRestantes": 7,
                        "dataContrato": "2022-03-26T00:00:00",
                        "agrupamento": 1560
                    }
                ]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/contratosRefin/items",
                "anyOf": [
                    {
                        "$id": "#/properties/contratosRefin/items/anyOf/0",
                        "type": "object",
                        "title": "The first anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": {},
                        "examples": [
                            {
                                "contrato": 1525462,
                                "produto": "Boleto",
                                "saldoDevedor": 523.48,
                                "proximoVencimento": "2021-07-14T00:00:00",
                                "vencimentoUltimaParcela": "2021-12-14T00:00:00",
                                "parcelasRestantes": 6,
                                "dataContrato": "2022-03-26T00:00:00",
                                "agrupamento": 1560
                            }
                        ],
                        "required": [
                            "contrato",
                            "produto",
                            "saldoDevedor",
                            "proximoVencimento",
                            "vencimentoUltimaParcela",
                            "parcelasRestantes",
                            "dataContrato",
                            "agrupamento"
                        ],
                        "properties": {
                            "contrato": {
                                "$id": "#/properties/contratosRefin/items/anyOf/0/properties/contrato",
                                "type": "integer",
                                "title": "The contrato schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1525462
                                ]
                            },
                            "produto": {
                                "$id": "#/properties/contratosRefin/items/anyOf/0/properties/produto",
                                "type": "string",
                                "title": "The produto schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "Boleto"
                                ]
                            },
                            "saldoDevedor": {
                                "$id": "#/properties/contratosRefin/items/anyOf/0/properties/saldoDevedor",
                                "type": "number",
                                "title": "The saldoDevedor schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0.0,
                                "examples": [
                                    523.48
                                ]
                            },
                            "proximoVencimento": {
                                "$id": "#/properties/contratosRefin/items/anyOf/0/properties/proximoVencimento",
                                "type": "string",
                                "title": "The proximoVencimento schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "2021-07-14T00:00:00"
                                ]
                            },
                            "vencimentoUltimaParcela": {
                                "$id": "#/properties/contratosRefin/items/anyOf/0/properties/vencimentoUltimaParcela",
                                "type": "string",
                                "title": "The vencimentoUltimaParcela schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "2021-12-14T00:00:00"
                                ]
                            },
                            "parcelasRestantes": {
                                "$id": "#/properties/contratosRefin/items/anyOf/0/properties/parcelasRestantes",
                                "type": "integer",
                                "title": "The parcelasRestantes schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    6
                                ]
                            },
                            "dataContrato": {
                                "$id": "#/properties/contratosRefin/items/anyOf/0/properties/dataContrato",
                                "type": "string",
                                "title": "The dataContrato schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "2022-03-26T00:00:00"
                                ]
                            },
                            "agrupamento": {
                                "$id": "#/properties/contratosRefin/items/anyOf/0/properties/agrupamento",
                                "type": "integer",
                                "title": "The agrupamento schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1560
                                ]
                            }
                        },
                        "additionalProperties": true
                    },
                    {
                        "$id": "#/properties/contratosRefin/items/anyOf/1",
                        "type": "object",
                        "title": "The second anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": {},
                        "examples": [
                            {
                                "contrato": 1525496,
                                "produto": "Energia",
                                "saldoDevedor": 794.21,
                                "proximoVencimento": "2021-08-26T00:00:00",
                                "vencUltimaParcela": "2022-03-26T00:00:00",
                                "parcelasRestantes": 7,
                                "dataContrato": "2022-03-26T00:00:00",
                                "agrupamento": 1560
                            }
                        ],
                        "required": [
                            "contrato",
                            "produto",
                            "saldoDevedor",
                            "proximoVencimento",
                            "vencUltimaParcela",
                            "parcelasRestantes",
                            "dataContrato",
                            "agrupamento"
                        ],
                        "properties": {
                            "contrato": {
                                "$id": "#/properties/contratosRefin/items/anyOf/1/properties/contrato",
                                "type": "integer",
                                "title": "The contrato schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1525496
                                ]
                            },
                            "produto": {
                                "$id": "#/properties/contratosRefin/items/anyOf/1/properties/produto",
                                "type": "string",
                                "title": "The produto schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "Energia"
                                ]
                            },
                            "saldoDevedor": {
                                "$id": "#/properties/contratosRefin/items/anyOf/1/properties/saldoDevedor",
                                "type": "number",
                                "title": "The saldoDevedor schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0.0,
                                "examples": [
                                    794.21
                                ]
                            },
                            "proximoVencimento": {
                                "$id": "#/properties/contratosRefin/items/anyOf/1/properties/proximoVencimento",
                                "type": "string",
                                "title": "The proximoVencimento schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "2021-08-26T00:00:00"
                                ]
                            },
                            "vencUltimaParcela": {
                                "$id": "#/properties/contratosRefin/items/anyOf/1/properties/vencUltimaParcela",
                                "type": "string",
                                "title": "The vencUltimaParcela schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "2022-03-26T00:00:00"
                                ]
                            },
                            "parcelasRestantes": {
                                "$id": "#/properties/contratosRefin/items/anyOf/1/properties/parcelasRestantes",
                                "type": "integer",
                                "title": "The parcelasRestantes schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    7
                                ]
                            },
                            "dataContrato": {
                                "$id": "#/properties/contratosRefin/items/anyOf/1/properties/dataContrato",
                                "type": "string",
                                "title": "The dataContrato schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "2022-03-26T00:00:00"
                                ]
                            },
                            "agrupamento": {
                                "$id": "#/properties/contratosRefin/items/anyOf/1/properties/agrupamento",
                                "type": "integer",
                                "title": "The agrupamento schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1560
                                ]
                            }
                        },
                        "additionalProperties": true
                    }
                ]
            }
        }
    },
    "additionalProperties": true
}'

where [route] = '/api/proposta/{id}' and [method] = 'PUT'
