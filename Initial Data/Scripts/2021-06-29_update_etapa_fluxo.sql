begin

    declare @etapaId int,
            @etapaNextId int

    select @etapaId = Id
    from dbo.PropostaEtapa
    where Nome = 'Fila Pagamento'

    select @etapaNextId = Id
    from dbo.PropostaEtapa
    where Nome = 'Pago ao Cliente'

    update dbo.PropostaEtapaFluxo
    set [RotaOrigem] = '/api/proposta/pago-ao-cliente'
    where PropostaEtapaId = @etapaId and PropostaProximaEtapaId = @etapaNextId

end
