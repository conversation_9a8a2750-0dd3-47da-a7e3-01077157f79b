-- correção de estrutura do json
update Parametro.Enum 
set Valor = '[{"id":0, "nome":"<PERSON><PERSON>"},{"id":1, "nome":"Filial"},{"id":2, "nome":"Subestabelecido"}]'
where Codigo = 'tipoUnidade'

--------
DECLARE @parametroGrupoId INT, @parametroId int
set @parametroGrupoId =  (SELECT Id from parametro.ParametroGrupo where Nome = 'Crivo - Oferta de Produtos')

INSERT into Parametro.Parametro
VALUES (@parametroGrupoId, 1, 'APROVACAO_FACILITADA', 'Aprovação facilitada', 'Aprovação facilitada de produto', 1, 'Aprovação facilitada')
set @parametroId = @@IDENTITY

insert into Parametro.ParametroHistorico
select id, dbo.getdateBR(), Valor
from Parametro.Parametro
where Id = @parametroId