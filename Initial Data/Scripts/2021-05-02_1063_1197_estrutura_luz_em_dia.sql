create table PropostaDebito (
    Id int identity(1,1) not null,
    Co<PERSON> varchar(10),
    PropostaId int not null,
    DataReferencia char(7) not null, -- "02/2021"
    DataVencimento datetime not null,
    <PERSON><PERSON> numeric(15,2) not null,
    <PERSON><PERSON><PERSON> tinyint not null, -- Enum Tipo Fatura
    Consumo numeric(7,2) not null,
    Situacao tinyint not null, -- situação fatura
    CodigoBarras varchar(50), 
    Comprovante varbinary(max),
    Descontar bit not null,
    Participa bit not null
)

alter table PropostaDebito add constraint PK_PropostaDebito primary key (Id);
alter table PropostaDebito add constraint FK_PropostaDebito_Proposta foreign key (PropostaId) references Proposta(Id);

alter table ConvenioTipo add LuzEmDia bit;
exec('update ConvenioTipo set LuzEmDia = case when Nome = ''Concessionária de Energia'' then 1 else 0 end;')

alter table Proposta add ValorOriginal numeric(15,2);
alter table Proposta add ProdutoOriginal int;
alter table Proposta add ElegivelLuzEmDia bit;