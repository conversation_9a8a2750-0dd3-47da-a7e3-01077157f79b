ALTER table PropostaAbas add <PERSON>baOrdem int

update PropostaAbas set Nome = 'Contatos' where Nome = 'Contato'

delete from PropostaEtapaAbas

delete from PropostaAbas where Nome = 'Referências'

update DicionarioDados set Tag = 'contatos.contato.email' where Tag = 'contato.email'
update DicionarioDados set Tag = 'contatos.contato.telefone' where Tag = 'contato.telefone'
update DicionarioDados set Tag = 'contatos.contato.telefoneFixo' where Tag = 'contato.telefoneFixo'

update DicionarioDados set Tag = 'contatos.referencia.nome' where Tag = 'referencia.nome'
update DicionarioDados set Tag = 'contatos.referencia.telefone' where Tag = 'referencia.telefone'
update DicionarioDados set Tag = 'contatos.referencia.tipoDeContato' where Tag = 'referencia.tipoDeContato'
