insert into dbo.Endereco (PessoaId, T<PERSON>o, CEP, UFId, CidadeId, Bairro, Logradouro, Numero, Complemento, TipoResidencia, TempoResidencia) values
(17, 1, '95012580', 21, 2959, '<PERSON><PERSON>', '<PERSON><PERSON>', 8219021, 'loja 3101', 1, '2020-05-05'), 
(20, 1, '57018465', 8, 1905, '<PERSON> J<PERSON>ira', '<PERSON><PERSON>', 821, 'loja 101', 1, '2020-05-05')

insert into dbo.Unidade (CorrespondenteId, Tipo, CEP, CidadeId, Bairro, Logradouro, Numero, Complemento) values 
(1, 0, '57018465', 1905, '<PERSON>', '<PERSON><PERSON>', 821, 'loja 101')

update Seguranca.Usuario set CorrespondenteId = 1, UnidadeId = @@IDENTITY where id in (1,3,5,7)

insert into dbo.Unidade (CorrespondenteId, <PERSON><PERSON><PERSON>, CE<PERSON>, <PERSON><PERSON>de<PERSON><PERSON>, Bairro, Logradouro, Numero, Complemento) values 
(2, 0, '95012580', 2959, '<PERSON><PERSON>', '<PERSON><PERSON>', 8219021, 'loja 3101')

update Seguranca.Usuario set CorrespondenteId = 2, UnidadeId = @@IDENTITY where id in (2,4,6)

update dbo.proposta set UnidadeId = id%2+1
update dbo.proposta set UsuarioId = 1 where id%2 = 0
update dbo.proposta set UsuarioId = 6 where id%2 = 1