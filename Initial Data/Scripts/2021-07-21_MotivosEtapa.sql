declare @decisaoId int,
        @etapaId int


ALTER TABLE [PropostaEtapaPropostaMotivo] NOCHECK CONSTRAINT [FK_PropostaEtapaPropostaMotivo_PropostaMotivo]
delete from PropostaDecisaoPropostaMotivo
DBCC CHECKIDENT('PropostaDecisaoPropostaMotivo',RESEED,0)

select @decisaoId = id from PropostaDecisao where Nome = 'Negada'

insert into PropostaDecisaoPropostaMotivo values
(@decisaoId, 1),
(@decisaoId, 2),
(@decisaoId, 3),
(@decisaoId, 4),
(@decisaoId, 5),
(@decisaoId, 6),
(@decisaoId, 7),
(@decisaoId, 8),
(@decisaoId, 9),
(@decisaoId, 10),
(@decisaoId, 11),
(@decisaoId, 12),
(@decisaoId, 13),
(@decisaoId, 14),
(@decisaoId, 15),
(@decisaoId, 16),
(@decisaoId, 17),
(@decisaoId, 18),
(@decisaoId, 19),
(@decisaoId, 20),
(@decisaoId, 21),
(@decisaoId, 22),
(@decisaoId, 23),
(@decisaoId, 24),
(@decisaoId, 25),
(@decisaoId, 26),
(@decisaoId, 27),
(@decisaoId, 28),
(@decisaoId, 29),
(@decisaoId, 30),
(@decisaoId, 31),
(@decisaoId, 32),
(@decisaoId, 33),
(@decisaoId, 34),
(@decisaoId, 35),
(@decisaoId, 36),
(@decisaoId, 37),
(@decisaoId, 38),
(@decisaoId, 39),
(@decisaoId, 40),
(@decisaoId, 41),
(@decisaoId, 42),
(@decisaoId, 43),
(@decisaoId, 44),
(@decisaoId, 45),
(@decisaoId, 46),
(@decisaoId, 47),
(@decisaoId, 48),
(@decisaoId, 49),
(@decisaoId, 50),
(@decisaoId, 51),
(@decisaoId, 52)



select @decisaoId = id from PropostaDecisao where Nome = 'Cancelada'

insert into PropostaDecisaoPropostaMotivo values
(@decisaoId, 1),
(@decisaoId, 2),
(@decisaoId, 3),
(@decisaoId, 4),
(@decisaoId, 5),
(@decisaoId, 6),
(@decisaoId, 7),
(@decisaoId, 8),
(@decisaoId, 9),
(@decisaoId, 10),
(@decisaoId, 11),
(@decisaoId, 12),
(@decisaoId, 13),
(@decisaoId, 14),
(@decisaoId, 15),
(@decisaoId, 16),
(@decisaoId, 17),
(@decisaoId, 18),
(@decisaoId, 19),
(@decisaoId, 20),
(@decisaoId, 21),
(@decisaoId, 22),
(@decisaoId, 23),
(@decisaoId, 24),
(@decisaoId, 25),
(@decisaoId, 26),
(@decisaoId, 27),
(@decisaoId, 28),
(@decisaoId, 29),
(@decisaoId, 30),
(@decisaoId, 31),
(@decisaoId, 32),
(@decisaoId, 33),
(@decisaoId, 34),
(@decisaoId, 35),
(@decisaoId, 36),
(@decisaoId, 37),
(@decisaoId, 38),
(@decisaoId, 39),
(@decisaoId, 40),
(@decisaoId, 41),
(@decisaoId, 42),
(@decisaoId, 43),
(@decisaoId, 44),
(@decisaoId, 45),
(@decisaoId, 46),
(@decisaoId, 47),
(@decisaoId, 48),
(@decisaoId, 49),
(@decisaoId, 50),
(@decisaoId, 51),
(@decisaoId, 52)

select @etapaId = id from PropostaEtapa where Nome = 'Falha de Comunicação'

insert into PropostaEtapaPropostaMotivo values
(@etapaId, 1),
(@etapaId, 2),
(@etapaId, 3),
(@etapaId, 4),
(@etapaId, 5),
(@etapaId, 6),
(@etapaId, 7),
(@etapaId, 8),
(@etapaId, 9),
(@etapaId, 10),
(@etapaId, 11),
(@etapaId, 12),
(@etapaId, 13),
(@etapaId, 14),
(@etapaId, 15),
(@etapaId, 16),
(@etapaId, 17),
(@etapaId, 18),
(@etapaId, 19),
(@etapaId, 20),
(@etapaId, 21),
(@etapaId, 22),
(@etapaId, 23),
(@etapaId, 24),
(@etapaId, 25),
(@etapaId, 26),
(@etapaId, 27),
(@etapaId, 28),
(@etapaId, 29),
(@etapaId, 30),
(@etapaId, 31),
(@etapaId, 32),
(@etapaId, 33),
(@etapaId, 34),
(@etapaId, 35),
(@etapaId, 36),
(@etapaId, 37),
(@etapaId, 38),
(@etapaId, 39),
(@etapaId, 40),
(@etapaId, 41),
(@etapaId, 42),
(@etapaId, 43),
(@etapaId, 44),
(@etapaId, 45),
(@etapaId, 46),
(@etapaId, 47),
(@etapaId, 48),
(@etapaId, 49),
(@etapaId, 50),
(@etapaId, 51),
(@etapaId, 52)

select @etapaId = id from PropostaEtapa where Nome = 'Seleção Oferta'


insert into PropostaEtapaPropostaMotivo values
(@etapaId, 1),
(@etapaId, 2),
(@etapaId, 3),
(@etapaId, 4),
(@etapaId, 5),
(@etapaId, 6),
(@etapaId, 7),
(@etapaId, 8),
(@etapaId, 9),
(@etapaId, 10),
(@etapaId, 11),
(@etapaId, 12),
(@etapaId, 13),
(@etapaId, 14),
(@etapaId, 15),
(@etapaId, 16),
(@etapaId, 17),
(@etapaId, 18),
(@etapaId, 19),
(@etapaId, 20),
(@etapaId, 21),
(@etapaId, 22),
(@etapaId, 23),
(@etapaId, 24),
(@etapaId, 25),
(@etapaId, 26),
(@etapaId, 27),
(@etapaId, 28),
(@etapaId, 29),
(@etapaId, 30),
(@etapaId, 31),
(@etapaId, 32),
(@etapaId, 33),
(@etapaId, 34),
(@etapaId, 35),
(@etapaId, 36),
(@etapaId, 37),
(@etapaId, 38),
(@etapaId, 39),
(@etapaId, 40),
(@etapaId, 41),
(@etapaId, 42),
(@etapaId, 43),
(@etapaId, 44),
(@etapaId, 45),
(@etapaId, 46),
(@etapaId, 47),
(@etapaId, 48),
(@etapaId, 49),
(@etapaId, 50),
(@etapaId, 51),
(@etapaId, 52)

select @etapaId = id from PropostaEtapa where Nome = 'Digitando'


insert into PropostaEtapaPropostaMotivo values
(@etapaId, 1),
(@etapaId, 2),
(@etapaId, 3),
(@etapaId, 4),
(@etapaId, 5),
(@etapaId, 6),
(@etapaId, 7),
(@etapaId, 8),
(@etapaId, 9),
(@etapaId, 10),
(@etapaId, 11),
(@etapaId, 12),
(@etapaId, 13),
(@etapaId, 14),
(@etapaId, 15),
(@etapaId, 16),
(@etapaId, 17),
(@etapaId, 18),
(@etapaId, 19),
(@etapaId, 20),
(@etapaId, 21),
(@etapaId, 22),
(@etapaId, 23),
(@etapaId, 24),
(@etapaId, 25),
(@etapaId, 26),
(@etapaId, 27),
(@etapaId, 28),
(@etapaId, 29),
(@etapaId, 30),
(@etapaId, 31),
(@etapaId, 32),
(@etapaId, 33),
(@etapaId, 34),
(@etapaId, 35),
(@etapaId, 36),
(@etapaId, 37),
(@etapaId, 38),
(@etapaId, 39),
(@etapaId, 40),
(@etapaId, 41),
(@etapaId, 42),
(@etapaId, 43),
(@etapaId, 44),
(@etapaId, 45),
(@etapaId, 46),
(@etapaId, 47),
(@etapaId, 48),
(@etapaId, 49),
(@etapaId, 50),
(@etapaId, 51),
(@etapaId, 52)

select @etapaId = id from PropostaEtapa where Nome = 'Em Análise'


insert into PropostaEtapaPropostaMotivo values
(@etapaId, 1),
(@etapaId, 2),
(@etapaId, 3),
(@etapaId, 4),
(@etapaId, 5),
(@etapaId, 6),
(@etapaId, 7),
(@etapaId, 8),
(@etapaId, 9),
(@etapaId, 10),
(@etapaId, 11),
(@etapaId, 12),
(@etapaId, 13),
(@etapaId, 14),
(@etapaId, 15),
(@etapaId, 16),
(@etapaId, 17),
(@etapaId, 18),
(@etapaId, 19),
(@etapaId, 20),
(@etapaId, 21),
(@etapaId, 22),
(@etapaId, 23),
(@etapaId, 24),
(@etapaId, 25),
(@etapaId, 26),
(@etapaId, 27),
(@etapaId, 28),
(@etapaId, 29),
(@etapaId, 30),
(@etapaId, 31),
(@etapaId, 32),
(@etapaId, 33),
(@etapaId, 34),
(@etapaId, 35),
(@etapaId, 36),
(@etapaId, 37),
(@etapaId, 38),
(@etapaId, 39),
(@etapaId, 40),
(@etapaId, 41),
(@etapaId, 42),
(@etapaId, 43),
(@etapaId, 44),
(@etapaId, 45),
(@etapaId, 46),
(@etapaId, 47),
(@etapaId, 48),
(@etapaId, 49),
(@etapaId, 50),
(@etapaId, 51),
(@etapaId, 52)

ALTER TABLE [PropostaDecisaoPropostaMotivo] CHECK CONSTRAINT [FK_PropostaDecisaoPropostaMotivo_PropostaMotivo]


delete from PropostaEtapaAbas where PropostaAbaId = (select id from PropostaAbas where Nome = 'Resumo Contato') and EtapaId not in (7,8)