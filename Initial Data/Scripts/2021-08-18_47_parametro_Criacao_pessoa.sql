declare @valor varchar(max)
declare @tipo tinyint -- 1 string, 2 inteiro, 3 numero e 4 data
declare @ParametroGrupo int


set @valor = try_cast((select Id from PropostaEtapa where Nome = 'Fila Pagamento') as varchar)


update Parametro.Parametro 
set Nome = 'Etapa criação Pessoa',
    NotaTecnica = 'Código da Etapa em que ocorre a criação/atualização da Pessoa associada a uma proposta',
    Valor = @valor
where 
    Codigo = 'CODIGO_ETAPA_CRIA_PESSOA'

insert into Parametro.ParametroHistorico 
select id, dbo.getdateBR(),@valor
from Parametro.Parametro
where 
    Codigo = 'CODIGO_ETAPA_CRIA_PESSOA'


set @valor = try_cast((select Id from PropostaEtapa where Nome = 'Aguard. Assinatura') as varchar)
set @tipo  = 2
set @ParametroGrupo = (select Id from Parametro.ParametroGrupo where Nome = 'Comercial')
insert into Parametro.Parametro values (@ParametroGrupo,@tipo,'CODIGO_ETAPA_CRIA_PESSOA_APP','Etapa criação Pessoa/usuario app','Código da Etapa em que ocorre a criação/atualização da Pessoa associada a uma proposta e caso a proposta for da modalidade Digital, também é criado o usuário do app e o disparo do SMS',1,@valor)
insert into Parametro.ParametroHistorico values (@@IDENTITY, dbo.getdateBR(),@valor)
