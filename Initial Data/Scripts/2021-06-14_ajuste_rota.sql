UPDATE api.route set [route] = '/api/proposta/oferta-produto/{id}' where [route] = '/api/proposta/oferta-produto/{consultaCrivoId}'
UPDATE api.route set [input] = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "renda": 1500,
            "produtoId": 1,
            "propostaId": 336
        }
    ],
    "required": [
        "renda",
        "produtoId",
        "propostaId"
    ],
    "properties": {
        "renda": {
            "$id": "#/properties/renda",
            "type": "integer",
            "title": "The renda schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1500
            ]
        },
        "produtoId": {
            "$id": "#/properties/produtoId",
            "type": "integer",
            "title": "The produtoId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "propostaId": {
            "$id": "#/properties/propostaId",
            "type": "integer",
            "title": "The propostaId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                336
            ]
        }
    },
    "additionalProperties": true
}' where [route] = '/api/proposta/limite-parcela'