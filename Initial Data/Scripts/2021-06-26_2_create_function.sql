create FUNCTION dbo.fncCalculoTempoEspera(@analistaId int, @PropostaId int, @tipo int = 1)
RETURNS varchar AS
BEGIN
    declare @dateTimeDistribuicao datetime,
            @dateTimeAbertura datetime

    select @dateTimeDistribuicao = min([Data]) from PropostaStatusHistorico a with(nolock) where a.PropostaId = @PropostaId and AnalistaId = @analistaId
    select @dateTimeAbertura = min([DataAcao]) from PropostaStatusHistorico a with(nolock) where a.PropostaId = @PropostaId and AnalistaId = @analistaId

    if(@tipo = 1) --retorna varchar no formato time
    begin
        if(@dateTimeAbertura is null)
        begin
            return cast(cast(cast(DATEDIFF(minute,@dateTimeDistribuicao,@dateTimeAbertura)/60 as varchar) +':'+ cast(DATEDIFF(minute,@dateTimeDistribuicao,@dateTimeAbertura)%60 as varchar) as time) as varchar)
        end
        else
        begin
            return cast(cast(cast(DATEDIFF(minute,@dateTimeDistribuicao,dbo.getdateBR())/60 as varchar) +':'+ cast(DATEDIFF(minute,@dateTimeDistribuicao,dbo.getdateBR())%60 as varchar) as time) as varchar)
        end
    end
    if(@tipo = 2) --retorna diferença de minutos
    begin
        if(@dateTimeAbertura is null)
        begin
            return cast(DATEDIFF(minute,@dateTimeDistribuicao,@dateTimeAbertura) as varchar)


        end
        else
        begin
            return cast(DATEDIFF(minute,@dateTimeDistribuicao,dbo.getdateBR()) as varchar)
        end
    end

    return null
END
