update dbo.Documento set DocumentoTipoId = (select top 1 id from dbo.DocumentoTipo where Tipo = 'boleto')

insert into ProdutoDocumento (ProdutoId, DocumentoId, Obrigatorio, Impressao, EtapaId) values 
((select top 1 id from Produto where Nome = '<PERSON><PERSON><PERSON>'), (select top 1 id from Documento where Nome = 'FICHA CADASTRAL'), 1, 1, 1),
((select top 1 id from Produto where Nome = '<PERSON><PERSON><PERSON>'), (select top 1 id from Documento where Nome = 'CCB NEGOCIÁVEL'), 1, 1, 1),
((select top 1 id from Produto where Nome = '<PERSON><PERSON><PERSON>'), (select top 1 id from Documento where Nome = 'CCB NÃO NEGOCIÁVEL'), 1, 1, 1)