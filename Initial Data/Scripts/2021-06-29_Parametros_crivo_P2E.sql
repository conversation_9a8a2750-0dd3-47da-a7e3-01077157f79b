declare @id int, @valor varchar(255), @GrupoId int

set @GrupoId = (select id from Parametro.ParametroGrupo where nome = 'Crivo - Oferta de Produtos')

set @valor = '"Driver"."Minhas Variaveis"."---- Política - P2 - Energia ----"."V_Limite_Máximo_R$"'
insert into Parametro.Parametro (ParametroGrupoId, Tipo, Codigo, Nome, NotaTecnica, Ordem, Valor) values
(@GrupoId,1,'V_Limite_Máximo_R$','V Limite Máximo R$','Retorna o limite max R$ luz em dia',1,@valor)
set @id = @@IDENTITY
 
insert into Parametro.ParametroHistorico values (@id,dbo.getdateBR(),@valor)

set @valor = '"Driver"."Minhas Variaveis"."---- Política - P2 - Energia ----"."V_Limite_Máximo_%"'
insert into Parametro.Parametro (ParametroGrupoId, Tipo, Codigo, Nome, NotaTecnica, Ordem, Valor) values
(@GrupoId,1,'V_Limite_Máximo_%','V Limite Máximo %','Retorna o limite max % luz em dia',1,@valor)
set @id = @@IDENTITY
 
insert into Parametro.ParametroHistorico values (@id,dbo.getdateBR(),@valor)

set @valor = '"Driver"."Minhas Variaveis"."---- Política - P2 - Energia ----"."V_Flag_Luz_em_Dia"'
insert into Parametro.Parametro (ParametroGrupoId, Tipo, Codigo, Nome, NotaTecnica, Ordem, Valor) values
(@GrupoId,1,'V_Flag_Luz_em_Dia','V Flag Luz em Dia','Retorna flag luz em dia',1,@valor)
set @id = @@IDENTITY
 
insert into Parametro.ParametroHistorico values (@id,dbo.getdateBR(),@valor)

set @valor = '"Driver"."Minhas Variaveis"."---- Política - P2 - Energia ----"."V_Qtde_Máxima_Fatura"'
insert into Parametro.Parametro (ParametroGrupoId, Tipo, Codigo, Nome, NotaTecnica, Ordem, Valor) values
(@GrupoId,1,'V_Qtde_Máxima_Fatura','V Qtde Máxima Fatura','Retorna quantidade max faturas em abeto luz em dia',1,@valor)
set @id = @@IDENTITY
 
insert into Parametro.ParametroHistorico values (@id,dbo.getdateBR(),@valor)