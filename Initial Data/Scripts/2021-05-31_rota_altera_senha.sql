BEGIN

    --
    -- Id do controller
    -- Não necessário definir valor
    --
    declare @controllerId int 

    --
    -- Variáveis de inserção
    -- Defina os valores do controlador caso seja inserção
    -- Defina os valores da rota para inserção de nova rota
    --
    declare @controllerName varchar(50) = 'Usuario',
            @controllerUser varchar(50) = 'crefazapisqlstag',
            @controllerDesc varchar(MAX) = 'Acesso as propriedades do usuario do aplicativo',
            @routeName varchar(255) = 'Altera Senha',
            @routeDesc varchar(MAX) = 'Altera Senha de usuario',
            @route varchar(100) = '/api/usuario-app/altera-senha',
            @routeMethod varchar(10) = 'PUT',
            @routeProcedure varchar(255) = 'controller.stpUsuario',
            @routeInput varchar(MAX) = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "cpf": "35867200175",
            "senha": "SenhaPadrao123"
        }
    ],
    "required": [
        "cpf",
        "senha"
    ],
    "properties": {
        "cpf": {
            "$id": "#/properties/cpf",
            "type": "string",
            "title": "The cpf schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "35867200175"
            ]
        },
        "senha": {
            "$id": "#/properties/senha",
            "type": "string",
            "title": "The senha schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "SenhaPadrao123"
            ]
        }
    },
    "additionalProperties": true
}',
            @routeOutput varchar(MAX) = '{}',
            @routeResponseId int = 2

    select @controllerId = Id from api.controller where [name] = @controllerName

    if (@controllerId IS NULL)
    BEGIN
        insert into api.controller
        (
            [user],
            [name],
            [description]
        )
        VALUES
        (
            @controllerUser,
            @controllerName,
            @controllerDesc
        )

        set @controllerId = @@IDENTITY
    END

    insert into api.route
    (
        [controller_id],
        [name],
        [description],
        [route],
        [method],
        [procedure],
        [input],
        [output],
        [response_type_id]
    ) values (
        @controllerId,
        @routeName,
        @routeDesc,
        @route,
        @routeMethod,
        @routeProcedure,
        @routeInput,
        @routeOutput,
        @routeResponseId
    )

END
-- mudança para criar um novo commit