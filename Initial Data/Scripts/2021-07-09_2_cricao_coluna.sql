update PropostaEtapa set ExibicaoAPP = 'Falha de Comunicação' where Nome = 'Falha de Comunicação'

update PropostaEtapa set ExibicaoAPP = 'Em Análise' where Nome in 
('An<PERSON><PERSON><PERSON> Promotora',
'<PERSON><PERSON>ção Oferta',
'<PERSON><PERSON><PERSON><PERSON>',
'<PERSON>guardando Análise',
'<PERSON> Análise',
'Pendente Análise',
'Fila Contato',
'Em Contato',
'Pendente Contato')

update PropostaEtapa set ExibicaoAPP = 'Aguardando Assinatura' where Nome in 
('Aguardando Assinatura',
'Aguardando validação',
'Análise validação',
'Pendente validação')

update PropostaEtapa set ExibicaoAPP = 'Aguardando Validação' where Nome = 'Fila Pagamento'

update PropostaEtapa set ExibicaoAPP = 'Pago ao Cliente' where Nome = 'Pago ao Cliente'

update Produto set DisponivelImpressao = 1 where Nome in ('Boleto',
'CDC',
'Energia',
'CP Refin',
'CP Cheque')

update <PERSON>du<PERSON> set DisponivelImpressao = 0 where Nome in (
'Consignado Privado',
'Luz em Dia')