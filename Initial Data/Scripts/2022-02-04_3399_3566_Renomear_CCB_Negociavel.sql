
declare @ViaNegociavel_CCB int , @CCB_Negociavel int 

select @ViaNegociavel_CCB = id from documento where nome = 'VIA NEGOCIAVEL CCB'
select @CCB_Negociavel = id from documento where nome = 'CCB NEGOCIÁVEL'


update  b
set b.Obrigatorio = a.<PERSON> , b.<PERSON><PERSON>ress<PERSON> = a.<PERSON>, b.EtapaId = a.<PERSON>d , b.TipoModalidade = a.Tipo<PERSON>odalidade , 
    b.Tipo<PERSON><PERSON>a = a.TipoRenda, b.Visivel = a.Visivel , b.ImpressaoDispositivo = a.ImpressaoDispositivo
from ProdutoDocumento a 
join ProdutoDocumento b on a.ProdutoId = b.ProdutoId
where a.DocumentoId = @ViaNegociavel_CCB
and b.DocumentoId = @CCB_Negociavel

update b 
set b.tipo = a.Tipo , b.DocumentoTipoId = a.DocumentoTipoId 
from Documento a
cross join Documento b  
where a.id = @ViaNegociavel_CCB and b.id = @CCB_Negociavel

update PropostaImagem
set DocumentoId = @CCB_Negociavel
where  DocumentoId = @ViaNegociavel_CCB


delete from ProdutoDocumento where DocumentoId = @ViaNegociavel_CCB
delete from documento where id   = @ViaNegociavel_CCB
