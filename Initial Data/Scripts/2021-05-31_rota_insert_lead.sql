BEGIN

    --
    -- Id do controller
    -- Não necessário definir valor
    --
    declare @controllerId int 

    --
    -- Variáveis de inserção
    -- Defina os valores do controlador caso seja inserção
    -- Defina os valores da rota para inserção de nova rota
    --
    declare @controllerName varchar(50) = 'Usuario',
            @controllerUser varchar(50) = 'crefazapisqlstag',
            @controllerDesc varchar(MAX) = 'Acesso as propriedades do usuario do aplicativo',
            @routeName varchar(255) = 'Cadastro Lead',
            @routeDesc varchar(MAX) = 'Realista novo cadastro na tabela de Lead',
            @route varchar(100) = '/api/usuario-app/cadastro-lead',
            @routeMethod varchar(10) = 'POST',
            @routeProcedure varchar(255) = 'controller.stpUsuario',
            @routeInput varchar(MAX) = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "nome": "Manuela Alana Caldeira",
            "telefone": "6738812025",
            "propostaId": null,
            "cpf": "33645382550",
            "pessoaId": null
        }
    ],
    "required": [
        "nome",
        "telefone",
        "propostaId",
        "cpf",
        "pessoaId"
    ],
    "properties": {
        "nome": {
            "$id": "#/properties/nome",
            "type": "string",
            "title": "The nome schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "Manuela Alana Caldeira"
            ]
        },
        "telefone": {
            "$id": "#/properties/telefone",
            "type": "string",
            "title": "The telefone schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "6738812025"
            ]
        },
        "propostaId": {
            "$id": "#/properties/propostaId",
            "type": "null",
            "title": "The propostaId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": null,
            "examples": [
                null
            ]
        },
        "cpf": {
            "$id": "#/properties/cpf",
            "type": "string",
            "title": "The cpf schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "33645382550"
            ]
        },
        "pessoaId": {
            "$id": "#/properties/pessoaId",
            "type": "null",
            "title": "The pessoaId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": null,
            "examples": [
                null
            ]
        }
    },
    "additionalProperties": true
}',
            @routeOutput varchar(MAX) = '{}',
            @routeResponseId int = 2

    select @controllerId = Id from api.controller where [name] = @controllerName

    if (@controllerId IS NULL)
    BEGIN
        insert into api.controller
        (
            [user],
            [name],
            [description]
        )
        VALUES
        (
            @controllerUser,
            @controllerName,
            @controllerDesc
        )

        set @controllerId = @@IDENTITY
    END

    insert into api.route
    (
        [controller_id],
        [name],
        [description],
        [route],
        [method],
        [procedure],
        [input],
        [output],
        [response_type_id]
    ) values (
        @controllerId,
        @routeName,
        @routeDesc,
        @route,
        @routeMethod,
        @routeProcedure,
        @routeInput,
        @routeOutput,
        @routeResponseId
    )

END
-- mudança para criar um novo commit