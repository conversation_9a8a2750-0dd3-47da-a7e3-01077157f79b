update PropostaAbas set tag = 'cliente' where Nome = 'Dad<PERSON>'
update PropostaAbas set tag = 'anexo' where Nome = 'Documentação'
update PropostaAbas set tag = 'operacao' where Nome = 'Operação'
update PropostaAbas set tag = 'debitosConveniada' where Nome = 'Histórico CIA'
update PropostaAbas set tag = 'contatos' where Nome = 'Contatos'
update PropostaAbas set tag = 'historicoChamada' where Nome = 'Chamadas'
update PropostaAbas set tag = 'endereco' where Nome = 'Endereço'
update PropostaAbas set tag = 'bancario' where Nome = 'Bancários'
update PropostaAbas set tag = 'profissional' where Nome = 'Profissional'
update PropostaAbas set tag = 'unidade' where Nome = 'Unidade'
update PropostaAbas set tag = 'resumo' where Nome = 'Resumo Contato'