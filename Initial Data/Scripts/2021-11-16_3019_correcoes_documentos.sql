declare @errado varchar(255) = 'Cédula de Crédito Bancário N°</h4>',
        @novo varchar(255) = 'Cédula de Crédito Bancário N° {{CONTRATO}}</h4>'

update a set Layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'

set @errado = '<span class="title">Local:</span>'
set @novo =   '<span class="title">Local: {{LOCAL}}</span>'
update a set Layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'

set @errado = '<span class="title">Data:</span>'
set @novo =   '<span class="title">Data: {{DATA}}</span>'
update a set Layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'
set @errado = 'dirigdas'
set @novo =   'dirigidas'
update a set Layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'

set @errado = 'Origatória'
set @novo =   'Obrigatória'
update a set Layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'

set @errado = 'CARATERÍSTICAS'
set @novo =   'CARACTERÍSTICAS'
update a set Layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'

set @errado = 'CARATERÍSTICAS'
set @novo =   'CARACTERÍSTICAS'
update a set Layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'

set @errado = 'relativo à'
set @novo =   'relativo a'
update a set Layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'


