ALTER TABLE Proposta
DROP CONSTRAINT FK_Proposta_Profissao;

ALTER TABLE Renda
DROP CONSTRAINT FK_Renda_Profissao;
--SELECT * FROM Profissao
delete Profissao
DBCC CHECKIDENT('Profissao',RESEED,0)


Declare @profissoes varchar(max) = '[
  {
    "Id": 1,
    "Nome": "ABATEDOR",
    "Ativo": "1"
  },
  {
    "Id": 2,
    "Nome": "ACOMPANHANTE",
    "Ativo": "1"
  },
  {
    "Id": 3,
    "Nome": "AÇOUGUEIRO",
    "Ativo": "1"
  },
  {
    "Id": 4,
    "Nome": "ACUPUNTURISTA",
    "Ativo": "1"
  },
  {
    "Id": 5,
    "Nome": "ADESTRADOR",
    "Ativo": "1"
  },
  {
    "Id": 6,
    "Nome": "ADMINISTRADOR",
    "Ativo": "1"
  },
  {
    "Id": 7,
    "Nome": "ADMINISTRADOR DE COMARCA",
    "Ativo": "1"
  },
  {
    "Id": 8,
    "Nome": "ADMINISTRADOR DE TRIBUNAL",
    "Ativo": "1"
  },
  {
    "Id": 9,
    "Nome": "ADVOGADO",
    "Ativo": "1"
  },
  {
    "Id": 10,
    "Nome": "AGENCIADOR DE PROPAGANDA",
    "Ativo": "1"
  },
  {
    "Id": 11,
    "Nome": "AGENTE COMUNITÁRIO DE SAÚDE",
    "Ativo": "1"
  },
  {
    "Id": 12,
    "Nome": "AGENTE DE SERVIÇOS FUNERÁRIOS E EMBALSAMADOR",
    "Ativo": "1"
  },
  {
    "Id": 13,
    "Nome": "AGENTE DE VIAGEM",
    "Ativo": "1"
  },
  {
    "Id": 14,
    "Nome": "AGENTE OU ASSISTENTE ADMINISTRATIVO",
    "Ativo": "1"
  },
  {
    "Id": 15,
    "Nome": "AGRICULTOR",
    "Ativo": "1"
  },
  {
    "Id": 16,
    "Nome": "AGRIMENSOR",
    "Ativo": "1"
  },
  {
    "Id": 17,
    "Nome": "AGRONOMO",
    "Ativo": "1"
  },
  {
    "Id": 18,
    "Nome": "AGROPECUARISTA",
    "Ativo": "1"
  },
  {
    "Id": 19,
    "Nome": "AJUDANTE",
    "Ativo": "1"
  },
  {
    "Id": 20,
    "Nome": "ALFAIATE",
    "Ativo": "1"
  },
  {
    "Id": 21,
    "Nome": "ALMOXARIFE",
    "Ativo": "1"
  },
  {
    "Id": 22,
    "Nome": "AMBULANTE",
    "Ativo": "1"
  },
  {
    "Id": 23,
    "Nome": "ANALISTA ADMINISTRATIVOS, FINANCEIROS, CRÉDITO E DE RISCOS",
    "Ativo": "1"
  },
  {
    "Id": 24,
    "Nome": "ANALISTA DE COMERCIALIZAÇÃO, MARKETING E COMUNICAÇÃO",
    "Ativo": "1"
  },
  {
    "Id": 25,
    "Nome": "ANALISTA DE INSTITUIÇÃO DE SERVIÇOS EDUCACIONAIS",
    "Ativo": "1"
  },
  {
    "Id": 26,
    "Nome": "ANALISTA DE MANUTENÇÃO",
    "Ativo": "1"
  },
  {
    "Id": 27,
    "Nome": "ANALISTA DE OBRAS EM EMPRESA DE CONSTRUÇÃO",
    "Ativo": "1"
  },
  {
    "Id": 28,
    "Nome": "ANALISTA DE OPERAÇÕES COMERCIAIS E DE ASSISTÊNCIA TÉCNICA",
    "Ativo": "1"
  },
  {
    "Id": 29,
    "Nome": "ANALISTA DE OPERAÇÕES DE SERVIÇOS EM EMPRESA DE TRANSPORTE, DE COMUNICAÇÃO E DE LOGÍSTICA(ARMAZENAGEM E DISTRIBUIÇÃO)",
    "Ativo": "1"
  },
  {
    "Id": 30,
    "Nome": "ANALISTA DE OPERAÇÕES DE SERVIÇOS EM EMPRESA DE TURISMO, DE ALOJAMENTO E ALIMENTAÇÃO",
    "Ativo": "1"
  },
  {
    "Id": 31,
    "Nome": "ANALISTA DE OPERAÇÕES DE SERVIÇOS EM INSTITUIÇÃO DE INTERMEDIAÇÃO FINANCEIRA",
    "Ativo": "1"
  },
  {
    "Id": 32,
    "Nome": "ANALISTA DE OPERAÇÕES EM EMPRESA DE SERVIÇOS DE SAÚDE",
    "Ativo": "1"
  },
  {
    "Id": 33,
    "Nome": "ANALISTA DE OPERAÇÕES EM EMPRESA DE SERVIÇOS PESSOAIS, EM EMPRESA DE SERVIÇOS PESSOAIS, SOCIAIS E CULTURAIS",
    "Ativo": "1"
  },
  {
    "Id": 34,
    "Nome": "ANALISTA DE PESQUISA E DESENVOLVIMENTO",
    "Ativo": "1"
  },
  {
    "Id": 35,
    "Nome": "ANALISTA DE PRODUÇÃO E OPERAÇÕES EM EMPRESA AGROPECUÁRIA, PESQUEIRA, AQUÍCOLA E FLORESTAL",
    "Ativo": "1"
  },
  {
    "Id": 36,
    "Nome": "ANALISTA DE PRODUÇÃO E OPERAÇÕES EM EMPRESA DA INDÚSTRIA EXTRATIVA, DE TRANSFORMAÇÃO E DE SERVIÇOS DE UTILIDADE PÚBLICA",
    "Ativo": "1"
  },
  {
    "Id": 37,
    "Nome": "ANALISTA DE RECURSOS HUMANOS E DE RELAÇÕES DO TRABALHO",
    "Ativo": "1"
  },
  {
    "Id": 38,
    "Nome": "ANALISTA DE SUPRIMENTOS E AFINS",
    "Ativo": "1"
  },
  {
    "Id": 39,
    "Nome": "ANALISTA DE TECNOLOGIA DA INFORMAÇÃO",
    "Ativo": "1"
  },
  {
    "Id": 40,
    "Nome": "ANTIQUARIO",
    "Ativo": "1"
  },
  {
    "Id": 41,
    "Nome": "ANTROPÓLOGO",
    "Ativo": "1"
  },
  {
    "Id": 42,
    "Nome": "APICULTOR",
    "Ativo": "1"
  },
  {
    "Id": 43,
    "Nome": "APRENDIZ",
    "Ativo": "1"
  },
  {
    "Id": 44,
    "Nome": "AQUICULTOR",
    "Ativo": "1"
  },
  {
    "Id": 45,
    "Nome": "ÁRBITRO",
    "Ativo": "1"
  },
  {
    "Id": 46,
    "Nome": "ARMEIRO",
    "Ativo": "1"
  },
  {
    "Id": 47,
    "Nome": "ARQUEÓLOGO OU MUSEOLOGO",
    "Ativo": "1"
  },
  {
    "Id": 48,
    "Nome": "ARQUITETO OU PAISAGISTA",
    "Ativo": "1"
  },
  {
    "Id": 49,
    "Nome": "ARTESÃO",
    "Ativo": "1"
  },
  {
    "Id": 50,
    "Nome": "ARTISTA PLÁSTICO",
    "Ativo": "1"
  },
  {
    "Id": 51,
    "Nome": "ASSISTENTE SOCIAL",
    "Ativo": "1"
  },
  {
    "Id": 52,
    "Nome": "ASTRÓLOGO",
    "Ativo": "1"
  },
  {
    "Id": 53,
    "Nome": "ASTRONOMO",
    "Ativo": "1"
  },
  {
    "Id": 54,
    "Nome": "ATENDENTE, RECEPCIONISTA",
    "Ativo": "1"
  },
  {
    "Id": 55,
    "Nome": "ATLETA PROFISSIONAL OU TECNICO EM DESPORTOS",
    "Ativo": "1"
  },
  {
    "Id": 56,
    "Nome": "ATOR OU DIRETOR DE ESPETÁCULOS",
    "Ativo": "1"
  },
  {
    "Id": 57,
    "Nome": "AUXILIAR ADMINISTRATIVO",
    "Ativo": "1"
  },
  {
    "Id": 58,
    "Nome": "AUXILIAR DE ENFERMAGEM",
    "Ativo": "1"
  },
  {
    "Id": 59,
    "Nome": "AUXILIAR DE ESCRITÓRIO E ASSEMELHADOS",
    "Ativo": "1"
  },
  {
    "Id": 60,
    "Nome": "AUXILIAR DE LABORATÓRIOS E RAIOS X",
    "Ativo": "1"
  },
  {
    "Id": 61,
    "Nome": "AUXILIAR DE NUTRIÇÃO",
    "Ativo": "1"
  },
  {
    "Id": 62,
    "Nome": "AUXILIAR DE PRODUÇÃO",
    "Ativo": "1"
  },
  {
    "Id": 63,
    "Nome": "AUXILIAR DE SERVIÇOS GERAIS",
    "Ativo": "1"
  },
  {
    "Id": 64,
    "Nome": "AVICULTOR",
    "Ativo": "1"
  },
  {
    "Id": 65,
    "Nome": "BABÁ OU CUIDADOR",
    "Ativo": "1"
  },
  {
    "Id": 66,
    "Nome": "BAILARINO, CENÓGRAFO, COREÓGRAFO, DANÇARINO",
    "Ativo": "1"
  },
  {
    "Id": 67,
    "Nome": "BANCÁRIO OU ECONOMIÁRIO",
    "Ativo": "1"
  },
  {
    "Id": 68,
    "Nome": "BARBEIRO OU CABELEREIRO",
    "Ativo": "1"
  },
  {
    "Id": 69,
    "Nome": "BIBLIOTECÁRIO",
    "Ativo": "1"
  },
  {
    "Id": 70,
    "Nome": "BIÓLOGO, BIOQUÍMICO, BIOFISICO",
    "Ativo": "1"
  },
  {
    "Id": 71,
    "Nome": "BOLSISTA",
    "Ativo": "1"
  },
  {
    "Id": 72,
    "Nome": "BOMBEIRO MILITAR",
    "Ativo": "1"
  },
  {
    "Id": 73,
    "Nome": "BORRACHEIRO",
    "Ativo": "1"
  },
  {
    "Id": 74,
    "Nome": "CAIXA",
    "Ativo": "1"
  },
  {
    "Id": 75,
    "Nome": "CALDEREIRO",
    "Ativo": "1"
  },
  {
    "Id": 76,
    "Nome": "CAMINHONEIRO",
    "Ativo": "1"
  },
  {
    "Id": 77,
    "Nome": "CAPITALISTA",
    "Ativo": "1"
  },
  {
    "Id": 78,
    "Nome": "CARTEIRO",
    "Ativo": "1"
  },
  {
    "Id": 79,
    "Nome": "CATADOR DE PAPEL",
    "Ativo": "1"
  },
  {
    "Id": 80,
    "Nome": "CERAMISTA",
    "Ativo": "1"
  },
  {
    "Id": 81,
    "Nome": "CHAVEIRO",
    "Ativo": "1"
  },
  {
    "Id": 82,
    "Nome": "COBRADOR, FISCAL DE ÔNIBUS",
    "Ativo": "1"
  },
  {
    "Id": 83,
    "Nome": "COMERCIÁRIO",
    "Ativo": "1"
  },
  {
    "Id": 84,
    "Nome": "COMISSÁRIO DE BORDO ",
    "Ativo": "1"
  },
  {
    "Id": 85,
    "Nome": "COMUNICOLOGO",
    "Ativo": "1"
  },
  {
    "Id": 86,
    "Nome": "CONFERENTE, REPOSITOR, EMBALADOR, ESTOQUISTA",
    "Ativo": "1"
  },
  {
    "Id": 87,
    "Nome": "CONSELHO TUTELAR",
    "Ativo": "1"
  },
  {
    "Id": 88,
    "Nome": "CONSULTOR",
    "Ativo": "1"
  },
  {
    "Id": 89,
    "Nome": "CONSULTOR LEGISLATIVO",
    "Ativo": "1"
  },
  {
    "Id": 90,
    "Nome": "CONTADOR",
    "Ativo": "1"
  },
  {
    "Id": 91,
    "Nome": "COORDENADOR",
    "Ativo": "1"
  },
  {
    "Id": 92,
    "Nome": "COORDENADOR ADMINISTRATIVOS, FINANCEIROS, CRÉDITO E DE RISCOS",
    "Ativo": "1"
  },
  {
    "Id": 93,
    "Nome": "COORDENADOR DE CALLCENTER",
    "Ativo": "1"
  },
  {
    "Id": 94,
    "Nome": "COORDENADOR DE COMERCIALIZAÇÃO, MARKETING E COMUNICAÇÃO",
    "Ativo": "1"
  },
  {
    "Id": 95,
    "Nome": "COORDENADOR DE INSTITUIÇÃO DE SERVIÇOS EDUCACIONAIS",
    "Ativo": "1"
  },
  {
    "Id": 96,
    "Nome": "COORDENADOR DE MANUTENÇÃO",
    "Ativo": "1"
  },
  {
    "Id": 97,
    "Nome": "COORDENADOR DE OBRAS EM EMPRESA DE CONSTRUÇÃO",
    "Ativo": "1"
  },
  {
    "Id": 98,
    "Nome": "COORDENADOR DE OPERAÇÕES COMERCIAIS E DE ASSISTÊNCIA TÉCNICA",
    "Ativo": "1"
  },
  {
    "Id": 99,
    "Nome": "COORDENADOR DE OPERAÇÕES DE SERVIÇOS EM EMPRESA DE TRANSPORTE, DE COMUNICAÇÃO E DE LOGÍSTICA(ARMAZENAGEM E DISTRIBUIÇÃO)",
    "Ativo": "1"
  },
  {
    "Id": 100,
    "Nome": "COORDENADOR DE OPERAÇÕES DE SERVIÇOS EM EMPRESA DE TURISMO, DE ALOJAMENTO E ALIMENTAÇÃO",
    "Ativo": "1"
  },
  {
    "Id": 101,
    "Nome": "COORDENADOR DE OPERAÇÕES DE SERVIÇOS EM INSTITUIÇÃO DE INTERMEDIAÇÃO FINANCEIRA",
    "Ativo": "1"
  },
  {
    "Id": 102,
    "Nome": "COORDENADOR DE OPERAÇÕES EM EMPRESA DE SERVIÇOS DE SAÚDE",
    "Ativo": "1"
  },
  {
    "Id": 103,
    "Nome": "COORDENADOR DE OPERAÇÕES EM EMPRESA DE SERVIÇOS PESSOAIS, EM EMPRESA DE SERVIÇOS PESSOAIS, SOCIAIS E CULTURAIS",
    "Ativo": "1"
  },
  {
    "Id": 104,
    "Nome": "COORDENADOR DE PESQUISA E DESENVOLVIMENTO",
    "Ativo": "1"
  },
  {
    "Id": 105,
    "Nome": "COORDENADOR DE PRODUÇÃO E OPERAÇÕES EM EMPRESA AGROPECUÁRIA, PESQUEIRA, AQUÍCOLA E FLORESTAL",
    "Ativo": "1"
  },
  {
    "Id": 106,
    "Nome": "COORDENADOR DE PRODUÇÃO E OPERAÇÕES EM EMPRESA DA INDÚSTRIA EXTRATIVA, DE TRANSFORMAÇÃO E DE SERVIÇOS DE UTILIDADE PÚBLICA",
    "Ativo": "1"
  },
  {
    "Id": 107,
    "Nome": "COORDENADOR DE RECURSOS HUMANOS E DE RELAÇÕES DO TRABALHO",
    "Ativo": "1"
  },
  {
    "Id": 108,
    "Nome": "COORDENADOR DE SUPRIMENTOS E AFINS",
    "Ativo": "1"
  },
  {
    "Id": 109,
    "Nome": "COORDENADOR DE TECNOLOGIA DA INFORMAÇÃO",
    "Ativo": "1"
  },
  {
    "Id": 110,
    "Nome": "CO-PILOTO",
    "Ativo": "1"
  },
  {
    "Id": 111,
    "Nome": "CORREGEDOR",
    "Ativo": "1"
  },
  {
    "Id": 112,
    "Nome": "CORRETOR",
    "Ativo": "1"
  },
  {
    "Id": 113,
    "Nome": "COSTUREIRO",
    "Ativo": "1"
  },
  {
    "Id": 114,
    "Nome": "COZINHEIRO, COPEIRO, GARÇOM, BARMAN, CAMAREIRO",
    "Ativo": "1"
  },
  {
    "Id": 115,
    "Nome": "DECORADOR",
    "Ativo": "1"
  },
  {
    "Id": 116,
    "Nome": "DEFENSOR PÚBLICO",
    "Ativo": "1"
  },
  {
    "Id": 117,
    "Nome": "DELEGADO DE POLÍCIA",
    "Ativo": "1"
  },
  {
    "Id": 118,
    "Nome": "DENTISTA, ODONTÓLOGO",
    "Ativo": "1"
  },
  {
    "Id": 119,
    "Nome": "DEPUTADO ESTADUAL/DISTRITAL",
    "Ativo": "1"
  },
  {
    "Id": 120,
    "Nome": "DEPUTADO FEDERAL",
    "Ativo": "1"
  },
  {
    "Id": 121,
    "Nome": "DESEMBARGADOR",
    "Ativo": "1"
  },
  {
    "Id": 122,
    "Nome": "DESENHISTA",
    "Ativo": "1"
  },
  {
    "Id": 123,
    "Nome": "DESENHISTA COMERCIAL",
    "Ativo": "1"
  },
  {
    "Id": 124,
    "Nome": "DESENHISTA INDUSTRIAL",
    "Ativo": "1"
  },
  {
    "Id": 125,
    "Nome": "DESENHISTA TECNICO",
    "Ativo": "1"
  },
  {
    "Id": 126,
    "Nome": "DESPACHANTE",
    "Ativo": "1"
  },
  {
    "Id": 127,
    "Nome": "DETETIVE PARTICULAR",
    "Ativo": "1"
  },
  {
    "Id": 128,
    "Nome": "DIARISTA, FAXINEIRO",
    "Ativo": "1"
  },
  {
    "Id": 129,
    "Nome": "DIGITADOR",
    "Ativo": "1"
  },
  {
    "Id": 130,
    "Nome": "DIPLOMATA",
    "Ativo": "1"
  },
  {
    "Id": 131,
    "Nome": "DIRETOR",
    "Ativo": "1"
  },
  {
    "Id": 132,
    "Nome": "DISCOTECARIO / DJ",
    "Ativo": "1"
  },
  {
    "Id": 133,
    "Nome": "DO LAR",
    "Ativo": "1"
  },
  {
    "Id": 134,
    "Nome": "ECOLOGISTA",
    "Ativo": "1"
  },
  {
    "Id": 135,
    "Nome": "ECONOMISTA",
    "Ativo": "1"
  },
  {
    "Id": 136,
    "Nome": "ELETRICIÁRIO, ELETRICISTA E ASSEMELHADOS",
    "Ativo": "1"
  },
  {
    "Id": 137,
    "Nome": "EMPREGADO DOMÉSTICO",
    "Ativo": "1"
  },
  {
    "Id": 138,
    "Nome": "EMPRESÁRIO (11 ATÉ 50 FUNCIONÁRIOS)",
    "Ativo": "1"
  },
  {
    "Id": 139,
    "Nome": "EMPRESÁRIO (ATÉ 10 FUNCIONÁRIOS )",
    "Ativo": "1"
  },
  {
    "Id": 140,
    "Nome": "EMPRESÁRIO (MAIS DE 50 FUNCIONARIOS)",
    "Ativo": "1"
  },
  {
    "Id": 141,
    "Nome": "ENCANADOR",
    "Ativo": "1"
  },
  {
    "Id": 142,
    "Nome": "ENCARREGADO",
    "Ativo": "1"
  },
  {
    "Id": 143,
    "Nome": "ENFERMEIRO",
    "Ativo": "1"
  },
  {
    "Id": 144,
    "Nome": "ENGENHEIRO",
    "Ativo": "1"
  },
  {
    "Id": 145,
    "Nome": "ENGRAXATE, SAPATEIRO",
    "Ativo": "1"
  },
  {
    "Id": 146,
    "Nome": "ESCREVENTE, AUXILIAR DE CARTÓRIO",
    "Ativo": "1"
  },
  {
    "Id": 147,
    "Nome": "ESCRITOR",
    "Ativo": "1"
  },
  {
    "Id": 148,
    "Nome": "ESCULTOR, PINTOR",
    "Ativo": "1"
  },
  {
    "Id": 149,
    "Nome": "ESTADUAL",
    "Ativo": "1"
  },
  {
    "Id": 150,
    "Nome": "ESTAGIÁRIO E ASSEMELHADOS",
    "Ativo": "1"
  },
  {
    "Id": 151,
    "Nome": "ESTATÍSTICO",
    "Ativo": "1"
  },
  {
    "Id": 152,
    "Nome": "ESTETICISTA",
    "Ativo": "1"
  },
  {
    "Id": 153,
    "Nome": "ESTIVADOR, CARREGADOR",
    "Ativo": "1"
  },
  {
    "Id": 154,
    "Nome": "ESTUDANTE",
    "Ativo": "1"
  },
  {
    "Id": 155,
    "Nome": "FARMACÊUTICO",
    "Ativo": "1"
  },
  {
    "Id": 156,
    "Nome": "FEDERAL",
    "Ativo": "1"
  },
  {
    "Id": 157,
    "Nome": "FEIRANTE",
    "Ativo": "1"
  },
  {
    "Id": 158,
    "Nome": "FERRAMENTEIRO",
    "Ativo": "1"
  },
  {
    "Id": 159,
    "Nome": "FERROVIRÁRIO",
    "Ativo": "1"
  },
  {
    "Id": 160,
    "Nome": "FILÓLOGO",
    "Ativo": "1"
  },
  {
    "Id": 161,
    "Nome": "FILÓSOFO",
    "Ativo": "1"
  },
  {
    "Id": 162,
    "Nome": "FISCAL, AUDITOR E ASSEMELHADOS",
    "Ativo": "1"
  },
  {
    "Id": 163,
    "Nome": "FÍSICO",
    "Ativo": "1"
  },
  {
    "Id": 164,
    "Nome": "FISIOTERAPEUTA OU TERAPEUTA OCUPACIONAL",
    "Ativo": "1"
  },
  {
    "Id": 165,
    "Nome": "FLORICULTURA",
    "Ativo": "1"
  },
  {
    "Id": 166,
    "Nome": "FONOAUDIÓLOGO",
    "Ativo": "1"
  },
  {
    "Id": 167,
    "Nome": "FOTÓGRAFO",
    "Ativo": "1"
  },
  {
    "Id": 168,
    "Nome": "FRENTISTA",
    "Ativo": "1"
  },
  {
    "Id": 169,
    "Nome": "FUNILEIRO",
    "Ativo": "1"
  },
  {
    "Id": 170,
    "Nome": "GARI",
    "Ativo": "1"
  },
  {
    "Id": 171,
    "Nome": "GARIMPEIRO",
    "Ativo": "1"
  },
  {
    "Id": 172,
    "Nome": "GEÓGRAFO",
    "Ativo": "1"
  },
  {
    "Id": 173,
    "Nome": "GEÓLOGO",
    "Ativo": "1"
  },
  {
    "Id": 174,
    "Nome": "GERENTE",
    "Ativo": "1"
  },
  {
    "Id": 175,
    "Nome": "GERENTE ADMINISTRATIVOS, FINANCEIROS, CRÉDITO E DE RISCOS",
    "Ativo": "1"
  },
  {
    "Id": 176,
    "Nome": "GERENTE DE COMERCIALIZAÇÃO, MARKETING E COMUNICAÇÃO",
    "Ativo": "1"
  },
  {
    "Id": 177,
    "Nome": "GERENTE DE INSTITUIÇÃO DE SERVIÇOS EDUCACIONAIS",
    "Ativo": "1"
  },
  {
    "Id": 178,
    "Nome": "GERENTE DE MANUTENÇÃO",
    "Ativo": "1"
  },
  {
    "Id": 179,
    "Nome": "GERENTE DE OBRAS EM EMPRESA DE CONSTRUÇÃO",
    "Ativo": "1"
  },
  {
    "Id": 180,
    "Nome": "GERENTE DE OPERAÇÕES COMERCIAIS E DE ASSISTÊNCIA TÉCNICA",
    "Ativo": "1"
  },
  {
    "Id": 181,
    "Nome": "GERENTE DE OPERAÇÕES DE SERVIÇOS EM EMPRESA DE TRANSPORTE, DE COMUNICAÇÃO E DE LOGÍSTICA(ARMAZENAGEM E DISTRIBUIÇÃO)",
    "Ativo": "1"
  },
  {
    "Id": 182,
    "Nome": "GERENTE DE OPERAÇÕES DE SERVIÇOS EM EMPRESA DE TURISMO, DE ALOJAMENTO E ALIMENTAÇÃO",
    "Ativo": "1"
  },
  {
    "Id": 183,
    "Nome": "GERENTE DE OPERAÇÕES DE SERVIÇOS EM INSTITUIÇÃO DE INTERMEDIAÇÃO FINANCEIRA",
    "Ativo": "1"
  },
  {
    "Id": 184,
    "Nome": "GERENTE DE OPERAÇÕES EM EMPRESA DE SERVIÇOS DE SAÚDE",
    "Ativo": "1"
  },
  {
    "Id": 185,
    "Nome": "GERENTE DE OPERAÇÕES EM EMPRESA DE SERVIÇOS PESSOAIS, EM EMPRESA DE SERVIÇOS PESSOAIS, SOCIAIS E CULTURAIS",
    "Ativo": "1"
  },
  {
    "Id": 186,
    "Nome": "GERENTE DE PESQUISA E DESENVOLVIMENTO",
    "Ativo": "1"
  },
  {
    "Id": 187,
    "Nome": "GERENTE DE PRODUÇÃO E OPERAÇÕES EM EMPRESA AGROPECUÁRIA, PESQUEIRA, AQUÍCOLA E FLORESTAL",
    "Ativo": "1"
  },
  {
    "Id": 188,
    "Nome": "GERENTE DE PRODUÇÃO E OPERAÇÕES EM EMPRESA DA INDÚSTRIA EXTRATIVA, DE TRANSFORMAÇÃO E DE SERVIÇOS DE UTILIDADE PÚBLICA",
    "Ativo": "1"
  },
  {
    "Id": 189,
    "Nome": "GERENTE DE RECURSOS HUMANOS E DE RELAÇÕES DO TRABALHO",
    "Ativo": "1"
  },
  {
    "Id": 190,
    "Nome": "GERENTE DE SUPRIMENTOS E AFINS",
    "Ativo": "1"
  },
  {
    "Id": 191,
    "Nome": "GERENTE DE TECNOLOGIA DA INFORMAÇÃO",
    "Ativo": "1"
  },
  {
    "Id": 192,
    "Nome": "GOVERNADOR, VICE-GOVERNADOR",
    "Ativo": "1"
  },
  {
    "Id": 193,
    "Nome": "HISTORIADOR",
    "Ativo": "1"
  },
  {
    "Id": 194,
    "Nome": "IMPRESSOR",
    "Ativo": "1"
  },
  {
    "Id": 195,
    "Nome": "INDUSTRIÁRIO",
    "Ativo": "1"
  },
  {
    "Id": 196,
    "Nome": "INSS",
    "Ativo": "1"
  },
  {
    "Id": 197,
    "Nome": "INSTRUTOR",
    "Ativo": "1"
  },
  {
    "Id": 198,
    "Nome": "JARDINEIRO",
    "Ativo": "1"
  },
  {
    "Id": 199,
    "Nome": "JOALHEIRO OU OURIVES",
    "Ativo": "1"
  },
  {
    "Id": 200,
    "Nome": "JORNALEIRO",
    "Ativo": "1"
  },
  {
    "Id": 201,
    "Nome": "JORNALISTA, REPÓRTER",
    "Ativo": "1"
  },
  {
    "Id": 202,
    "Nome": "JUIZ",
    "Ativo": "1"
  },
  {
    "Id": 203,
    "Nome": "LANTERNEIRO OU PINTOR DE VEICULOS",
    "Ativo": "1"
  },
  {
    "Id": 204,
    "Nome": "LAVADOR DE CARROS",
    "Ativo": "1"
  },
  {
    "Id": 205,
    "Nome": "LEILOEIRO OU AVALIADOR",
    "Ativo": "1"
  },
  {
    "Id": 206,
    "Nome": "LOCATÁRIO",
    "Ativo": "1"
  },
  {
    "Id": 207,
    "Nome": "LOCUTOR, COMENTARISTA OU RADIALISTA",
    "Ativo": "1"
  },
  {
    "Id": 208,
    "Nome": "MANICURE, PEDICURE",
    "Ativo": "1"
  },
  {
    "Id": 209,
    "Nome": "MANOBRISTA",
    "Ativo": "1"
  },
  {
    "Id": 210,
    "Nome": "MAQUINISTA OU FOGUISTA",
    "Ativo": "1"
  },
  {
    "Id": 211,
    "Nome": "MARCENEIRO",
    "Ativo": "1"
  },
  {
    "Id": 212,
    "Nome": "MARINHEIRO",
    "Ativo": "1"
  },
  {
    "Id": 213,
    "Nome": "MASSAGISTA",
    "Ativo": "1"
  },
  {
    "Id": 214,
    "Nome": "MATEMÁTICO OU ATUÁRIO",
    "Ativo": "1"
  },
  {
    "Id": 215,
    "Nome": "MECÂNICO DE MANUTENÇÃO DE MÁQUINAS INDUSTRIAIS",
    "Ativo": "1"
  },
  {
    "Id": 216,
    "Nome": "MECÂNICO DE VEICULOS AUTOMOTORES",
    "Ativo": "1"
  },
  {
    "Id": 217,
    "Nome": "MECÂNICO DE VOO",
    "Ativo": "1"
  },
  {
    "Id": 218,
    "Nome": "MÉDICO",
    "Ativo": "1"
  },
  {
    "Id": 219,
    "Nome": "MEEIRO",
    "Ativo": "1"
  },
  {
    "Id": 220,
    "Nome": "MERGULHADOR",
    "Ativo": "1"
  },
  {
    "Id": 221,
    "Nome": "MESTRE DE OBRAS, CONSTRUTOR",
    "Ativo": "1"
  },
  {
    "Id": 222,
    "Nome": "METALURGICO OU SIDERURGICO",
    "Ativo": "1"
  },
  {
    "Id": 223,
    "Nome": "METEREOLOGISTA",
    "Ativo": "1"
  },
  {
    "Id": 224,
    "Nome": "MICROEMPREENDEDOR INDIVIDUAL",
    "Ativo": "1"
  },
  {
    "Id": 225,
    "Nome": "MILITAR REFORMADO  ",
    "Ativo": "1"
  },
  {
    "Id": 226,
    "Nome": "MILITARES REFORMADOS",
    "Ativo": "1"
  },
  {
    "Id": 227,
    "Nome": "MINISTRO DE ESTADO",
    "Ativo": "1"
  },
  {
    "Id": 228,
    "Nome": "MISSIONÁRIO",
    "Ativo": "1"
  },
  {
    "Id": 229,
    "Nome": "MODELISTA OU CORTADOR",
    "Ativo": "1"
  },
  {
    "Id": 230,
    "Nome": "MODELO DE MODA",
    "Ativo": "1"
  },
  {
    "Id": 231,
    "Nome": "MODISTA",
    "Ativo": "1"
  },
  {
    "Id": 232,
    "Nome": "MOTO TAXI",
    "Ativo": "1"
  },
  {
    "Id": 233,
    "Nome": "MOTOCICLISTA (TRANSPORTE DE MERCADORIAS OU ASSEMELHADOS)",
    "Ativo": "1"
  },
  {
    "Id": 234,
    "Nome": "MOTORISTA",
    "Ativo": "1"
  },
  {
    "Id": 235,
    "Nome": "MOTORISTA DE MÁQUINAS PESADAS",
    "Ativo": "1"
  },
  {
    "Id": 236,
    "Nome": "MOTORISTA DE VEÍCULOS DE TRANSPOSRTE DE CARGA",
    "Ativo": "1"
  },
  {
    "Id": 237,
    "Nome": "MUNICIPAL",
    "Ativo": "1"
  },
  {
    "Id": 238,
    "Nome": "MÚSICO, CANTOR OU COMPOSITOR",
    "Ativo": "1"
  },
  {
    "Id": 239,
    "Nome": "NUTRICIONISTA",
    "Ativo": "1"
  },
  {
    "Id": 240,
    "Nome": "OCEANÓGRAFO",
    "Ativo": "1"
  },
  {
    "Id": 241,
    "Nome": "OFICIAIS GENERAIS (GENERAL, BRIGADEIRO)",
    "Ativo": "1"
  },
  {
    "Id": 242,
    "Nome": "OFICIAIS INTERMEDIÁRIOS (CAPITÃO, CAPITÃO-TENENTE)",
    "Ativo": "1"
  },
  {
    "Id": 243,
    "Nome": "OFICIAIS SUBALTERNOS (ASPIRANTE, PRIMEIRO-TENENTE, SEGUNDO-TENENTE)",
    "Ativo": "1"
  },
  {
    "Id": 244,
    "Nome": "OFICIAIS SUPERIORES (MAJOR, TENENTE-CORONEL, CORONEL)",
    "Ativo": "1"
  },
  {
    "Id": 245,
    "Nome": "OFICIAL DE JUSTIÇA",
    "Ativo": "1"
  },
  {
    "Id": 246,
    "Nome": "OLEIRO",
    "Ativo": "1"
  },
  {
    "Id": 247,
    "Nome": "OPERADOR DE CÂMERA DE CINEMA E TELEVISÃO",
    "Ativo": "1"
  },
  {
    "Id": 248,
    "Nome": "OPERADOR DE COMPUTADOR",
    "Ativo": "1"
  },
  {
    "Id": 249,
    "Nome": "OPERADOR DE MÁQUINAS AGROPECUÁRIAS",
    "Ativo": "1"
  },
  {
    "Id": 250,
    "Nome": "OPERADOR DE TELEMARKETING",
    "Ativo": "1"
  },
  {
    "Id": 251,
    "Nome": "OUTROS",
    "Ativo": "1"
  },
  {
    "Id": 252,
    "Nome": "PADEIRO",
    "Ativo": "1"
  },
  {
    "Id": 253,
    "Nome": "PASTOR",
    "Ativo": "1"
  },
  {
    "Id": 254,
    "Nome": "PECUARISTA",
    "Ativo": "1"
  },
  {
    "Id": 255,
    "Nome": "PEDAGOGO",
    "Ativo": "1"
  },
  {
    "Id": 256,
    "Nome": "PEDREIRO, PINTOR, CARPINTEIRO, SERRALHEIRO",
    "Ativo": "1"
  },
  {
    "Id": 257,
    "Nome": "PEIXEIRO",
    "Ativo": "1"
  },
  {
    "Id": 258,
    "Nome": "PENSÃO ALIMENTÍCIA PARTICULAR COMPROVADA",
    "Ativo": "1"
  },
  {
    "Id": 259,
    "Nome": "PESCADOR",
    "Ativo": "1"
  },
  {
    "Id": 260,
    "Nome": "PESCADOR COM FINS COMERCIAIS",
    "Ativo": "1"
  },
  {
    "Id": 261,
    "Nome": "PETROLEIRO",
    "Ativo": "1"
  },
  {
    "Id": 262,
    "Nome": "PILOTO",
    "Ativo": "1"
  },
  {
    "Id": 263,
    "Nome": "PINTOR",
    "Ativo": "1"
  },
  {
    "Id": 264,
    "Nome": "POLÍCIA CIVIL - AGENTE PENITENCIÁRIO, INSPETOR, DETETIVE, INVESTIGADOR, ESCRIVÃO",
    "Ativo": "1"
  },
  {
    "Id": 265,
    "Nome": "POLÍCIA CIVIL - DELEGADO",
    "Ativo": "1"
  },
  {
    "Id": 266,
    "Nome": "POLÍCIA CIVIL - PERITO",
    "Ativo": "1"
  },
  {
    "Id": 267,
    "Nome": "POLÍCIA FEDERAL - DELEGADO",
    "Ativo": "1"
  },
  {
    "Id": 268,
    "Nome": "POLÍCIA FEDERAL - INSPETOR, DETETIVE, INVESTIGADOR, ESCRIVÃO",
    "Ativo": "1"
  },
  {
    "Id": 269,
    "Nome": "POLÍCIA FEDERAL - PERITO",
    "Ativo": "1"
  },
  {
    "Id": 270,
    "Nome": "POLÍTICO",
    "Ativo": "1"
  },
  {
    "Id": 271,
    "Nome": "PORTEIRO DE EDIFICIO, ASCENSORISTA OU GARAGISTA",
    "Ativo": "1"
  },
  {
    "Id": 272,
    "Nome": "PORTUÁRIO",
    "Ativo": "1"
  },
  {
    "Id": 273,
    "Nome": "PRAÇAS OU GRADUADOS (SARGENTO, MARINHEIRO, SOLDADO, CABO)",
    "Ativo": "1"
  },
  {
    "Id": 274,
    "Nome": "PREFEITO, VICE-PREFEITO",
    "Ativo": "1"
  },
  {
    "Id": 275,
    "Nome": "PREVIDÊNCIA PRIVADA",
    "Ativo": "1"
  },
  {
    "Id": 276,
    "Nome": "PROCURADOR DE JUSTIÇA",
    "Ativo": "1"
  },
  {
    "Id": 277,
    "Nome": "PRODUTOR DE ESPETÁCULOS",
    "Ativo": "1"
  },
  {
    "Id": 278,
    "Nome": "PRODUTOR RURAL",
    "Ativo": "1"
  },
  {
    "Id": 279,
    "Nome": "PROFESSOR",
    "Ativo": "1"
  },
  {
    "Id": 280,
    "Nome": "PROFESSOR DE ENSINO FUNDAMENTAL",
    "Ativo": "1"
  },
  {
    "Id": 281,
    "Nome": "PROFESSOR DE ENSINO MÉDIO",
    "Ativo": "1"
  },
  {
    "Id": 282,
    "Nome": "PROFESSOR DE ENSINO SUPERIOR",
    "Ativo": "1"
  },
  {
    "Id": 283,
    "Nome": "PROFISSIONAL DE LETRAS E ARTES",
    "Ativo": "1"
  },
  {
    "Id": 284,
    "Nome": "PROGRAMADOR DE SISTEMAS",
    "Ativo": "1"
  },
  {
    "Id": 285,
    "Nome": "PROMOTOR DE JUSTIÇA",
    "Ativo": "1"
  },
  {
    "Id": 286,
    "Nome": "PROMOTOR DE VENDAS",
    "Ativo": "1"
  },
  {
    "Id": 287,
    "Nome": "PROPRIETÁRIO DE LOJA (CONFECÇÃO)",
    "Ativo": "1"
  },
  {
    "Id": 288,
    "Nome": "PROPRIETÁRIO DE PADARIA E SIMILARES",
    "Ativo": "1"
  },
  {
    "Id": 289,
    "Nome": "PROPRIETÁRIO DE RESTAURANTE",
    "Ativo": "1"
  },
  {
    "Id": 290,
    "Nome": "PROTÉTICO",
    "Ativo": "1"
  },
  {
    "Id": 291,
    "Nome": "PSICÓLOGO",
    "Ativo": "1"
  },
  {
    "Id": 292,
    "Nome": "PUBLICITÁRIO",
    "Ativo": "1"
  },
  {
    "Id": 293,
    "Nome": "QUÍMICO",
    "Ativo": "1"
  },
  {
    "Id": 294,
    "Nome": "RADIOTÉCNICO",
    "Ativo": "1"
  },
  {
    "Id": 295,
    "Nome": "REDATOR",
    "Ativo": "1"
  },
  {
    "Id": 296,
    "Nome": "RELAÇÕES PÚBLICAS",
    "Ativo": "1"
  },
  {
    "Id": 297,
    "Nome": "RELOJOEIRO",
    "Ativo": "1"
  },
  {
    "Id": 298,
    "Nome": "RENTISTA OU LOCADOR",
    "Ativo": "1"
  },
  {
    "Id": 299,
    "Nome": "REPRESENTANTE COMERCIAL",
    "Ativo": "1"
  },
  {
    "Id": 300,
    "Nome": "RODOVIÁRIOS",
    "Ativo": "1"
  },
  {
    "Id": 301,
    "Nome": "SACERDOTE",
    "Ativo": "1"
  },
  {
    "Id": 302,
    "Nome": "SALVA VIDAS",
    "Ativo": "1"
  },
  {
    "Id": 303,
    "Nome": "SAPATEIRO",
    "Ativo": "1"
  },
  {
    "Id": 304,
    "Nome": "SECRETÁRIA",
    "Ativo": "1"
  },
  {
    "Id": 305,
    "Nome": "SECRETÁRIO",
    "Ativo": "1"
  },
  {
    "Id": 306,
    "Nome": "SECRETÁRIO ESTADUAL",
    "Ativo": "1"
  },
  {
    "Id": 307,
    "Nome": "SECRETÁRIO FEDERAL",
    "Ativo": "1"
  },
  {
    "Id": 308,
    "Nome": "SECRETÁRIO MUNICIPAL",
    "Ativo": "1"
  },
  {
    "Id": 309,
    "Nome": "SECURITÁRIO",
    "Ativo": "1"
  },
  {
    "Id": 310,
    "Nome": "SEGURANÇA OU VIGILANTE",
    "Ativo": "1"
  },
  {
    "Id": 311,
    "Nome": "SENADOR",
    "Ativo": "1"
  },
  {
    "Id": 312,
    "Nome": "SERRALHEIRO",
    "Ativo": "1"
  },
  {
    "Id": 313,
    "Nome": "SERVENTUÁRIO DE JUSTIÇA",
    "Ativo": "1"
  },
  {
    "Id": 314,
    "Nome": "SERVIDOR PÚBLICO ESTADUAL",
    "Ativo": "1"
  },
  {
    "Id": 315,
    "Nome": "SERVIDOR PÚBLICO FEDERAL",
    "Ativo": "1"
  },
  {
    "Id": 316,
    "Nome": "SERVIDOR PÚBLICO MUNICIPAL",
    "Ativo": "1"
  },
  {
    "Id": 317,
    "Nome": "SOCIÓLOGO",
    "Ativo": "1"
  },
  {
    "Id": 318,
    "Nome": "SOLDADO/INVESTIGADOR/PERITO",
    "Ativo": "1"
  },
  {
    "Id": 319,
    "Nome": "SUCATEIRO",
    "Ativo": "1"
  },
  {
    "Id": 320,
    "Nome": "SUPERVISOR, INSPETOR, AGENTE DE COMPRAS E VENDAS",
    "Ativo": "1"
  },
  {
    "Id": 321,
    "Nome": "TABELIÃO OU OFICIAL DE REGISTRO",
    "Ativo": "1"
  },
  {
    "Id": 322,
    "Nome": "TAPECEIRO",
    "Ativo": "1"
  },
  {
    "Id": 323,
    "Nome": "TAQUIGRAFO",
    "Ativo": "1"
  },
  {
    "Id": 324,
    "Nome": "TAXISTA",
    "Ativo": "1"
  },
  {
    "Id": 325,
    "Nome": "TÉCNICO AGRICOLA",
    "Ativo": "1"
  },
  {
    "Id": 326,
    "Nome": "TÉCNICO DE EDIFICAÇÕES",
    "Ativo": "1"
  },
  {
    "Id": 327,
    "Nome": "TÉCNICO DE ELETRICIDADE, ELETRÔNICA E TELECOMUNICAÇÕES",
    "Ativo": "1"
  },
  {
    "Id": 328,
    "Nome": "TÉCNICO DE ENFERMAGEM",
    "Ativo": "1"
  },
  {
    "Id": 329,
    "Nome": "TÉCNICO DE INFORMÁTICA",
    "Ativo": "1"
  },
  {
    "Id": 330,
    "Nome": "TÉCNICO DE LABORATÓRIO E RAIOS X",
    "Ativo": "1"
  },
  {
    "Id": 331,
    "Nome": "TÉCNICO DE QUÍMICA",
    "Ativo": "1"
  },
  {
    "Id": 332,
    "Nome": "TÉCNICO EM BIOLOGIA",
    "Ativo": "1"
  },
  {
    "Id": 333,
    "Nome": "TÉCNICO EM CONTABILIDADE",
    "Ativo": "1"
  },
  {
    "Id": 334,
    "Nome": "TECNICO EM GERAL",
    "Ativo": "1"
  },
  {
    "Id": 335,
    "Nome": "TÉCNICO EM MECÂNICA",
    "Ativo": "1"
  },
  {
    "Id": 336,
    "Nome": "TÉCNICO EM SEGURANÇA DO TRABALHO",
    "Ativo": "1"
  },
  {
    "Id": 337,
    "Nome": "TÉCNICO INSTRUMENTISTA",
    "Ativo": "1"
  },
  {
    "Id": 338,
    "Nome": "TECNÓLOGO",
    "Ativo": "1"
  },
  {
    "Id": 339,
    "Nome": "TELEFONISTA",
    "Ativo": "1"
  },
  {
    "Id": 340,
    "Nome": "TENENTE",
    "Ativo": "1"
  },
  {
    "Id": 341,
    "Nome": "TESOUREIRO",
    "Ativo": "1"
  },
  {
    "Id": 342,
    "Nome": "TINTUREIRO",
    "Ativo": "1"
  },
  {
    "Id": 343,
    "Nome": "TIPÓGRAFO E DEMAIS TRABALHADORES DE ARTES GRÁFICAS",
    "Ativo": "1"
  },
  {
    "Id": 344,
    "Nome": "TOPÓGRAFO",
    "Ativo": "1"
  },
  {
    "Id": 345,
    "Nome": "TORNEIRO MECÂNICO",
    "Ativo": "1"
  },
  {
    "Id": 346,
    "Nome": "TRABALHADOR DE CONSTRUÇÃO CIVIL",
    "Ativo": "1"
  },
  {
    "Id": 347,
    "Nome": "TRABALHADOR DE EXTRAÇÃO DE MINÉRIOS",
    "Ativo": "1"
  },
  {
    "Id": 348,
    "Nome": "TRABALHADOR DE EXTRAÇÃO VEGETAL",
    "Ativo": "1"
  },
  {
    "Id": 349,
    "Nome": "TRABALHADOR FLORESTAL",
    "Ativo": "1"
  },
  {
    "Id": 350,
    "Nome": "TRABALHADOR INFORMAL",
    "Ativo": "1"
  },
  {
    "Id": 351,
    "Nome": "TRABALHADOR NA FABRICAÇÃO DE ROUPAS",
    "Ativo": "1"
  },
  {
    "Id": 352,
    "Nome": "TRABALHADOR RURAL",
    "Ativo": "1"
  },
  {
    "Id": 353,
    "Nome": "TRABALHADOR USINAGEM DE METAIS",
    "Ativo": "1"
  },
  {
    "Id": 354,
    "Nome": "TRADUTOR",
    "Ativo": "1"
  },
  {
    "Id": 355,
    "Nome": "VENDEDOR DE COMÉRCIO VAREJISTA OU ATACADISTA",
    "Ativo": "1"
  },
  {
    "Id": 356,
    "Nome": "VENDEDOR PRACISTA OU REPRESENTANTE COMERCIAL",
    "Ativo": "1"
  },
  {
    "Id": 357,
    "Nome": "VEREADOR",
    "Ativo": "1"
  },
  {
    "Id": 358,
    "Nome": "VETERINÁRIO OU ZOOTECNISTA",
    "Ativo": "1"
  },
  {
    "Id": 359,
    "Nome": "VIDRACEIRO",
    "Ativo": "1"
  },
  {
    "Id": 360,
    "Nome": "ZELADOR",
    "Ativo": "1"
  }
]'

declare @profissoesTable table(
    Id int identity(1,1),
    Nome varchar(255)
)

insert into @profissoesTable
     select * from openjson(@profissoes)
      with (
            Nome varchar(255)
        )

insert into dbo.Profissao     
select 
Nome,
1 Ativo
from  @profissoesTable
ORDER by id


 ALTER TABLE dbo.Proposta WITH CHECK ADD CONSTRAINT FK_Proposta_Profissao FOREIGN KEY (ProfissaoId)
 REFERENCES dbo.Profissao(Id)

 ALTER TABLE dbo.Renda  WITH CHECK ADD CONSTRAINT [FK_Renda_Profissao] FOREIGN KEY(ProfissaoId)
 REFERENCES dbo.Profissao(Id)

