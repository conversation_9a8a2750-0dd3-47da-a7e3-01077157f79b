
update api.route 
set input =
'{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "numeroCCB": 6,
            "motivo": 0,
            "dadosAdicionais": "teste motivos"
        }
    ],
    "required": [
        "numeroCCB",
        "motivo"
    ],
    "properties": {
        "numeroCCB": {
            "$id": "#/properties/numeroCCB",
            "type": "integer",
            "title": "The numeroCCB schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                6
            ]
        },
        "motivo": {
            "$id": "#/properties/motivo",
            "type": "integer",
            "title": "The motivo schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                0
            ]
        },
        "dadosAdicionais": {
            "$id": "#/properties/dadosAdicionais",
            "type": "string",
            "title": "The dadosAdicionais schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "teste motivos"
            ]
        }
    },
    "additionalProperties": true
}'
where [route] = '/api/proposta/revisao' and [method] = 'POST'


