declare @GrupoOrdem int, 
        @GrupoId int,
        @Id int, 
        @valor varchar(max),
        @codigo varchar(100)

select @GrupoOrdem = max(Ordem)+1 from Parametro.ParametroGrupo

insert into Parametro.ParametroGrupo values ('Unico',@GrupoOrdem)
set @GrupoId = @@IDENTITY


set @valor = '60'
set @codigo = 'UNICO_REFRESH_TIMEOUT' 

insert into Parametro.Parametro values (@GrupoId,2,@codigo,'Tempo intervalo rotina Timeout Unico','Tempo em segundos entre as execuções da rotina de timeout de resposta da Unico',1,@valor)
set @Id = @@IDENTITY
insert into Parametro.ParametroHistorico values (@Id, dbo.getdateBR(),@valor)

set @valor = '10'
set @codigo = 'UNICO_ETAPA_BIOMETRIA' 

insert into Parametro.Parametro values (@GrupoId,2,@codigo,'Etapa Biometria Unico','Etapa em que ocorre a validação da Biometria pela Unico',1,@valor)
set @Id = @@IDENTITY
insert into Parametro.ParametroHistorico values (@Id, dbo.getdateBR(),@valor)

set @valor = '11'
set @codigo = 'UNICO_ETAPA_BIOMETRIA_SEM_RESPOSTA' 

insert into Parametro.Parametro values (@GrupoId,2,@codigo,'Etapa Biometria Unico sem Resposta','Etapa para onde a proposta vai caso a Unico não responda no tempo necessário',1,@valor)
set @Id = @@IDENTITY
insert into Parametro.ParametroHistorico values (@Id, dbo.getdateBR(),@valor)

set @valor = '15'
set @codigo = 'UNICO_BIOMETRIA_TIMEOUT' 

insert into Parametro.Parametro values (@GrupoId,2,@codigo,'Tempo Timeout Unico','Tempo em minutos sem a resposta da Unico para mudar a proposta de etapa por timeout',1,@valor)
set @Id = @@IDENTITY
insert into Parametro.ParametroHistorico values (@Id, dbo.getdateBR(),@valor)

