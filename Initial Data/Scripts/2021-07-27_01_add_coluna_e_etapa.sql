declare @etapaId int

update PropostaEtapa set Nome = 'Análise Validação' where Nome = 'Análise validação'

alter table PropostaEtapa add Exibicao<PERSON><PERSON><PERSON> varchar(50) null

select * from PropostaEtapa 

insert into PropostaEtapa (Nome, Cor, TempoExpiracao, CancelamentoParceiro, Modalidade, TipoResponsavel, FilaDistribuicao, RotaDestino, Pendencia, ExibicaoAPP) values
('Aguard. Checagem', '#BC80EB', 15, 0, 2, 1, 0, null, 0, 'aguard. validação')

set @etapaId = @@IDENTITY

insert into PropostaEtapaFluxo values 
(@etapaId, (select id from PropostaEtapa where Nome = 'Em Análise'), null, null, 1),
(@etapaId, (select id from PropostaEtapa where Nome = 'Proposta Pendente'), null, null, 1),
(@etapaId, (select id from PropostaEtapa where Nome = 'Fila Contato'), null, null, 1),
(@etapaId, (select id from PropostaEtapa where Nome = 'Ligando'), null, null, 1),
(@etapaId, (select id from PropostaEtapa where Nome = 'Contato Pendente'), null, null, 1),
(@etapaId, (select id from PropostaEtapa where Nome = 'Aguard. Assinatura'), null, null, 1),
(@etapaId, (select id from PropostaEtapa where Nome = 'Aguard. Validação'), null, null, 1),
(@etapaId, (select id from PropostaEtapa where Nome = 'Análise Validação'), null, null, 1),
(@etapaId, (select id from PropostaEtapa where Nome = 'Validação Pendente'), null, null, 1),
(@etapaId, (select id from PropostaEtapa where Nome = 'Análise Promotora'), null, null, 1),
(@etapaId, (select id from PropostaEtapa where Nome = 'Fila Pagamento'), null, null, 1)


