

insert into ParametrosReanalise(Response,chave,Reanalise,PropostaMotivoId) values
('P1','"Resposta"."(Pré Análise) - Int - Cliente com acordo de cobrança em curso"."Sistema"',1,54),
('P1','"Resposta"."(Pré Análise) - Int - Cliente com UF diferente da região de atuação"."Sistema"',1,55),
('P2B','"Resposta"."P2 (Boleto) - Assertiva - Consulta Óbito - PF"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - Assertiva - Valida CEP - PF"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - Assertiva - Valida Nome da Mãe - PF"."Sistema"',1,56),
('P2B','"Resposta"."P2 (<PERSON><PERSON><PERSON>) - Assertiva - Valida Telefone - PF"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - Assertiva - Valida UF - PF"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - SRF - Coleta Declaração IR 2020 - PF"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - SRF - Coleta Declaração IR 2019 - PF"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - RAIS - Coleta Informações - PF"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - Portal da Transparência - Coleta Informações - PF"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - Correios - Valida Restrição CEP - PF"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - Bacen - Coleta Bacen - PF"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - Nações Unidas - Verifica Nome - PF"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - OFAC - Verifica Nome - PF"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - Instantofac - Verifica Nome - PF"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - Int - Base PEP"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - Interpol - Consulta Interpol - PF"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - Int - Cidade do cliente tem fronteira internacional"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - SRF - Cliente com renda comprovada superior a R$ 2.379,98 e não declarou IR"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - Int - Possui mesma referência pessoal cadastrada em mais de 3 propostas"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - Int - Possui mesmo telefone pessoal informado em mais de 3 propostas"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - Int - UF de emissão do CPF diferente do UF da Residência"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - Int - Proposta acima do valor permitido"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - Int - UF do cliente é diferente da UF da loja"."Sistema"',1,56),
('P2B','"Resposta"."P2 (Boleto) - Google - Distância Loja e Endereço Cliente"."Sistema"',1,56),
('P2E','"Resposta"."P2 (Energia) Fornecimento Suspenso"."Sistema"',1,29),
('P2E','"Resposta"."P2 (Energia) Titularidade - PF"."Sistema"',1,51),
('P2E','"Resposta"."P2 (Energia) Validação Luz em Dia - PF"."Sistema"',1,40),
('P3E','"Resposta"."P3 (Energia) - Int - UF de emissão do CPF diferente do UF da Residência"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - Google - Distância Loja e Endereço Cliente"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - Assertiva - Valida Nome da Mãe - PF"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - Assertiva - Consulta Óbito - PF"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - Assertiva - Valida UF - PF"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - Assertiva - Valida Telefone - PF"."Sistema"',1,56), 
('P3E','"Resposta"."P3 (Energia) - Assertiva - Valida CEP - PF"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - Int - Possui mesma referência pessoal cadastrada em mais de 3 propostas"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - Int - Possui mesmo telefone pessoal informado em mais de 3 propostas"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - Int - Mesmo ID da companhia elétrica utilizada por outro CPF nos últimos 3 meses"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - Int - Mesmo ID da companhia elétrica utilizada por outro CPF nos últimos 6 meses"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - Int - Mesmo ID da companhia elétrica utilizada por outro CPF nos últimos 9 meses"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - SRF - Coleta Declaração IR 2019 - PF"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - SRF - Coleta Declaração IR 2020 - PF"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - Correios - Valida Restrição CEP - PF"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - SRF - Cliente com renda comprovada superior a R$ 2.379,98 e não declarou IR"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - Nações Unidas - Verifica Nome - PF"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - OFAC - Verifica Nome - PF"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - Instantofac - Verifica Nome - PF"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - Int - Base PEP"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - Interpol - Consulta Interpol - PF"."Sistema"',1,56),
('P3E','"Resposta"."P3 (Energia) - Int - Cidade do cliente tem fronteira internacional"."Sistema"',1,56),
('P4E','"Resposta"."P4 (Energia) - Google - Distância APP e Endereço Cliente"."Sistema"',1,38),
('P4E','"Resposta"."P4 (Energia) - Google - Distância APP e Loja"."Sistema"',1,38),
('P4E','"Resposta"."P4 (Energia) - Acesso - Avaliação de ponto de corte score biometria facial"."Sistema"',1,38)



update ParametrosReanalise set propostamotivoid = 38 where chave = '"Resposta"."P2 (CDC) - Int - UF do cliente é diferente da UF da loja"."Sistema"'	
update ParametrosReanalise set propostamotivoid = 38 where chave = '"Resposta"."P2 (CDC) - Int - Valor da proposta diferente do intervalo permitido"."Sistema"'
update ParametrosReanalise set propostamotivoid = 38 where chave = '"Resposta"."P2 (CDC) - Int - Plano diferente do intervalo permitido"."Sistema"'
update ParametrosReanalise set propostamotivoid = 49 where chave = '"Resposta"."P2 (CDC) - Int - Cliente com menos de 6 meses de emprego"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - Int - Cliente com renda Comprovada acima da renda Presumida"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - Int - Cliente com renda Comprovada menor do que limite estabelicido"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - Int - Cliente com última atualização cadastral há mais de 180 dias"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - Assertiva - Valida Nome da Mãe - PF"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - Assertiva - Consulta Óbito - PF"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - Assertiva - Valida UF - PF"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - Assertiva - Valida Telefone - PF"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - Assertiva - Valida CEP - PF"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - Int - Possui mesma referência pessoal cadastrada em mais de 3 propostas"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - SRF - Cliente com renda comprovada superior a R$ 2.379,98 e não declarou imposto de renda"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - Int - Possui mesmo telefone pessoal informado em mais de 3 propostas"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - Int - Valida Emissão do CPF pelo UF da Proposta"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - SRF - Coleta Declaração IR 2019 - PF"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - SRF - Coleta Declaração IR 2020 - PF"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - Portal da Transparência - Coleta Informações - PF"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - RAIS - Coleta Informações - PF"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - Correios - Valida Restrição CEP - PF"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - Nações Unidas - Verifica Nome - PF"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - OFAC - Verifica Nome - PF"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - Instantofac - Verifica Nome - PF"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - Int - Base PEP"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - Interpol - Consulta Interpol - PF"."Sistema"'
update ParametrosReanalise set propostamotivoid = 56 where chave = '"Resposta"."P2 (CDC) - Int - Cidade do cliente tem fronteira internacional"."Sistema"'



