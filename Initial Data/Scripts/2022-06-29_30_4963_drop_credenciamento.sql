drop procedure if exists controller.stpCredenciamento
drop procedure if exists dbo.stpCredenciamentoCrud
drop procedure if exists dbo.stpCredenciamentoEstagioGet
drop procedure if exists dbo.stpCredenciamentoGet
drop procedure if exists dbo.stpCredenciamentoStatusGet

delete from api.route where [procedure] = 'controller.stpCredenciamento'
delete from api.route where [procedure] = 'dbo.stpCredenciamentoCrud'
delete from api.route where [procedure] = 'dbo.stpCredenciamentoEstagioGet'
delete from api.route where [procedure] = 'dbo.stpCredenciamentoGet'
delete from api.route where [procedure] = 'dbo.stpCredenciamentoStatusGet'