BEGIN

    --
    -- Id do controller
    -- N<PERSON> necessário definir valor
    --
    declare @controllerId int

    --
    -- Variáveis de inserção
    -- Defina os valores do controlador caso seja inserção
    -- Defina os valores da rota para inserção de nova rota
    --
    declare @controllerName varchar(50) = 'Propostas',
            @controllerUser varchar(50) = 'crefazapisqlstag',
            @controllerDesc varchar(MAX) = 'Acessa a API de propostas',
            @routeName varchar(255) = 'Decisão Proposta Parceiro',
            @routeDesc varchar(MAX) = 'Altera decisão do parceiro sobre a proposta',
            @route varchar(100) = '/api/all/proposta/aprovarReprovar',
            @routeMethod varchar(10) = 'PUT',
            @routeProcedure varchar(255) = 'controller.stpProposta',
            @routeInput varchar(MAX) = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "codigoStatus": "10",
            "propostas": [
                {
                    "codproposta": 1
                }
            ]
        }
    ],
    "required": [
        "codigoStatus",
        "propostas"
    ],
    "properties": {
        "codigoStatus": {
            "$id": "#/properties/codigoStatus",
            "type": "string",
            "title": "The codigoStatus schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "10"
            ]
        },
        "propostas": {
            "$id": "#/properties/propostas",
            "type": "array",
            "title": "The propostas schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [
                    {
                        "codproposta": 1
                    }
                ]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/propostas/items",
                "anyOf": [
                    {
                        "$id": "#/properties/propostas/items/anyOf/0",
                        "type": "object",
                        "title": "The first anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": {},
                        "examples": [
                            {
                                "codproposta": 1
                            }
                        ],
                        "required": [
                            "codproposta"
                        ],
                        "properties": {
                            "codproposta": {
                                "$id": "#/properties/propostas/items/anyOf/0/properties/codproposta",
                                "type": "integer",
                                "title": "The codproposta schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1
                                ]
                            }
                        },
                        "additionalProperties": true
                    }
                ]
            }
        }
    },
    "additionalProperties": true
}',
            @routeOutput varchar(MAX) = '{}',
            @routeResponseId int = 2

    select @controllerId = Id from api.controller where [name] = @controllerName

    if (@controllerId IS NULL)
    BEGIN
        insert into api.controller
        (
            [user],
            [name],
            [description]
        )
        VALUES
        (
            @controllerUser,
            @controllerName,
            @controllerDesc
        )

        set @controllerId = @@IDENTITY
    END

    insert into api.route
    (
        [controller_id],
        [name],
        [description],
        [route],
        [method],
        [procedure],
        [input],
        [output],
        [response_type_id]
    ) values (
        @controllerId,
        @routeName,
        @routeDesc,
        @route,
        @routeMethod,
        @routeProcedure,
        @routeInput,
        @routeOutput,
        @routeResponseId
    )

END
