update api.route set [input] = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "pagina": 1,
            "quantidadePorPagina": 15,
            "ordenacao": "",
            "ordenacaoAsc": false,
            "cpf": "00000000000",
            "nomeAgente": "Edson",
            "tipoData": 0,
            "dataInicial": "2021-06-15",
            "dataFinal": "2021-07-16",
            "etapaId": [
                1
            ],
            "produtoId": [
                1
            ],
            "matrizId": 1,
            "supervisorId": 1,
            "tipoCliente": [
                0
            ],
            "propostaId": null,
            "origemId": null,
            "analistas": []
        }
    ],
    "required": [
        "pagina",
        "quantidadePorPagina",
        "ordenacao",
        "ordenacaoAsc"
    ],
    "properties": {
        "pagina": {
            "$id": "#/properties/pagina",
            "type": "integer",
            "title": "The pagina schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "quantidadePorPagina": {
            "$id": "#/properties/quantidadePorPagina",
            "type": "integer",
            "title": "The quantidadePorPagina schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                15
            ]
        },
        "ordenacao": {
            "$id": "#/properties/ordenacao",
            "type": "string",
            "title": "The ordenacao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                ""
            ]
        },
        "ordenacaoAsc": {
            "$id": "#/properties/ordenacaoAsc",
            "type": "boolean",
            "title": "The ordenacaoAsc schema",
            "description": "An explanation about the purpose of this instance.",
            "default": false,
            "examples": [
                false
            ]
        },
        "cpf": {
            "$id": "#/properties/cpf",
            "type": "string",
            "title": "The cpf schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "00000000000"
            ]
        },
        "nomeAgente": {
            "$id": "#/properties/nomeAgente",
            "type": "string",
            "title": "The nomeAgente schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "Edson"
            ]
        },
        "tipoData": {
            "$id": "#/properties/tipoData",
            "type": "integer",
            "title": "The tipoData schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                0
            ]
        },
        "dataInicial": {
            "$id": "#/properties/dataInicial",
            "type": "string",
            "title": "The dataInicial schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-06-15"
            ]
        },
        "dataFinal": {
            "$id": "#/properties/dataFinal",
            "type": "string",
            "title": "The dataFinal schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-07-16"
            ]
        },
        "etapaId": {
            "$id": "#/properties/etapaId",
            "type": "array",
            "title": "The etapaId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [
                    1
                ]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/etapaId/items",
                "anyOf": [
                    {
                        "$id": "#/properties/etapaId/items/anyOf/0",
                        "type": "integer",
                        "title": "The first anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            1
                        ]
                    }
                ]
            }
        },
        "produtoId": {
            "$id": "#/properties/produtoId",
            "type": "array",
            "title": "The produtoId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
              [
                1
              ]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/produtoId/items",
                "anyOf": [
                    {
                        "$id": "#/properties/produtoId/items/anyOf/0",
                        "type": "integer",
                        "title": "The first anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            1
                        ]
                    }
                ]
            }
        },
        "matrizId": {
            "$id": "#/properties/matrizId",
            "type": "integer",
            "title": "The matrizId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "supervisorId": {
            "$id": "#/properties/supervisorId",
            "type": "integer",
            "title": "The supervisorId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "tipoCliente": {
            "$id": "#/properties/tipoCliente",
            "type": "array",
            "title": "The tipoCliente schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [
                    0
                ]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/tipoCliente/items",
                "anyOf": [
                    {
                        "$id": "#/properties/tipoCliente/items/anyOf/0",
                        "type": "integer",
                        "title": "The first anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            0
                        ]
                    }
                ]
            }
        },
        "propostaId": {
            "$id": "#/properties/propostaId",
            "type": "null",
            "title": "The propostaId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": null,
            "examples": [
                null
            ]
        },
        "origemId": {
            "$id": "#/properties/origemId",
            "type": "null",
            "title": "The origemId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": null,
            "examples": [
                null
            ]
        },
        "analistas": {
            "$id": "#/properties/analistas",
            "type": "array",
            "title": "The analistas schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [
                  1
                ]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/analistas/items"
            }
        }
    },
    "additionalProperties": true
}' where Id = (Select Id from api.route where [route] = '/api/proposta/esteira-credito' and [method] = 'POST')
