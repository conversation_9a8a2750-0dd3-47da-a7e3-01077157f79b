alter table DicionarioDados add constraint PK_DicionarioDados Primary key (id);

create table CamposEditaveis (
    Id int identity(1,1) NOT NULL PRIMARY key,
    CampoId int NOT NULL, 
    EtapaId tinyint NULL,
    MotivoId tinyint NULL,
    Tipo tinyint NULL
)

alter table CamposEditaveis add constraint FK_CamposEditaveis_Campo foreign key (CampoId) references DicionarioDados(Id);

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'0 inclusão - 1 exclusão - Inclui ou Exclui a edição' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CamposEditaveis', @level2type=N'COLUMN',@level2name=N'Tipo'

alter table dbo.DicionarioDados add Obrigatorio bit 