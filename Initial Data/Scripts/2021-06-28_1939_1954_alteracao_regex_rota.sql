/*
nomeCategoria	nomeConvenio	formato
Rota	            ENEL CE	    [A-Z\d],[A-Z\d],[A-<PERSON>\d],[A-Z\d],[A-<PERSON>\d],[A-Z\d],[A-<PERSON>\d],[A-<PERSON>\d]," ","-"," ",[A-Z\d],[A-Z\d]
Rota	            ENEL RJ	    [\d],[\d],[\d],[\d],[\d],[\d],[\d]," ",[\d],[\d]," ",[\d],[\d],[\d],[\d],[\d],[\d]," ","-"," ",[\d]
Lote	            CPFL	    [\d],[\d]
Etapa	            Celesc	    [\d],[\d]

select a.nome as nomeCategoria, c.Nome as nomeConvenio, a.formato 
from ConvenioDados a
join ConvenioDadosCategoria b on a.ConvenioDadosCategoriaId = b.Id
join Convenio c on c.Id = a.ConvenioId
where b.Nome = 'Rota'
*/

UPDATE a
set formato = '[\d],[\d]'
from ConvenioDados a
join ConvenioDadosCategoria b on a.ConvenioDadosCategoriaId = b.Id
where b.Nome = 'Rota'
