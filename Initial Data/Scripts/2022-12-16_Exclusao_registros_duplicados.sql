delete externo.ConsultaDetalhes
from externo.ConsultaDetalhes a with(nolock)
	join externo.Informacao b with(nolock) on a.InformacaoId = b.id
where b.Tag = 'data.comprometimentoRenda'
	and (a.valor = '' 
		 OR 
		 a.valor = 'variável não encontrada'
		 OR
		 a.valor = '0')
	and a.ConsultaId in (select ConsultaId
						 from externo.consultadetalhes c with(nolock)
						 where c.ConsultaId = a.ConsultaId
						 group by ConsultaId, InformacaoId
						 having count(consultaID) > 1)