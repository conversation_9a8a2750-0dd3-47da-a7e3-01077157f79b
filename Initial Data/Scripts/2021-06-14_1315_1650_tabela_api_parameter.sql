create table api.parameter (
    id int identity(1,1),
    [key] varchar(50) not null,
    [value] varchar(255) not null,
    [description] varchar(255)
);
alter table api.parameter add constraint pk_api_parameter primary key (id);
alter table api.parameter add constraint uc_api_parameter_key unique (id);

insert into api.parameter values ('debug', '0','Bit: 1 - será registrado o log de todas as requisições da API. 0 - Não será registrado log de requisições')
