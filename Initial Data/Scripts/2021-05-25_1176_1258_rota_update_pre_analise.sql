BEGIN

    --
    -- Id do controller
    -- N<PERSON> necessário definir valor
    --
    declare @controllerId int

    --
    -- Variáveis de inserção
    -- Defina os valores do controlador caso seja inserção
    -- Defina os valores da rota para inserção de nova rota
    --
    declare @controllerName varchar(50) = 'Propostas',
            @controllerUser varchar(50) = 'crefazapisqlstag',
            @controllerDesc varchar(MAX) = 'Altera Pré-Análise',
            @routeName varchar(255) = 'Atualização de Pré-Análise',
            @routeDesc varchar(MAX) = 'Atualiza Pré-Análise',
            @route varchar(100) = '/api/proposta/pre-analise/{id}',
            @routeMethod varchar(10) = 'PUT',
            @routeProcedure varchar(255) = 'controller.stpProposta',
            @routeInput varchar(MAX) = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "cpf": "46051403019",
            "nome": "Teste bug ",
            "nascimento": "2021-05-03T17:21:04.930Z",
            "telefone": "(27) 95612-3256",
            "ocupacaoId": 1,
            "cep": "29102571",
            "ufId": 8,
            "cidadeId": 1905,
            "bairro": "Praia das Gaivotas",
            "logradouro": "Rua Sebastião Silveira",
            "id": 6
        }
    ],
    "required": [
        "id"
    ],
    "properties": {
        "cpf": {
            "$id": "#/properties/cpf",
            "type": "string",
            "title": "The cpf schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "46051403019"
            ]
        },
        "nome": {
            "$id": "#/properties/nome",
            "type": "string",
            "title": "The nome schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "Teste bug "
            ]
        },
        "nascimento": {
            "$id": "#/properties/nascimento",
            "type": "string",
            "title": "The nascimento schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-05-03T17:21:04.930Z"
            ]
        },
        "telefone": {
            "$id": "#/properties/telefone",
            "type": "string",
            "title": "The telefone schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "(27) 95612-3256"
            ]
        },
        "ocupacaoId": {
            "$id": "#/properties/ocupacaoId",
            "type": "integer",
            "title": "The ocupacaoId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "cep": {
            "$id": "#/properties/cep",
            "type": "string",
            "title": "The cep schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "29102571"
            ]
        },
        "ufId": {
            "$id": "#/properties/ufId",
            "type": "integer",
            "title": "The ufId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                8
            ]
        },
        "cidadeId": {
            "$id": "#/properties/cidadeId",
            "type": "integer",
            "title": "The cidadeId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1905
            ]
        },
        "bairro": {
            "$id": "#/properties/bairro",
            "type": "string",
            "title": "The bairro schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "Praia das Gaivotas"
            ]
        },
        "logradouro": {
            "$id": "#/properties/logradouro",
            "type": "string",
            "title": "The logradouro schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "Rua Sebastião Silveira"
            ]
        },
        "id": {
            "$id": "#/properties/id",
            "type": "integer",
            "title": "The id schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                6
            ]
        }
    },
    "additionalProperties": true
}',
            @routeOutput varchar(MAX) = '{}',
            @routeResponseId int = 2

    select @controllerId = Id from api.controller where [name] = @controllerName

    if (@controllerId IS NULL)
    BEGIN
        insert into api.controller
        (
            [user],
            [name],
            [description]
        )
        VALUES
        (
            @controllerUser,
            @controllerName,
            @controllerDesc
        )

        set @controllerId = @@IDENTITY
    END

    insert into api.route
    (
        [controller_id],
        [name],
        [description],
        [route],
        [method],
        [procedure],
        [input],
        [output],
        [response_type_id]
    ) values (
        @controllerId,
        @routeName,
        @routeDesc,
        @route,
        @routeMethod,
        @routeProcedure,
        @routeInput,
        @routeOutput,
        @routeResponseId
    )

END
-- mudança para criar um novo commit