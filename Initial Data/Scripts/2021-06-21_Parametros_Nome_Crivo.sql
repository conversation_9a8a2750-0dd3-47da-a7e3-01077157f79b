declare @parametroGrupoId int, @parametroId int, @valor varchar(255), @codigo varchar(50)

set @codigo = 'NOME_P1_CRIVO'
set @valor = '---- <PERSON><PERSON> ----'
select @parametroGrupoId = id from Parametro.ParametroGrupo where Nome = 'Crivo - Oferta de Produtos'
insert into Parametro.Parametro values 
(@parametroGrupoId,1,@codigo,'NOME P1 CRIVO','Nomenclatura das politicas do crivo para consultas',1,@valor)
set @parametroId = @@IDENTITY

insert into Parametro.ParametroHistorico values (@parametroId, dbo.getdateBR(),@valor)

set @codigo = 'NOME_P2E_CRIVO'
set @valor = '---- Política - P2 - Energia ----'
select @parametroGrupoId = id from Parametro.ParametroGrupo where Nome = 'Crivo - Oferta de Produtos'
insert into Parametro.Parametro values 
(@parametroGrupoId,1,@codigo,'NOME P2E CRIVO','Nomenclatura das politicas do crivo para consultas',1,@valor)
set @parametroId = @@IDENTITY

insert into Parametro.ParametroHistorico values (@parametroId, dbo.getdateBR(),@valor)

set @codigo = 'NOME_P2B_CRIVO'
set @valor = '---- Política - P2 - Boleto ----'
select @parametroGrupoId = id from Parametro.ParametroGrupo where Nome = 'Crivo - Oferta de Produtos'
insert into Parametro.Parametro values 
(@parametroGrupoId,1,@codigo,'NOME P2B CRIVO','Nomenclatura das politicas do crivo para consultas',1,@valor)
set @parametroId = @@IDENTITY

insert into Parametro.ParametroHistorico values (@parametroId, dbo.getdateBR(),@valor)


set @codigo = 'NOME_P3E_CRIVO'
set @valor = '---- Política - P3 - Energia ----'
select @parametroGrupoId = id from Parametro.ParametroGrupo where Nome = 'Crivo - Oferta de Produtos'
insert into Parametro.Parametro values 
(@parametroGrupoId,1,@codigo,'NOME P3E CRIVO','Nomenclatura das politicas do crivo para consultas',1,@valor)
set @parametroId = @@IDENTITY

insert into Parametro.ParametroHistorico values (@parametroId, dbo.getdateBR(),@valor)

set @codigo = 'NOME_P3.5E_CRIVO'
set @valor = '---- Política - P3.5 - Energia ----'
select @parametroGrupoId = id from Parametro.ParametroGrupo where Nome = 'Crivo - Oferta de Produtos'
insert into Parametro.Parametro values 
(@parametroGrupoId,1,@codigo,'NOME P3.5E CRIVO','Nomenclatura das politicas do crivo para consultas',1,@valor)
set @parametroId = @@IDENTITY

insert into Parametro.ParametroHistorico values (@parametroId, dbo.getdateBR(),@valor)

set @codigo = 'NOME_P3B_CRIVO'
set @valor = '---- Política - P3 - Boleto ----'
select @parametroGrupoId = id from Parametro.ParametroGrupo where Nome = 'Crivo - Oferta de Produtos'
insert into Parametro.Parametro values 
(@parametroGrupoId,1,@codigo,'NOME P3B CRIVO','Nomenclatura das politicas do crivo para consultas',1,@valor)
set @parametroId = @@IDENTITY

insert into Parametro.ParametroHistorico values (@parametroId, dbo.getdateBR(),@valor)

set @codigo = 'NOME_P4E_CRIVO'
set @valor = '---- Política - P4 - Energia ----'
select @parametroGrupoId = id from Parametro.ParametroGrupo where Nome = 'Crivo - Oferta de Produtos'
insert into Parametro.Parametro values 
(@parametroGrupoId,1,@codigo,'NOME P4E CRIVO','Nomenclatura das politicas do crivo para consultas',1,@valor)
set @parametroId = @@IDENTITY

insert into Parametro.ParametroHistorico values (@parametroId, dbo.getdateBR(),@valor)

set @codigo = 'MENSAGEM_SEM_OFERTA_CRIVO'
set @valor = 'Não há produtos ofertados no momento'
select @parametroGrupoId = id from Parametro.ParametroGrupo where Nome = 'Crivo'
insert into Parametro.Parametro values 
(@parametroGrupoId,1,@codigo,'Mensagem sem oferta de produtos','Mensagem a ser apresentada quando o crivo não retornar nenhum produto na oferta',1,@valor)
set @parametroId = @@IDENTITY

insert into Parametro.ParametroHistorico values (@parametroId, dbo.getdateBR(),@valor)

