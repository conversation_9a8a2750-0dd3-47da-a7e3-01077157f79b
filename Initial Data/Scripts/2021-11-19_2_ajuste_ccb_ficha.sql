declare @errado varchar(255),
        @novo varchar(255)

set @errado = 'Local: '
set @novo =   'Local: {{ASSINATURALOCAL}}'

update a set Layout = replace(Layout,@errado,@novo) -- [atualizado], 
--         SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
--         replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] 
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'
and ProdutoId in (select id from Produto where nome in ('CP Refin', 'Boleto'))
and tipo = 2 and TipoDocumento = 'ficha-cadastral'

set @errado = 'Local:'
set @novo =   'Local: {{ASSINATURALOCAL}}'

update a set Layout = replace(Layout,@errado,@novo) -- [atualizado], 
--         SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
--         replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] 
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'
and ProdutoId in (select id from Produto where nome in ('CP Refin', 'Boleto'))
and tipo = 2 and TipoDocumento = 'ccb-negociavel'


set @errado = 'Data: '
set @novo =   'Data: {{ASSINATURADATA}}'
update a set Layout = replace(Layout,@errado,@novo) -- [atualizado], 
        -- SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        -- replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] 
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%' 
and ProdutoId in (select id from Produto where nome in ('CP Refin', 'Boleto'))
and tipo = 2 and TipoDocumento = 'ficha-cadastral'

set @errado = '>Data:'
set @novo =   '>Data: {{ASSINATURADATA}}'
update a set Layout = replace(Layout,@errado,@novo) -- [atualizado], 
        -- SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        -- replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] 
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%' 
and ProdutoId in (select id from Produto where nome in ('CP Refin', 'Boleto'))
and tipo = 2 and TipoDocumento = 'ccb-negociavel'