
alter table LogGeolocalizacaoApp
DROP CONSTRAINT FK_LogGeolocalizacaoApp;  

drop table if exists LogGeolocalizacaoApp;

drop table if exists GeolocalizacaoApp;

create table GeolocalizacaoApp (
    Id int identity(1,1) not null, 
    PessoaId int not null, 
    PropostaId int not null,
	Latitude varchar(50) NOT NULL,
	Longitude varchar(50) NOT NULL,
    DataHora datetime NOT NULL  
)
alter table GeolocalizacaoApp add constraint PK_GeolocalizacaoApp primary key (Id);
alter table GeolocalizacaoApp add constraint FK_GeolocalizacaoApp_Pessoa foreign key (PessoaId) references Pessoa(Id);
alter table GeolocalizacaoApp add constraint FK_GeolocalizacaoApp_Proposta foreign key (PropostaId) references Proposta(Id);