update api.route set [input] = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "motivo": "Foi apresentado o comprovante de pagamento dos debitos citados."
        }
    ],
    "required": [
        "motivo"
    ],
    "properties": {
        "motivo": {
            "$id": "#/properties/motivo",
            "type": "string",
            "title": "The motivo schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "Foi apresentado o comprovante de pagamento dos debitos citados."
            ]
        }
    },
    "additionalProperties": true
}' where [route] = '/api/proposta/reanalise/{id}'