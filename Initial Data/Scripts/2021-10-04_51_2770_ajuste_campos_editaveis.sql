declare @DicionarioDadosId int

-----------
select @DicionarioDadosId = id from DicionarioDados where Tag = 'endereco.cidadeId'
delete from CamposEditaveis where DicionarioDadosId = @DicionarioDadosId


insert into CamposEditaveis
values(@DicionarioDadosId, null, null, 1, 0, 0)

insert into CamposEditaveis
Select @DicionarioDadosId, id as EtapaId, null as MotivoId, 0 as Tipo, 0 as Checagem, 0 as Tela from PropostaEtapa where nome in ('Falha de Comunicação', 'Seleção Oferta')

------------------

select @DicionarioDadosId = id from DicionarioDados where Tag = 'endereco.ufId'
delete from CamposEditaveis where DicionarioDadosId = @DicionarioDadosId


insert into CamposEditaveis
values(@DicionarioDadosId, null, null, 1, 0, 0)

insert into CamposEditaveis
Select @DicionarioDadosId, id as EtapaId, null as MotivoId, 0 as Tip<PERSON>, 0 as Checagem, 0 as Tela from PropostaEtapa where nome in ('Falha de Comunicação', 'Seleção Oferta')
