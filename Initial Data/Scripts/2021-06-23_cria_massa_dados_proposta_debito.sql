declare @idProposta INT,
        @idProduto INT,
        @dataReferencia date,
        @dataVencimento date,
        @valor numeric(15,2),
        @tipo TINYINT,
        @consumo numeric(7,2),
        @situacao TINYINT,
        @codigoBarras varchar(50),
        @descontar BIT,
        @participa bit

set @idProposta = 0
select @idProduto = Id from dbo.Produto where Nome = 'Energia'

while((select max(Id) from dbo.Proposta) > @idProposta)
BEGIN

    set @idProposta = (select top 1 Id from dbo.Proposta where Id > @idProposta and ProdutoId = @idProduto)
    set @dataVencimento = dateadd(M, floor(1+rand()*(12-1)), dbo.getdateBR())
    set @dataReferencia = DATEADD(M, -1, @dataVencimento)
    set @valor = cast(((floor(100+rand()*(10000 - 100)))/100) as numeric(15,2))
    set @codigoBarras = concat('8365000000',substring(cast(@valor as varchar(max)),1,1),'7',replace(substring(cast(@valor as varchar(max)),2,len(cast(@valor as varchar(max)))),'.',''),'00513004115037098114900033199481')
    set @tipo = 1
    set @consumo = cast(((floor(100+rand()*(10000 - 100)))/100) as numeric(7,2))
    set @situacao = ((floor(1+rand()*(10-8))))
    set @participa = cast((floor(1+rand()*10)) as int)%2
    set @descontar = cast((floor(1+rand()*10)) as int)%2

    if(@idProposta IS NOT NULL)
    begin
      insert into dbo.PropostaDebito (
          PropostaId,
          DataReferencia,
          DataVencimento,
          Valor,
          Tipo,
          Consumo,
          Situacao,
          CodigoBarras,
          Descontar,
          Participa
      ) values (
          @idProposta,
          @dataReferencia,
          @dataVencimento,
          @valor,
          @tipo,
          @consumo,
          @situacao,
          @codigoBarras,
          @descontar,
          @participa
      )
    end

END

select * from dbo.PropostaDebito
