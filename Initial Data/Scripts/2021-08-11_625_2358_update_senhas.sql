update seguranca.usuario set senha = convert(varchar(max), HASHBYTES('MD5', '123456'), 2) where senha = convert(varchar(max), HASHBYTES('SHA2_256', '123456'), 1)
update seguranca.usuario set senha = convert(varchar(max), HASHBYTES('MD5', 'edson.alves'), 2) where senha = '$2a$12$hujbblmPYC99yv7eJLzsIua8hj.FY2FdSjf7uvXuIcHl.E5DVQeUe'
update seguranca.usuario set senha = convert(varchar(max), HASHBYTES('MD5', '<EMAIL>'), 2) where senha = '$2a$12$F1V8c51oTcznHsgYYscwV.hwA6ymftTDrQ1mp806pRlaFz7vaEL8G'
update seguranca.usuario set senha = convert(varchar(max), HASHBYTES('MD5', 'D@zt1Sp#mcbFfe'), 2) where [login] = 'crivo.integracao'

