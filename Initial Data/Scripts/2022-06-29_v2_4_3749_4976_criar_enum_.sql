-- 4976: <PERSON>ada<PERSON><PERSON> En<PERSON>'s

delete from Parametro.enum where codigo = 'classificacao'
if not exists (select * from Parametro.enum where codigo = 'classificacao')
begin
    insert into Parametro.enum
    select 'classificacao', '[{"id":1,"nome":"Call center"},{"id":2,"nome":"Hibrido"},{"id":3,"nome":"Home Office"},{"id":4,"nome":"Presencial"}]'
end

delete from Parametro.enum where codigo = 'certificacao'
if not exists (select * from Parametro.enum where codigo = 'certificacao')
begin
    insert into Parametro.enum
    select 'certificacao', '[{"id":1,"nome":"ABECIP"},{"id":2,"nome":"ACREFI"},{"id":3,"nome":"ANEPS"},{"id":4,"nome":"ASSBANDF"},{"id":5,"nome":"FEBRABAN"}]'
end

delete from Parametro.enum where codigo = 'tipoCertificacao'
if not exists (select * from Parametro.enum where codigo = 'tipoCertificacao')
begin
    insert into Parametro.enum
    select 'tipoCertificacao', '[{"id":1,"nome":"Completa"},{"id":2,"nome":"Consignado"},{"id":3,"nome":"Pldft - Empréstimo Consignado Para Correspondente Bancário"},{"id":4,"nome":"Lgpd Para Correspondentes No País - Res. 4.935"},{"id":5,"nome":"Pldft No Consórcio Para Correspondente Bancário"}]'
end


