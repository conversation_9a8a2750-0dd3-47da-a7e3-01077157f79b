update PropostaEtapa set FL_Followup = 1 where Nome in (
'Aguard. Análise',
'<PERSON>',
'Proposta Pendente',
'Fila Contato',
'Ligando',
'Contato Pendente',
'Aguard. Assinatura',
'Aguard. Validação',
'Análise Promotora',
'Fila Pagamento',
'Pago ao Cliente',
'Aguard. Checagem',
'Aguard. Averbação' 
)

update PropostaEtapa set FL_Acumulativo = 1 where Nome in (
'Aguard. Análise',
'Em Análise',
'Proposta Pendente',
'Fila Contato',
'Ligando',
'Contato Pendente',
'Aguard. Assinatura',
'Aguard. Validação',
'Análise Promotora',
'Aguard. Checagem',
'Aguard. Averbação'  
)

insert into dbo.OrdenacaoFallowup values
((select id from PropostaEtapa where Nome = 'Aguard. Análise'), 1, 1),
((select id from PropostaEtapa where Nome = 'Em Análise'), 2, 1),
((select id from PropostaEtapa where Nome = 'Proposta Pendente'), 3, 1),
((select id from PropostaEtapa where Nome = 'Aguard. Checagem'), 4, 1),
((select id from PropostaEtapa where Nome = 'Aguard. Averbação'), 5, 1),
((select id from PropostaEtapa where Nome = 'Fila Contato'), 6, 1),
((select id from PropostaEtapa where Nome = 'Ligando'), 7, 1),
((select id from PropostaEtapa where Nome = 'Contato Pendente'), 8, 1),
((select id from PropostaDecisao where Nome = 'Negada'), 9, 2),
((select id from PropostaDecisao where Nome = 'Cancelada'), 10, 2),
((select id from PropostaEtapa where Nome = 'Aguard. Assinatura'), 11, 1),
((select id from PropostaEtapa where Nome = 'Aguard. Validação'), 12, 1),
((select id from PropostaEtapa where Nome = 'Análise Promotora'), 13, 1),
((select id from PropostaEtapa where Nome = 'Fila Pagamento'), 14, 1),
((select id from PropostaEtapa where Nome = 'Pago ao Cliente'), 15, 1)