alter table PropostaMotivo alter column Nome varchar(255)

insert into PropostaMotivo values
('Negado por restrições diversas'),
('Negado divergência CPF'),
('Negado divergência RG'),
('Negado conta óbito'),
('Dados foram devidamente informados'),
('Todas as checagens foram realizadas'),
('Os campos obrigatorios estão corretamente informados')


declare @decisaoId INT
declare @etapaId INT
declare @motivoId INT

select @decisaoId = id from PropostaDecisao where Nome = 'Negada'

select @motivoId = id from PropostaMotivo where Nome = 'Negado por restrições diversas'
insert into PropostaDecisaoPropostaMotivo values
(@decisaoId,@motivoId)

select @motivoId = id from PropostaMotivo where Nome = 'Negado divergência CPF'
insert into PropostaDecisaoPropostaMotivo values
(@decisaoId,@motivoId)

select @motivoId = id from PropostaMotivo where Nome = 'Negado divergência RG'
insert into PropostaDecisaoPropostaMotivo values
(@decisaoId,@motivoId)

select @motivoId = id from PropostaMotivo where Nome = 'Negado conta óbito'
insert into PropostaDecisaoPropostaMotivo values
(@decisaoId,@motivoId)

select @decisaoId = id from PropostaDecisao where Nome = 'Cancelada'

select @motivoId = id from PropostaMotivo where Nome = 'Cancelamento Forçado'
insert into PropostaDecisaoPropostaMotivo values
(@decisaoId,@motivoId)

select @motivoId = id from PropostaMotivo where Nome = 'Expirado por Tempo Decorrido'
insert into PropostaDecisaoPropostaMotivo values
(@decisaoId,@motivoId)

select @motivoId = id from PropostaMotivo where Nome = 'Cancelado pelo parceiro'
insert into PropostaDecisaoPropostaMotivo values
(@decisaoId,@motivoId)

select @etapaId = id from PropostaEtapa where Nome = 'Falha de Comunicação'

select @motivoId = id from PropostaMotivo where Nome = 'Dados foram devidamente informados'
insert into PropostaEtapaPropostaMotivo values
(@etapaId,@motivoId)
select @motivoId = id from PropostaMotivo where Nome = 'Todas as checagens foram realizadas'
insert into PropostaEtapaPropostaMotivo values
(@etapaId,@motivoId)
select @motivoId = id from PropostaMotivo where Nome = 'Os campos obrigatorios estão corretamente informados'
insert into PropostaEtapaPropostaMotivo values
(@etapaId,@motivoId)

select @etapaId = id from PropostaEtapa where Nome = 'Seleção Oferta'

select @motivoId = id from PropostaMotivo where Nome = 'Dados foram devidamente informados'
insert into PropostaEtapaPropostaMotivo values
(@etapaId,@motivoId)
select @motivoId = id from PropostaMotivo where Nome = 'Todas as checagens foram realizadas'
insert into PropostaEtapaPropostaMotivo values
(@etapaId,@motivoId)
select @motivoId = id from PropostaMotivo where Nome = 'Os campos obrigatorios estão corretamente informados'
insert into PropostaEtapaPropostaMotivo values
(@etapaId,@motivoId)

select @etapaId = id from PropostaEtapa where Nome = 'Digitando'

select @motivoId = id from PropostaMotivo where Nome = 'Dados foram devidamente informados'
insert into PropostaEtapaPropostaMotivo values
(@etapaId,@motivoId)
select @motivoId = id from PropostaMotivo where Nome = 'Todas as checagens foram realizadas'
insert into PropostaEtapaPropostaMotivo values
(@etapaId,@motivoId)
select @motivoId = id from PropostaMotivo where Nome = 'Os campos obrigatorios estão corretamente informados'
insert into PropostaEtapaPropostaMotivo values
(@etapaId,@motivoId)

select @etapaId = id from PropostaEtapa where Nome = 'Em Análise'

select @motivoId = id from PropostaMotivo where Nome = 'Dados foram devidamente informados'
insert into PropostaEtapaPropostaMotivo values
(@etapaId,@motivoId)
select @motivoId = id from PropostaMotivo where Nome = 'Todas as checagens foram realizadas'
insert into PropostaEtapaPropostaMotivo values
(@etapaId,@motivoId)
select @motivoId = id from PropostaMotivo where Nome = 'Os campos obrigatorios estão corretamente informados'
insert into PropostaEtapaPropostaMotivo values
(@etapaId,@motivoId)

