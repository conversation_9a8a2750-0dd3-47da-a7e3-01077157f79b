


update PropostaMotivo set nome = 'Anexar CCB via negociável' where id = 1

update PropostaMotivo set nome = 'Sem contato com o cliente' where id = 2

update PropostaMotivo set nome = 'Comprovante de residência' where id = 3

update PropostaMotivo set nome = 'CPF pendente, regularizar na Receita Federal' where id = 4

update PropostaMotivo set nome = 'Cliente solicitou o cancelamento' where id = 5

update PropostaMotivo set nome = 'Pendente documento' where id = 6

update PropostaMotivo set nome = 'Extrato bancário' where id = 7

update PropostaMotivo set nome = 'Espécie do benefício não atendido para esse produto' where id = 8

update PropostaMotivo set nome = 'Telefone inválido' where id = 9

update PropostaMotivo set nome = 'CPF do cliente incorreto na cia. Verificar orientações internas na proposta' where id = 10

update PropostaMotivo set nome = 'Cliente não está ciente dos valores' where id = 11

update PropostaMotivo set nome = 'Cliente não está ciente da assinatura por APP' where id = 12

update PropostaMotivo set nome = 'Assinatura divergente' where id = 13

update PropostaMotivo set nome = 'Assinatura ou selfie fora dos padrões, refazer processo no APP' where id = 14

update PropostaMotivo set nome = 'Atualizar calculo conforme orientações internas na proposta' where id = 15

update PropostaMotivo set nome = 'Cia elétrica apresenta instabilidade, aguardando normalização para análise' where id = 16

update PropostaMotivo set nome = 'Classificação profissional não elegivel ao produto' where id = 17

update PropostaMotivo set nome = 'Cliente alega não ter assinado o contrato' where id = 18

update PropostaMotivo set nome = 'Cliente analfabeto não é elegível a modalidade digital' where id = 19

update PropostaMotivo set nome = 'Cliente com histórico de quitação antecipada' where id = 20

update PropostaMotivo set nome = 'Cliente deve entrar em contato com o sac: 0800 052 5051' where id = 21

update PropostaMotivo set nome = 'Cliente em processo de troca de titularidade' where id = 22

update PropostaMotivo set nome = 'Cliente já possui proposta em andamento' where id = 23

update PropostaMotivo set nome = 'Cliente não reside no endereço cadastrado' where id = 24

update PropostaMotivo set nome = 'Cliente possui empréstimo recente' where id = 25

update PropostaMotivo set nome = 'Cliente pulou a etapa de assinatura, refazer processo no APP' where id = 26

update PropostaMotivo set nome = 'Cliente pulou etapa da selfie, refazer processo no APP' where id = 27

update PropostaMotivo set nome = 'Cliente se recusa a realizar as confirmações' where id = 28

update PropostaMotivo set nome = 'Cliente sem fornecimento de energia, suspenso ou inativo' where id = 29

update PropostaMotivo set nome = 'Comprovante do tempo de atividade' where id = 30

update PropostaMotivo set nome = 'Contracheque' where id = 31

update PropostaMotivo set nome = 'Contrato anexado sem assinatura' where id = 32

update PropostaMotivo set nome = 'Contrato divergente' where id = 33

update PropostaMotivo set nome = 'CPF suspenso na receita federal' where id = 34

update PropostaMotivo set nome = 'Dados bancários divergentes' where id = 35

update PropostaMotivo set nome = 'Data de nascimento divergente' where id = 36

update PropostaMotivo set nome = 'Detalhamento de crédito' where id = 37

update PropostaMotivo set nome = 'Fora da política de crédito' where id = 38

update PropostaMotivo set nome = 'Histórico de consumo indisponível, orientar ao cliente a atualização do cadastro junto a cia elétrica' where id = 39

update PropostaMotivo set nome = 'Histórico na cia elétrica incompatível com política de crédito' where id = 40

update PropostaMotivo set nome = 'Inconsistência na confirmação' where id = 41

update PropostaMotivo set nome = 'Laudo médico' where id = 42

update PropostaMotivo set nome = 'Margem insuficiente para novo empréstimo' where id = 43

update PropostaMotivo set nome = 'Proposta liberada para assinatura no app' where id = 44

update PropostaMotivo set nome = 'Proposta recusada na análise de documentos' where id = 45

update PropostaMotivo set nome = 'Sem contato com o RH' where id = 46

update PropostaMotivo set nome = 'Sem contato com referencias' where id = 47

update PropostaMotivo set nome = 'Solicitado pela loja' where id = 48

update PropostaMotivo set nome = 'Tempo mínimo de trabalho' where id = 49

update PropostaMotivo set nome = 'Unidade consumidora incorreta' where id = 50

update PropostaMotivo set nome = 'Unidade consumidora não pertence ao cliente' where id = 51

update PropostaMotivo set nome = 'Valor ajustado para o luz em dia, validar faturas para pagamento dentro da proposta' where id = 52


set identity_insert propostamotivo on 
insert into PropostaMotivo(Nome,id) values
('Nome do cliente não corresponde ao CPF cadastrado',53),
('Cliente com acordo em andamento',54),
('Cliente com uf diferente da região de atuação',55),
('Informações adicionais crivo',56),
('Classificação profissional incorreta',57),
('Redigitar proposta com a UF correta conforme endereço do cliente',58)
set identity_insert propostamotivo off 

set identity_insert propostamotivo on 
insert into PropostaMotivo(Nome,id) values
('Outros',100),
('Problemas na Averbação com a Cia Elétrica',101),
('Problemas com o Depósito ao Cliente',102),
('Vencimento do Boleto de REFIN',103),
('Cancelado pelo financeiro',104),
('Expirado por tempo',105)
set identity_insert propostamotivo off 

declare @update table (grupoid int,grupohistoricoid int,MotivoId int,motivonovo int)

insert into @update
select b.Id,b.GrupoHistoricoId,b.PropostaMotivoId,c.id
from PropostaMotivo a with(nolock)
join PropostaMotivoGrupoHistorico b with(nolock) on b.PropostaMotivoId = a.id
join PropostaMotivo c with(nolock) on c.Nome = a.nome and c.id >= 100
where a.Nome in('Outros','Problemas na Averbação com a Cia Elétrica','Problemas com o Depósito ao Cliente','Vencimento do Boleto de REFIN','Cancelado pelo financeiro','Expirado por tempo')
and a.id < 100

update a
    set a.PropostaMotivoId = b.motivonovo
from PropostaMotivoGrupoHistorico a
join @update b on b.grupoid = a.id and b.grupohistoricoid = a.GrupoHistoricoId

delete from PropostaMotivo 
where Nome in('Outros','Problemas na Averbação com a Cia Elétrica','Problemas com o Depósito ao Cliente','Vencimento do Boleto de REFIN','Cancelado pelo financeiro','Expirado por tempo')
and id < 100


