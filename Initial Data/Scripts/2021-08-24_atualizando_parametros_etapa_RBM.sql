declare @etapa varchar(3)

select @etapa = cast(id as varchar) from propostaEtapa where nome = 'Fila Pagamento'

update b
    set valor = @etapa
from parametro.parametro a
    join parametro.parametrohistorico b on a.id = b.parametroid
where codigo = 'RBM_ETAPA_EXPORTACAO'

update a
    set valor = @etapa
from parametro.parametro a
where codigo = 'RBM_ETAPA_EXPORTACAO'

update b
    set valor = @etapa
from parametro.parametro a
    join parametro.parametrohistorico b on a.id = b.parametroid
where codigo = 'RBM_ETAPA_FORMALIZACAO'

update a
    set valor = @etapa
from parametro.parametro a
where codigo = 'RBM_ETAPA_FORMALIZACAO'


update a
    set valor = '[{"dataPrimeiroContrato": false},{"dataUltimoContrato": false},{"bloqueioCpf": false},{"fonePessoal": false},{"parcelasPagas90Dias": false},{"parcelasPagas180Dias": false},{"parcelasPagas270Dias": false},{"parcelasPagas360Dias": false},{"parcelasPagas720Dias": false},{"parcelasPagas1080Dias": false},{"parcelasPagasVidaInteira": false},{"scoreNegadosUltimos15Dias": false},{"quantidadeDeContratosTomadosPorProduto": false},{"quantidadeDeContratosEmCursoPorProduto": false},{"quantidadeDeDiasAtrasoPorProduto": false},{"quantidadeDiasDesdaUltimaAlteracaoRenda": false},{"quantidadeDiasDeClientela": false},{"porcentualPagamentoPrimeiroContrato": false},{"porcentualPagamentoContratoEmCurso": false},{"maiorValorContratado": false},{"quantidadePropostasFinaizadas": false},{"ultimoAtrasoDiaProduto": false},{"estaDeAcordoComCobrancaEmCurso": false},{"quantidadeDiasAtrasoDeAcordoCobranca": false},{"comprometimentoRenda": false}]'
from parametro.parametro a
where codigo = 'RBM_VARIAVEIS_COMPORTAMENTAIS'

insert into Parametro.ParametroHistorico 
select Id, dbo.getdateBR(), Valor from Parametro.Parametro where codigo = 'RBM_VARIAVEIS_COMPORTAMENTAIS'
