--  
insert into Integracao.DeParaRBMInfo (Nome) 
VALUES ('Estado Civil'), ('Grau de Instrução'), ('Tipo Conta'), ('Modalidade Conta')


--
INSERT into integracao.DeParaRBMDetalhe (DeParaRBMInfoId, RBMId, Descricao)
values 
(1,1,'SOLTEIRO'),
(1,2,'CASADO'),
(1,3,'DIVORCIADO'),
(1,4,'SEPARADO'),
(1,5,'VIUVO'),
(2,1, 'ENSINO FUNDAMENTAL COMPLETO'),
(2,2, '<PERSON><PERSON>IN<PERSON> FUNDAMENTAL INCOMPLETO'),
(2,3, 'ENSINO MÉDIO COMPLETO'),
(2,4, 'ENSINO MÉDIO INCOMPLETO'),
(2,5, 'MESTRADO'),
(2,6, 'PÓS-GRADUAÇÃO'),
(2,7, 'SUPERIOR'),
(2,8, 'COMPELTO'),
(2,9, 'DOUTORADO'),
(3,1,'CORRENTE'),
(3,2,'POUPANÇA'),
(3,3,'<PERSON>IX<PERSON> FÁCIL'),
(4,1,'INDIVIDUAL'),
(4,2,'CONJUNTA'),
(4,3,'TERCEIROS')


---
INSERT into integracao.DeParaRBM (DeParaRBMDetalheId, CodeId)
values  (1,0), (2, 1), (3, 2), (4, 2), (5, 3),
        (6,1), (7,2), (8,3), (9,4), (10,5), (11,6), (12,7), (14,8),
        (15,0), (16, 1), (17, 2),
        (18,0), (19, 1)
