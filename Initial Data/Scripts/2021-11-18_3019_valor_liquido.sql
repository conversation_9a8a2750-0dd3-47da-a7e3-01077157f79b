declare @errado varchar(255),
        @novo varchar(255)

set @errado = 'Valor Líquido: R$ '
set @novo =   'Valor Líquido: '
update a set Layout = replace(Layout,@errado,@novo)/*  [atualizado], 
        SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100) [como estava], 
        replace(SUBSTRING(Layout,PATINDEX('%'+@errado+'%',Layout)-20,100),@errado,@novo) [como fica] */
    from VersaoLayoutContrato a 
where Layout like '%'+@errado+'%'