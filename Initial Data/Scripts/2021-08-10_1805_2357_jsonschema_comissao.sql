declare @input varchar(max) = 
'{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "array",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": [],
    "examples": [
        [
            {
                "comissaoId": 1,
                "operacaoRMB": 562,
                "propostaId": 25,
                "valorComisssao": 500.41,
                "dataHoraPagamento": null,
                "cpfcnpj": "00000000001",
                "cliente": "Fulano",
                "tipoLancamento": 0,
                "valorOperacao": 5400,
                "valorLiquido": 4500,
                "prazo": 1,
                "produto": 7,
                "uf": "ES",
                "percentualComissao": 5,
                "empresa": "Crefaz",
                "dataHoraComissao": "2021-06-25T18:20:21.759Z",
                "matriz": "Crefaz"
            },
            {
                "comissaoId": 2,
                "operacaoRMB": 562,
                "propostaId": 25,
                "valorComisssao": 500.41,
                "dataHoraPagamento": null,
                "cpfcnpj": "00000000001",
                "cliente": "Fulano",
                "tipoLancamento": 0,
                "valorOperacao": 5400,
                "valorLiquido": 4500,
                "prazo": 2,
                "produto": 1,
                "uf": "ES",
                "percentualComissao": 5,
                "empresa": "Crefaz",
                "dataHoraComissao": "2021-06-25T18:20:21.759Z",
                "matriz": "Crefaz"
            }
        ]
    ],
    "additionalItems": true,
    "items": {
        "$id": "#/items",
        "anyOf": [
            {
                "$id": "#/items/anyOf/0",
                "type": "object",
                "title": "The first anyOf schema",
                "description": "An explanation about the purpose of this instance.",
                "default": {},
                "examples": [
                    {
                        "comissaoId": 1,
                        "operacaoRMB": 562,
                        "propostaId": 25,
                        "valorComisssao": 500.41,
                        "dataHoraPagamento": null,
                        "cpfcnpj": "00000000001",
                        "cliente": "Fulano",
                        "tipoLancamento": 0,
                        "valorOperacao": 5400,
                        "valorLiquido": 4500,
                        "prazo": 1,
                        "produto": 7,
                        "uf": "ES",
                        "percentualComissao": 5,
                        "empresa": "Crefaz",
                        "dataHoraComissao": "2021-06-25T18:20:21.759Z",
                        "matriz": "Crefaz"
                    }
                ],
                "required": [
                    "comissaoId",
                    "operacaoRMB",
                    "valorComisssao",
                    "cpfcnpj",
                    "cliente",
                    "tipoLancamento",
                    "valorOperacao",
                    "valorLiquido",
                    "prazo",
                    "produto",
                    "uf",
                    "percentualComissao",
                    "empresa",
                    "dataHoraComissao",
                    "matriz"
                ],
                "properties": {
                    "comissaoId": {
                        "$id": "#/items/anyOf/0/properties/comissaoId",
                        "type": "integer",
                        "title": "The comissaoId schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            1
                        ]
                    },
                    "operacaoRMB": {
                        "$id": "#/items/anyOf/0/properties/operacaoRMB",
                        "type": "integer",
                        "title": "The operacaoRMB schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            562
                        ]
                    },
                    "propostaId": {
                        "$id": "#/items/anyOf/0/properties/propostaId",
                        "type": "integer",
                        "title": "The propostaId schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            25
                        ]
                    },
                    "valorComisssao": {
                        "$id": "#/items/anyOf/0/properties/valorComisssao",
                        "type": "number",
                        "title": "The valorComisssao schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0.0,
                        "examples": [
                            500.41
                        ]
                    },
                    "dataHoraPagamento": {
                        "$id": "#/items/anyOf/0/properties/dataHoraPagamento",
                        "type": "null",
                        "title": "The dataHoraPagamento schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": null,
                        "examples": [
                            null
                        ]
                    },
                    "cpfcnpj": {
                        "$id": "#/items/anyOf/0/properties/cpfcnpj",
                        "type": "string",
                        "title": "The cpfcnpj schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": "",
                        "examples": [
                            "00000000001"
                        ]
                    },
                    "cliente": {
                        "$id": "#/items/anyOf/0/properties/cliente",
                        "type": "string",
                        "title": "The cliente schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": "",
                        "examples": [
                            "Fulano"
                        ]
                    },
                    "tipoLancamento": {
                        "$id": "#/items/anyOf/0/properties/tipoLancamento",
                        "type": "integer",
                        "title": "The tipoLancamento schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            0
                        ]
                    },
                    "valorOperacao": {
                        "$id": "#/items/anyOf/0/properties/valorOperacao",
                        "type": "integer",
                        "title": "The valorOperacao schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            5400
                        ]
                    },
                    "valorLiquido": {
                        "$id": "#/items/anyOf/0/properties/valorLiquido",
                        "type": "integer",
                        "title": "The valorLiquido schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            4500
                        ]
                    },
                    "prazo": {
                        "$id": "#/items/anyOf/0/properties/prazo",
                        "type": "integer",
                        "title": "The prazo schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            1
                        ]
                    },
                    "produto": {
                        "$id": "#/items/anyOf/0/properties/produto",
                        "type": "integer",
                        "title": "The produto schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            7
                        ]
                    },
                    "uf": {
                        "$id": "#/items/anyOf/0/properties/uf",
                        "type": "string",
                        "title": "The uf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": "",
                        "examples": [
                            "ES"
                        ]
                    },
                    "percentualComissao": {
                        "$id": "#/items/anyOf/0/properties/percentualComissao",
                        "type": "integer",
                        "title": "The percentualComissao schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            5
                        ]
                    },
                    "empresa": {
                        "$id": "#/items/anyOf/0/properties/empresa",
                        "type": "string",
                        "title": "The empresa schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": "",
                        "examples": [
                            "Crefaz"
                        ]
                    },
                    "dataHoraComissao": {
                        "$id": "#/items/anyOf/0/properties/dataHoraComissao",
                        "type": "string",
                        "title": "The dataHoraComissao schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": "",
                        "examples": [
                            "2021-06-25T18:20:21.759Z"
                        ]
                    },
                    "matriz": {
                        "$id": "#/items/anyOf/0/properties/matriz",
                        "type": "string",
                        "title": "The matriz schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": "",
                        "examples": [
                            "Crefaz"
                        ]
                    }
                },
                "additionalProperties": true
            }
        ]
    }
}'

update api.route set [input] = @input where route = '/api/comissao' and method = 'POST'