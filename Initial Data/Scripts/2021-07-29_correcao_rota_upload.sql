delete api.route WHERE [route] = '/api/comissao/upload-nota' and [method] = 'POST'

update api.route set [route] = '/api/comissao/upload-nota' where [route] = '/api/comissao/upload-nota/{id}'

update api.route set [method] = 'POST' where [route] = '/api/relatorio-comissoes/notas'

update api.route set [input] = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "array",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": [],
    "examples": [
        [
            {
                "id": 916,
                "nomeArquivo": "nome do arquivo nf",
                "mesAnoReferencia": "julho/2021"
            },
            {
                "id": 915,
                "nomeArquivo": "nome do arquivo nf2",
                "mesAnoReferencia": "agosto/2021"
            }
        ]
    ],
    "additionalItems": true,
    "items": {
        "$id": "#/items",
        "anyOf": [
            {
                "$id": "#/items/anyOf/0",
                "type": "object",
                "title": "The first anyOf schema",
                "description": "An explanation about the purpose of this instance.",
                "default": {},
                "examples": [
                    {
                        "id": 916,
                        "nomeArquivo": "nome do arquivo nf",
                        "mesAnoReferencia": "julho/2021"
                    }
                ],
                "required": [
                    "id",
                    "nomeArquivo",
                    "mesAnoReferencia"
                ],
                "properties": {
                    "id": {
                        "$id": "#/items/anyOf/0/properties/id",
                        "type": "integer",
                        "title": "The id schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                            916
                        ]
                    },
                    "nomeArquivo": {
                        "$id": "#/items/anyOf/0/properties/nomeArquivo",
                        "type": "string",
                        "title": "The nomeArquivo schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": "",
                        "examples": [
                            "nome do arquivo nf"
                        ]
                    },
                    "mesAnoReferencia": {
                        "$id": "#/items/anyOf/0/properties/mesAnoReferencia",
                        "type": "string",
                        "title": "The mesAnoReferencia schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": "",
                        "examples": [
                            "julho/2021"
                        ]
                    }
                },
                "additionalProperties": true
            }
        ]
    }
}' where [route] = '/api/comissao/upload-nota'