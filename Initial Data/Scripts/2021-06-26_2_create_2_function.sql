create FUNCTION dbo.fncTempoAnalise(@analistaId int, @PropostaId int, @tipo int = 1)
RETURNS varchar AS
BEGIN
    declare @dateTime datetime,
            @tempoAnalise int,
            @PropostaEtapaIdOld smallint, 
            @PropostaEtapaIdNew smallint, 
            @PropostaDecisaoIdOld smallint, 
            @PropostaDecisaoIdNew smallint,
            @tempoMedioH char(2),
            @tempoMedioM char(2)

        select @dateTime = max([Data]) from PropostaStatusHistorico a with(nolock) where a.PropostaId = @PropostaId and AnalistaId = @analistaId
 
        select top 1 @PropostaEtapaIdOld = a.PropostaEtapaId, @PropostaDecisaoIdOld = a.PropostaDecisaoId
        from PropostaStatusHistorico a with(nolock)
        where a.[Data] < @dateTime
        and a.PropostaId = @PropostaId
        order by a.Id desc
 
        select top 1 @PropostaEtapaIdNew = a.PropostaEtapaId, @PropostaDecisaoIdNew = a.PropostaDecisaoId
        from PropostaStatusHistorico a with(nolock)
        where a.[Data] <= dbo.getdateBR()
        and a.PropostaId = @PropostaId
        order by a.Id desc
 
        if isnull(@PropostaDecisaoIdOld, -1) <> isnull(@PropostaDecisaoIdNew, -1) or @PropostaEtapaIdOld <> @PropostaEtapaIdNew
        begin
            set @tempoAnalise = cast((select DATEDIFF(minute,max(a.DataAcao),dbo.getdateBR()) from PropostaStatusHistorico a with(nolock) where a.PropostaId = @PropostaId and a.DataAcao is not null) as int)
        end
        if(@tipo = 1) --retorna varchar no formato time
        begin 
            if(@tempoAnalise is null)
            begin 
                return cast('00:00' as varchar)
            end
            else
            begin
                set @tempoMedioH = cast(@tempoAnalise/60 as varchar)
                set @tempoMedioM = cast(@tempoAnalise % 60 as varchar)
            
                return cast(cast(@tempoMedioH+':'+@tempoMedioM as time) as varchar)
            end
        end
        if(@tipo = 2) --retorna diferença de minutos int
        begin
            if(@tempoAnalise is null)
            begin 
                return '0'
            end
            else
            begin
                return cast(@tempoAnalise as varchar)
            end
        end
    return null
END