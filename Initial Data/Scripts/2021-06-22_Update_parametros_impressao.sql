update dbo.Documento set DocumentoTipoId = null where Nome = 'DOCUMENTO DE IDENTIFICAÇÃO'
update dbo.Documento set DocumentoTipoId = null where Nome = 'COMPROVANTE DE RESIDÊNCIA'
update dbo.Documento set DocumentoTipoId = null where Nome = 'COMPROVANTE DE RENDA'
update dbo.Documento set DocumentoTipoId = null where Nome = 'COMPROVANTE DE TEMPO DE ATIVIDADE'
update dbo.Documento set DocumentoTipoId = null where Nome = 'VIA NEGOCIAVEL CCB'
update dbo.Documento set DocumentoTipoId = null where Nome = 'AUTORIZAÇÃO PARA DEBITO'
update dbo.Documento set DocumentoTipoId = null where Nome = 'TERMO LUZ EM DIA'
update dbo.Documento set DocumentoTipoId = 1 where Nome = 'CCB NEGOCIÁVEL'
update dbo.Documento set DocumentoTipoId = 2 where Nome = 'CCB NEGOCIÁVEL'
update dbo.Documento set DocumentoTipoId = 4 where Nome = 'CCB NEGOCIÁVEL'
update dbo.Documento set DocumentoTipoId = 9 where Nome = 'CCB NÃO NEGOCIÁVEL'
update dbo.Documento set DocumentoTipoId = 6 where Nome = 'FICHA CADASTRAL'
update dbo.Documento set DocumentoTipoId = null where Nome = 'BOLETO'
update dbo.Documento set DocumentoTipoId = null where Nome = 'DOCUMENTO DE IDENTIFICAÇÃO'
update dbo.Documento set DocumentoTipoId = null where Nome = 'COMPROVANTE DE RESIDÊNCIA'
update dbo.Documento set DocumentoTipoId = null where Nome = 'COMPROVANTE DE RENDA'
update dbo.Documento set DocumentoTipoId = null where Nome = 'COMPROVANTE DE TEMPO DE ATIVIDADE'
update dbo.Documento set DocumentoTipoId = null where Nome = 'VIA NEGOCIAVEL CCB'
update dbo.Documento set DocumentoTipoId = null where Nome = 'AUTORIZAÇÃO PARA DEBITO'
update dbo.Documento set DocumentoTipoId = null where Nome = 'TERMO LUZ EM DIA'
update dbo.Documento set DocumentoTipoId = 1 where Nome = 'CCB NEGOCIÁVEL'
update dbo.Documento set DocumentoTipoId = 2 where Nome = 'CCB NÃO NEGOCIÁVEL'
update dbo.Documento set DocumentoTipoId = 4 where Nome = 'FICHA CADASTRAL'
update dbo.Documento set DocumentoTipoId = 9 where Nome = 'TERMO DE AUTORIZAÇÃO DE COBRANÇA'
update dbo.Documento set DocumentoTipoId = 6 where Nome = 'TERMO DE AUTORIZAÇÃO DE COBRANÇA'
update dbo.Documento set DocumentoTipoId = null where Nome = 'BOLETO'

update dbo.Documento set DocumentoTipoId = (select top 1 id from dbo.DocumentoTipo where Tipo = 'boleto') where Nome = 'BOLETO'