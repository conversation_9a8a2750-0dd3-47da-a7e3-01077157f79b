

begin tran
    --DBCC CHECKIDENT('HierarquiaNivel', RESEED, 22)

    declare @hierarquianivelnovo table(id int)
    declare @niveladministrador table(id int,HierarquiaId int)
    declare @coringas table(usuarioId int)
    declare @hierarquiasNivelDetalhe table (id int)
--/*    
    insert into HierarquiaNivel
    select id,'Administração',null,0,0,1
    from Hierarquia where Nome in ('Hierarquia Comercial CFZ','Hierarquia Comercial CANAIS DIGITAIS','Hierarquia Comercial LOJISTA','Hierarquia Comercial CORBAN')

    insert @hierarquianivelnovo
    select HierarquiaId from HierarquiaNivel where nome = 'Administração'

    update a set 
        Ordem = ordem + 1,
        RelacaoMultipla = case when ordem = 0 then 0 else 1 end
    from HierarquiaNivel a
    join @hierarquianivelnovo b on b.id = a.HierarquiaId 
--*/
    insert into @niveladministrador
    select  id, HierarquiaId
    from HierarquiaNivel
    where Ordem = 1
    order by HierarquiaId,Ordem

    insert into HierarquiaNivelDetalhe(Nome,HierarquiaNivelId,HierarquiaNivelDetalheId,Ativo)
    select replace(a.nome,'Coordenação','Administração') nome,d.id,null,1
    from HierarquiaNivelDetalhe a with(nolock)
    join HierarquiaNivel b with(nolock) on b.Id = a.HierarquiaNivelId
    join @hierarquianivelnovo c on c.id = b.HierarquiaId
    join @niveladministrador d on d.HierarquiaId = c.id
    where HierarquiaNivelDetalheId is null

    update d
    set
        HierarquiaNivelDetalheId = e.id
    from @niveladministrador a
    join @hierarquianivelnovo b on b.id = a.HierarquiaId
    join HierarquiaNivel c on c.HierarquiaId = b.id and Ordem = 2 
    join HierarquiaNivelDetalhe d on d.HierarquiaNivelId = c.id
    join HierarquiaNivelDetalhe e on e.HierarquiaNivelId = a.id

    if DB_NAME() = 'db-crefaz-prod'
        insert into @coringas
        select id from seguranca.usuario where [login] in ('GILDOREZENDE','GUSTAVO','fernanda.merlo','luiz.ferreira','mercedes.garcia','suelen.fujii','rafael.ribeiro')

    if DB_NAME() = 'db-crefaz-stag'
        insert into @coringas
        select id from seguranca.usuario where [login] in ('edson.alves','pedro.cabral','GILDOREZENDE','GUSTAVO','fernanda.merlo','luiz.ferreira','mercedes.garcia','suelen.fujii','rafael.ribeiro')

    insert into @hierarquiasNivelDetalhe
    select id from HierarquiaNivelDetalhe where HierarquiaNivelDetalheId is null

    insert into UsuarioHierarquiaNivelDetalhe(UsuarioId,HierarquiaNivelDetalheId,DataInicio,DataFim)
    select a.usuarioId,b.id,'1900-01-01',null from @coringas a
    CROSS join @hierarquiasNivelDetalhe b


/*--mostrar a fernanda
    select * from HierarquiaNivel order by HierarquiaId,Ordem

    select a.* 
    from HierarquiaNivelDetalhe a with(nolock)
    join HierarquiaNivel b with(nolock) on b.Id = a.HierarquiaNivelId  
    order by b.HierarquiaId,b.Ordem

    select * from UsuarioHierarquiaNivelDetalhe
--*/

commit--ROLLBACK
