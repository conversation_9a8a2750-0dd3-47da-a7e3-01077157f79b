


DECLARE @erro varchar(1000)
BEGIN TRAN
    BEGIN TRY
    -- Digitando > Aguard. Cadastro
    update dbo.PropostaEtapa set Nome = 'Aguard. Cadastro' where Nome = 'Digitando'

    -- A<PERSON>and<PERSON> > Aguard. Análise
    update dbo.PropostaEtapa set Nome = 'Aguard. Análise' where Nome = 'Aguardando <PERSON>'    

    -- Em Análise > Em Análise
    update dbo.PropostaEtapa set Nome = 'Em Análise' where Nome = 'Em Análise' 

    -- Pendente Análise > Proposta Pendente
    update dbo.PropostaEtapa set Nome = 'Proposta Pendente' where Nome = 'Pendente Análise' 

    -- Em Contato > Ligando
    update dbo.PropostaEtapa set Nome = 'Ligando' where Nome = 'Em Contato' 

    -- Pendente Contato > Contato Pendente
    update dbo.PropostaEtapa set Nome = 'Contato Pendente' where Nome = 'Pendente Contato' 

    -- Aguardando Assinatura > Aguard. Assinatura
    update dbo.PropostaEtapa set Nome = 'Aguard. Assinatura' where Nome = 'Aguardando Assinatura' 

    -- Aguardando <PERSON> > Aguard. Validação
    update dbo.PropostaEtapa set Nome = 'Aguard. Validação' where Nome = 'Aguardando Validação' 

    -- Pendente Validação > Validação Pendente
    update dbo.PropostaEtapa set Nome = 'Validação Pendente' where Nome = 'Pendente Validação' 

    COMMIT
    END TRY
    BEGIN CATCH
        
         set @erro = ERROR_MESSAGE()
            rollback
            RAISERROR(@erro,16,1)
            return
    END CATCH



