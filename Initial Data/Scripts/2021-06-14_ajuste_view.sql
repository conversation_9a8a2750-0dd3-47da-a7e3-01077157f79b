alter view [dbo].[vwPropostaStatusHistoricoDetalhes] as
select  a.<PERSON>d, 
        a.<PERSON>, 
        a.<PERSON>, 
        Parametro.fncRetornaEnumValor('tipoModalidade',a.Tipo<PERSON>odalidade) TipoModalidadeDescricao, 
        a.<PERSON>,
        b.<PERSON>d <PERSON>aEtapaId, 
        b.<PERSON> DescricaoPropostaE<PERSON>pa, 
        b.<PERSON>ostaEtapa, 
        c.Id PropostaDecisaoId, 
        c.<PERSON><PERSON> Des<PERSON>oPropostaDecisao, 
        c.<PERSON><PERSON>Dec<PERSON>, 
        d.Id PropostaMotivoId, 
        d.Nome DescricaoPropostaMotivo,
        a.[Data], 
        a.<PERSON>,
        a.<PERSON>l_<PERSON>al<PERSON>_Consulta FlagFalhaConsultaCrivo,
        ROW_NUMBER() over (partition by a.PropostaId order by a.Id desc) as OrdemDesc
    from PropostaStatusHistorico a with(nolock)
    join PropostaEtapa b with(nolock) on a.PropostaEtapaId = b.Id
    left join PropostaDecisao c with(nolock) on a.PropostaDecisaoId = c.Id
    left join PropostaMotivo d with(nolock) on a.PropostaMotivoId = d.Id

