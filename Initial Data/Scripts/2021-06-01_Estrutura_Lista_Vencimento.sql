create table ListaVencimento (
    Id int identity(1,1) NOT NULL PRIMARY key, 
    ProdutoId int NOT NULL, 
    OcupacaoId int NULL,
    DiaVencimento int NULL
)
alter table ListaVencimento add constraint FK_ListaVencimento_Produto foreign key (ProdutoId) references Produto(Id);
alter table ListaVencimento add constraint FK_ListaVencimento_Ocupacao foreign key (OcupacaoId) references Ocupacao(Id);
alter table ListaVencimento add constraint DF_ListaVencimento_OcupacaoId default (null) for [OcupacaoId]

insert into ListaVencimento (ProdutoId, OcupacaoId, DiaVencimento) VALUES
(9,3, 3),(9,3, 5),(9,3, 7),(9,3, 10),(9,1, 5),(9,1, 10),(9,1, 15),(9,1, 20),
(9, null, 3),(9,null, 5),(9, null, 7),(9, null, 10),(9, null, 15),(9, null, 20),
(10,3, 3),(10,3, 5),(10,3, 7),(10,3, 10),(10,1, 5),(10,1, 10),(10,1, 15),(10,1, 20),
(10, null, 3),(10,null, 5),(10, null, 7),(10, null, 10),(10, null, 15),(10, null, 20)