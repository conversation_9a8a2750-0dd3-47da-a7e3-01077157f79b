DECLARE @parametroGrupoId INT, @parametroId INT

SELECT @parametroGrupoId = Id from Parametro.ParametroGrupo where Nome = 'Crivo'

----
INSERT into Parametro.Parametro
VALUES(@ParametroGrupoId, 1, 'V_Consumo', '<PERSON><PERSON><PERSON> da Fatura', 'Lista de consumos (Fatura) separada por ";".', 1, '"Driver"."Minhas Variaveis"."---- Política - P2 - Energia ----"."V_Consumo"')
SET @parametroId = @@IDENTITY

insert into Parametro.ParametroHistorico
select id, dbo.getdateBR(), Valor from Parametro.ParametroHistorico where Id = @parametroId

alter table PropostaDebito drop column codigo
alter table PropostaDebito alter column Consumo numeric(7,2) null
alter table PropostaDebito alter column DataVencimento date
alter table PropostaDebito alter column Participa bit null

