update api.[route] set input = 
'{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "convenioId": 1,
            "tabelaJurosId": 2,
            "diaRecebimento": -1,
            "valorSolicitado": 500,
            "adicionais": [
                {
                    "convenioDadosId": 12,
                    "convenioId": 1,
                    "nome": "N° da Instalação",
                    "tipo": 2,
                    "ordem": 1,
                    "formato": "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d]",
                    "mensagem": "N° da instalação inválido. Informe o N° da instalação com nove dígitos.",
                    "valor": "432342342"
                },
                {
                    "convenioDadosId": 23,
                    "convenioId": 1,
                    "nome": "Data de Leitura",
                    "tipo": 4,
                    "ordem": 2,
                    "mensagem": "Data inválida.",
                    "valor": "2021-06-03T13:23:22.240Z"
                }
            ],
            "id": 872,
            "produtoId": 6,
            "plano": 12,
            "prestacao": 135.17,
            "renda": 1220,
            "tipoRenda": 1,
            "vencimento": "2021-07-17",
            "valor": 500
        }
    ],
    "required": [
        "convenioId",
        "tabelaJurosId",
        "diaRecebimento",
        "valorSolicitado",
        "adicionais",
        "id",
        "produtoId",
        "plano",
        "prestacao",
        "renda",
        "tipoRenda",
        "vencimento",
        "valor"
    ],
    "properties": {
        "convenioId": {
            "$id": "#/properties/convenioId",
            "type": "integer",
            "title": "The convenioId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "tabelaJurosId": {
            "$id": "#/properties/tabelaJurosId",
            "type": "integer",
            "title": "The tabelaJurosId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                2
            ]
        },
        "diaRecebimento": {
            "$id": "#/properties/diaRecebimento",
            "type": "integer",
            "title": "The diaRecebimento schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                -1
            ]
        },
        "valorSolicitado": {
            "$id": "#/properties/valorSolicitado",
            "type": "integer",
            "title": "The valorSolicitado schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                500
            ]
        },
        "adicionais": {
            "$id": "#/properties/adicionais",
            "type": "array",
            "title": "The adicionais schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [
                    {
                        "convenioDadosId": 12,
                        "convenioId": 1,
                        "nome": "N° da Instalação",
                        "tipo": 2,
                        "ordem": 1,
                        "formato": "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d]",
                        "mensagem": "N° da instalação inválido. Informe o N° da instalação com nove dígitos.",
                        "valor": "432342342"
                    },
                    {
                        "convenioDadosId": 23,
                        "convenioId": 1,
                        "nome": "Data de Leitura",
                        "tipo": 4,
                        "ordem": 2,
                        "mensagem": "Data inválida.",
                        "valor": "2021-06-03T13:23:22.240Z"
                    }
                ]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/adicionais/items",
                "anyOf": [
                    {
                        "$id": "#/properties/adicionais/items/anyOf/0",
                        "type": "object",
                        "title": "The first anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": {},
                        "examples": [
                            {
                                "convenioDadosId": 12,
                                "convenioId": 1,
                                "nome": "N° da Instalação",
                                "tipo": 2,
                                "ordem": 1,
                                "formato": "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d]",
                                "mensagem": "N° da instalação inválido. Informe o N° da instalação com nove dígitos.",
                                "valor": "432342342"
                            }
                        ],
                        "required": [
                            "convenioDadosId",
                            "convenioId",
                            "nome",
                            "tipo",
                            "ordem",
                            "formato",
                            "mensagem",
                            "valor"
                        ],
                        "properties": {
                            "convenioDadosId": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/convenioDadosId",
                                "type": "integer",
                                "title": "The convenioDadosId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    12
                                ]
                            },
                            "convenioId": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/convenioId",
                                "type": "integer",
                                "title": "The convenioId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1
                                ]
                            },
                            "nome": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/nome",
                                "type": "string",
                                "title": "The nome schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "N° da Instalação"
                                ]
                            },
                            "tipo": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/tipo",
                                "type": "integer",
                                "title": "The tipo schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    2
                                ]
                            },
                            "ordem": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/ordem",
                                "type": "integer",
                                "title": "The ordem schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1
                                ]
                            },
                            "formato": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/formato",
                                "type": "string",
                                "title": "The formato schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d],[\\d]"
                                ]
                            },
                            "mensagem": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/mensagem",
                                "type": "string",
                                "title": "The mensagem schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "N° da instalação inválido. Informe o N° da instalação com nove dígitos."
                                ]
                            },
                            "valor": {
                                "$id": "#/properties/adicionais/items/anyOf/0/properties/valor",
                                "type": "string",
                                "title": "The valor schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "432342342"
                                ]
                            }
                        },
                        "additionalProperties": true
                    },
                    {
                        "$id": "#/properties/adicionais/items/anyOf/1",
                        "type": "object",
                        "title": "The second anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": {},
                        "examples": [
                            {
                                "convenioDadosId": 23,
                                "convenioId": 1,
                                "nome": "Data de Leitura",
                                "tipo": 4,
                                "ordem": 2,
                                "mensagem": "Data inválida.",
                                "valor": "2021-06-03T13:23:22.240Z"
                            }
                        ],
                        "required": [
                            "convenioDadosId",
                            "convenioId",
                            "nome",
                            "tipo",
                            "ordem",
                            "mensagem",
                            "valor"
                        ],
                        "properties": {
                            "convenioDadosId": {
                                "$id": "#/properties/adicionais/items/anyOf/1/properties/convenioDadosId",
                                "type": "integer",
                                "title": "The convenioDadosId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    23
                                ]
                            },
                            "convenioId": {
                                "$id": "#/properties/adicionais/items/anyOf/1/properties/convenioId",
                                "type": "integer",
                                "title": "The convenioId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    1
                                ]
                            },
                            "nome": {
                                "$id": "#/properties/adicionais/items/anyOf/1/properties/nome",
                                "type": "string",
                                "title": "The nome schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "Data de Leitura"
                                ]
                            },
                            "tipo": {
                                "$id": "#/properties/adicionais/items/anyOf/1/properties/tipo",
                                "type": "integer",
                                "title": "The tipo schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    4
                                ]
                            },
                            "ordem": {
                                "$id": "#/properties/adicionais/items/anyOf/1/properties/ordem",
                                "type": "integer",
                                "title": "The ordem schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    2
                                ]
                            },
                            "mensagem": {
                                "$id": "#/properties/adicionais/items/anyOf/1/properties/mensagem",
                                "type": "string",
                                "title": "The mensagem schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "Data inválida."
                                ]
                            },
                            "valor": {
                                "$id": "#/properties/adicionais/items/anyOf/1/properties/valor",
                                "type": "string",
                                "title": "The valor schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "2021-06-03T13:23:22.240Z"
                                ]
                            }
                        },
                        "additionalProperties": true
                    }
                ]
            }
        },
        "id": {
            "$id": "#/properties/id",
            "type": "integer",
            "title": "The id schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                872
            ]
        },
        "produtoId": {
            "$id": "#/properties/produtoId",
            "type": "integer",
            "title": "The produtoId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                6
            ]
        },
        "plano": {
            "$id": "#/properties/plano",
            "type": "integer",
            "title": "The plano schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                12
            ]
        },
        "prestacao": {
            "$id": "#/properties/prestacao",
            "type": "number",
            "title": "The prestacao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0.0,
            "examples": [
                135.17
            ]
        },
        "renda": {
            "$id": "#/properties/renda",
            "type": "integer",
            "title": "The renda schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1220
            ]
        },
        "tipoRenda": {
            "$id": "#/properties/tipoRenda",
            "type": "integer",
            "title": "The tipoRenda schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "vencimento": {
            "$id": "#/properties/vencimento",
            "type": "string",
            "title": "The vencimento schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-07-17"
            ]
        },
        "valor": {
            "$id": "#/properties/valor",
            "type": "integer",
            "title": "The valor schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                500
            ]
        }
    },
    "additionalProperties": true
}'
where route = '/api/proposta/simulacao-crivo'