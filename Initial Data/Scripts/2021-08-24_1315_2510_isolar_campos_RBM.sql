create table PropostaRBM (
    Id int identity(1,1) not null, 
    PropostaId int not null, 
    PropostaRBM int, 
    OperacaoRBM int, 
    Exportacao datetime, 
    Documento datetime, 
    Formalizacao datetime
)

alter table PropostaRBM add constraint PK_PropostaRBM primary key (Id);
alter table PropostaRBM add constraint FK_PropostaRBM_Proposta foreign key (PropostaId) references Proposta(Id);

alter table Proposta drop column ExportadoRBM;
alter table Proposta drop column CodigoRBM;
alter table Proposta drop column ExportadoRBMDocumento;
alter table Proposta drop column FormalizadoRBM;