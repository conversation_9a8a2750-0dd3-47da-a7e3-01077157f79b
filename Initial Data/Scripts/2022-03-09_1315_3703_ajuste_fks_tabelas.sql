alter TABLE dbo.CamposEditaveis add CONSTRAINT FK_CamposEditaveis_PropostaEtapa FOREIGN KEY (EtapaId) REFERENCES dbo.PropostaEtapa(Id)
alter TABLE dbo.CamposEditaveis alter COLUMN MotivoId SMALLINT
alter TABLE dbo.CamposEditaveis add CONSTRAINT FK_CamposEditaveis_PropostaMotivo FOREIGN KEY (MotivoId) REFERENCES dbo.PropostaMotivo(Id)
alter TABLE dbo.Cep add CONSTRAINT FK_Cep_Cidade FOREIGN KEY (CidadeId) REFERENCES dbo.Cidade(Id)
alter TABLE dbo.CorrespondenteTabelaJuros add CONSTRAINT FK_CorrespondenteTabelaJuros_TabelaJuros FOREIGN KEY (TabelaJurosId) REFERENCES dbo.TabelaJuros(Id)
alter TABLE dbo.CorrespondenteTabelaJuros add CONSTRAINT FK_CorrespondenteTabelaJuros_Correspondente FOREIGN KEY (CorrespondenteId) REFERENCES dbo.Correspondente(Id)
alter TABLE dbo.CredenciamentoImagem add CONSTRAINT FK_CredenciamentoImagem_Credenciamento FOREIGN KEY (CredenciamentoId) REFERENCES dbo.Credenciamento(Id)
alter TABLE dbo.ProdutoDado add CONSTRAINT FK_ProdutoDado_Produto FOREIGN KEY (ProdutoId) REFERENCES dbo.Produto(Id)
alter TABLE dbo.PropostaBoleto add CONSTRAINT FK_PropostaBoleto_Proposta FOREIGN KEY (PropostaId) REFERENCES Proposta(Id)
alter TABLE dbo.PropostaContratoRefin add CONSTRAINT FK_PropostaContratoRefin_Proposta FOREIGN KEY (PropostaId) REFERENCES dbo.Proposta(Id)
alter TABLE dbo.PropostaDocumentoEnviadoFTP ADD CONSTRAINT FK_PropostaDocumentoEnviadoFTP_Proposta FOREIGN KEY (PropostaId) REFERENCES dbo.Proposta(Id) 
alter TABLE Notificacao.Mensagem add CONSTRAINT FK_Mensagem_Pessoa FOREIGN KEY (PessoaId) REFERENCES dbo.Pessoa(Id)
