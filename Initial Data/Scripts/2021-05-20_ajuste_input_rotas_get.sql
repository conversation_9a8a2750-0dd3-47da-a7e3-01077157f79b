
update api.route
set [input] = '{}'
where method = 'GET'
and [route] like '%/api/bloqueado%'

update api.route
set [input] = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "telefone": "27999999999",
            "tipo": "celular",
            "motivoId": 1,
            "descricao": "descrição opcional do bloqueio"
        }
    ],
    "required": [
        "telefone",
        "tipo",
        "motivoId"
    ],
    "properties": {
        "telefone": {
            "$id": "#/properties/telefone",
            "type": "string",
            "title": "The telefone schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "27999999999"
            ]
        },
        "tipo": {
            "$id": "#/properties/tipo",
            "type": "string",
            "title": "The tipo schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "celular"
            ]
        },
        "motivoId": {
            "$id": "#/properties/motivoId",
            "type": "integer",
            "title": "The motivoId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "descricao": {
            "$id": "#/properties/descricao",
            "type": "string",
            "title": "The descricao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "descrição opcional do bloqueio"
            ]
        }
    },
    "additionalProperties": true
}'
where method = 'POST'
and route = '/api/bloqueado/telefone'
