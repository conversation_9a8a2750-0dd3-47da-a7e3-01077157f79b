

declare @grupomenuId int,
        @grupofiltroId int,
        @grupobotaoId int,
        @grupoabaId int,
        @grupoframeId int,
        @grupotelaId int,
        @grupotabelaId int

if not exists (select * from Seguranca.objetogrupo where nome = 'menu')
BEGIN
    insert into Seguranca.objetogrupo 
    values('menu',0,0)

    set @grupomenuId = @@IDENTITY
END
ELSE
    select @grupomenuId = id from Seguranca.objetogrupo where nome = 'menu'

if not exists (select * from Seguranca.objetogrupo where nome = 'filtro')
BEGIN
    insert into Seguranca.objetogrupo 
    values('filtro',0,0)

    set @grupofiltroId = @@IDENTITY
END
ELSE
    select @grupofiltroId = id from Seguranca.objetogrupo where nome = 'filtro'

if not exists (select * from Seguranca.objetogrupo where nome = 'botao')
BEGIN
    insert into Seguranca.objetogrupo 
    values('botao',0,0)

    set @grupobotaoId = @@IDENTITY
END
ELSE
    select @grupobotaoId = id from Seguranca.objetogrupo where nome = 'botao'

if not exists (select * from Seguranca.objetogrupo where nome = 'aba')
BEGIN
    insert into Seguranca.objetogrupo 
    values('aba',0,0)

    set @grupoabaId = @@IDENTITY
END
ELSE
    select @grupoabaId = id from Seguranca.objetogrupo where nome = 'aba'

if not exists (select * from Seguranca.objetogrupo where nome = 'frame')
BEGIN
    insert into Seguranca.objetogrupo 
    values('frame',0,0)

    set @grupoframeId = @@IDENTITY
END
ELSE
    select @grupoframeId = id from Seguranca.objetogrupo where nome = 'frame'

if not exists (select * from Seguranca.objetogrupo where nome = 'tela')
BEGIN
    insert into Seguranca.objetogrupo 
    values('tela',0,0)
    set @grupotelaId = @@IDENTITY
END
ELSE
    select @grupotelaId = id from Seguranca.objetogrupo where nome = 'tela'

if not exists (select * from Seguranca.objetogrupo where nome = 'tabela')
BEGIN
    insert into Seguranca.objetogrupo 
    values('tabela',0,0)
    set @grupotabelaId = @@IDENTITY
END
ELSE
    select @grupotabelaId = id from Seguranca.objetogrupo where nome = 'tabela'

insert into seguranca.objeto(nome,tag,ObjetoGrupoId,Ordem) values
('Parâmetro','menu.configuracoes.parametro',@grupomenuId,0),
('Auditoria','menu.configuracoes.seguranca.auditoria',@grupomenuId,0),
('Relatório de Comissão','menu.comercial.comissao.relatorioComissao',@grupomenuId,0),
('Relatório de Comissão','tela.comercial.comissao.relatorioComissao',@grupotelaId,0),
('Comissão Paga','filtro.relatorioComissao.paga',@grupofiltroId,0),
('Comissão não Paga','filtro.relatorioComissao.naoPaga',@grupofiltroId,0),
('Anexar PDF','botao.relatorioComissao.anexarPDF',@grupobotaoId,0),
('Notas Fiscais','filtro.relatorioComissao.notasFiscais',@grupofiltroId,0),
('Notas Fiscais','menu.comercial.comissao.notasFiscais',@grupomenuId,0),
('Notas Fiscais','tela.comercial.comissao.notasFiscais',@grupotelaId,0),
('Anexar Notas','botao.notasFiscais.anexarNota',@grupobotaoId,0),
('Verificado','botao.notasFiscais.tabelaNotasFiscais.verificado',@grupobotaoId,0),
('Download NF','botao.notasFiscais.tabelaNotasFiscais.downloadNF',@grupobotaoId,0),
('Cadastro','menu.credito.pessoa.cadastro',@grupomenuId,0),
('Cadastro de Pessoa','tela.credito.pessoa.cadastro',@grupotelaId,0),
('Blocklist','botao.cadastroPessoa.blocklist',@grupobotaoId,0),
('Editar','botao.cadastroPessoa.edicao',@grupobotaoId,0),
('Cliente Selecionado','tela.credito.pessoa.cadastro.clienteselecionado',@grupotelaId,0),
('Blocklist','botao.cadastroPessoa.cliente.blocklist',@grupobotaoId,0),
('Interno','botao.cadastroPessoa.cliente.anotacoesInternas',@grupobotaoId,0),
('Dados Pessoais','aba.cadastroPessoa.cliente.pessoais',@grupoabaId,0),
('Endereço','aba.cadastroPessoa.cliente.endereco',@grupoabaId,0),
('Contato','aba.cadastroPessoa.cliente.contato',@grupoabaId,0),
('Dados Profissionais','aba.cadastroPessoa.cliente.profissionais',@grupoabaId,0),
('Referências Pessoais','aba.cadastroPessoa.cliente.referenciasPessoais',@grupoabaId,0),
('Histórico de Contratos realizados','tabela.cadastroPessoa.cliente.historicoContratosRealizados',@grupotabelaId,0),
('Histórico de Propostas','tabela.cadastroPessoa.cliente.historicoPropostas',@grupotabelaId,0),
('Histórico de Alteração dos dados da Pessoa','tabela.cadastroPessoa.cliente.historicoAlteracoesDados',@grupotabelaId,0),
('Simulação','menu.credito.proposta.simulacao',@grupomenuId,0),
('Simulação','tela.credito.proposta.simulacao',@grupotelaId,0),
('Simular','botao.simulacao.simular',@grupobotaoId,0),
('Pré-Análise','botao.simulacao.preanalise',@grupobotaoId,0),
('Nova Proposta','menu.credito.proposta.novaProposta',@grupomenuId,0),
('Nova Proposta','tela.credito.proposta.novaProposta',@grupotelaId,0),
('Pré-Análise','frame.credito.proposta.preanalise',@grupoframeId,0),
('Proposta Crédito','tela.propostacredito',@grupotelaId,0),
('Aprovar','botao.propostaCredito.aprovarProposta',@grupobotaoId,0),
('Cancelar','botao.propostaCredito.cancelarProposta',@grupobotaoId,0),
('Negar Proposta','botao.propostacredito.negarproposta',@grupobotaoId,0),
('Enviar','botao.propostaCredito.centralComunicacao',@grupobotaoId,0),
('Análise','menu.credito.proposta.analise',@grupomenuId,0),
('Análise','tela.credito.proposta.analise',@grupotelaId,0),
('Mesa de Crédito','tela.credito.proposta.mesacredito',@grupotelaId,0),
('Etapa','botao.analiseMesaCredito.alterarEtapa',@grupobotaoId,0),
('Decisão','botao.analiseMesaCredito.salvarParecer',@grupobotaoId,0),
('Blocklist','botao.analiseMesaCredito.blocklist',@grupobotaoId,0),
('Externo','botao.analiseMesaCredito.externo',@grupobotaoId,0),
('Interno','botao.analiseMesaCredito.interno',@grupobotaoId,0),
('Status Operatividade','botao.analiseMesaCredito.statusOperatividade',@grupobotaoId,0),
('Histórico','botao.analiseMesaCredito.historico',@grupobotaoId,0),
('Ver Mais','botao.analiseMesaCredito.verMais',@grupobotaoId,0),
('Histórico de Propostas','tabela.analiseMesaCredito.historicoPropostas',@grupotabelaId,0),
('Histórico de Alteração de dados das pessoas','tabela.analiseMesaCredito.historicoAlteracaoDados',@grupotabelaId,0),
('Acompanhamento','menu.credito.proposta.acompanhamento',@grupomenuId,0),
('Acompanhamento','tela.credito.proposta.acompanhamento',@grupotelaId,0),
('Simulação Proposta','botao.acompanhamento.simulacaoProposta',@grupobotaoId,0),
('Cadastrar Proposta','botao.acompanhamento.cadastrarProposta',@grupobotaoId,0),
('Imprimir Contrato','botao.acompanhamento.imprimirContrato',@grupobotaoId,0),
('Modalidade','botao.acompanhamento.selecaoModalidade',@grupobotaoId,0),
('Imprimir Proposta','botao.acompanhamento.impressaoDocumentos',@grupobotaoId,0),
('Situação','botao.acompanhamento.situacaoProposta',@grupobotaoId,0),
('Reenviar','botao.acompanhamento.reenviarProposta',@grupobotaoId,0),
('Redistribuição de Proposta','menu.credito.proposta.redistribuicao',@grupomenuId,0),
('Redistribuição de Proposta','tela.credito.proposta.redistribuicao',@grupotelaId,0),
('Impressão de Contratos','menu.credito.proposta.impressaoContrato',@grupomenuId,0),
('Impressão de Contratos','tela.credito.proposta.impressaoContrato',@grupotelaId,0),
('Follow Up','menu.credito.relatorio.followUp',@grupomenuId,0),
('Follow Up','tela.credito.relatorio.followUp',@grupotelaId,0),
('Visão Analítica','menu.credito.relatorio.visaoAnalitica',@grupomenuId,0),
('Visão Analítica','tela.credito.relatorio.visaoAnalitica',@grupotelaId,0),
('Esteira de Crédito','menu.credito.relatorio.esteiraCredito',@grupomenuId,0),
('Esteira de Crédito','tela.credito.relatorio.esteiraCredito',@grupotelaId,0),
('Reanalise','botao.esteiraCredito.reanalise',@grupobotaoId,0),
('Blocklist CPF','menu.backoffice.blocklist.cpf',@grupomenuId,0),
('Blocklist CPF','tela.backoffice.blocklist.cpf',@grupotelaId,0),
('Blocklist CPF','frame.blocklistCpf',@grupoframeId,0),
('Salvar','botao.blocklistCpf.edicao',@grupobotaoId,0),
('Salvar','frame.blocklistCpf.edicao',@grupoframeId,0),
('Incluir','botao.blocklistCpf.inclusao',@grupobotaoId,0),
('Blocklist Telefone','menu.backoffice.blocklist.telefone',@grupomenuId,0),
('Blocklist Telefone','tela.backoffice.blocklist.telefone',@grupotelaId,0),
('Blocklist Telefone','frame.blocklistTelefone',@grupoframeId,0),
('Editar','botao.blocklistTelefone.edicao',@grupobotaoId,0),
('Editar','frame.blocklistTelefone.edicao',@grupoframeId,0),
('Inclusão','botao.blocklistTelefone.inclusao',@grupobotaoId,0),
('Blocklist Unidade Consumidora','menu.backoffice.blocklist.unidadeConsumidora',@grupomenuId,0),
('Blocklist Unidade Consumidora','tela.backoffice.blocklist.unidadeConsumidora',@grupotelaId,0),
('Unidade Consumidora','frame.blocklistUnidadeConsumidora',@grupoframeId,0),
('Editar','botao.blocklistUnidadeConsumidora.edicao',@grupobotaoId,0),
('Editar','frame.blocklistUnidadeConsumidora.edicao',@grupoframeId,0),
('Inclusão','botao.blocklistUnidadeConsumidora.inclusao',@grupobotaoId,0),
('Histórico de Chamadas','menu.backoffice.telefonia.historico',@grupomenuId,0),
('Histórico de Chamadas','tela.backoffice.telefonia.historico',@grupotelaId,0)