update a
    set valor = '[{"dataPrimeiroContrato": true},{"dataUltimoContrato": true},{"bloqueioCpf": true},{"fonePessoal": true},{"parcelasPagas90Dias": true},{"parcelasPagas180Dias": true},{"parcelasPagas270Dias": true},{"parcelasPagas360Dias": true},{"parcelasPagas720Dias": true},{"parcelasPagas1080Dias": true},{"parcelasPagasVidaInteira": true},{"scoreNegadosUltimos15Dias": true},{"quantidadeDeContratosTomadosPorProduto": true},{"quantidadeDeContratosEmCursoPorProduto": true},{"quantidadeDeDiasAtrasoPorProduto": true},{"quantidadeDiasDesdaUltimaAlteracaoRenda": true},{"quantidadeDiasDeClientela": true},{"porcentualPagamentoPrimeiroContrato": true},{"porcentualPagamentoContratoEmCurso": true},{"maiorValorContratado": true},{"quantidadePropostasFinaizadas": true},{"ultimoAtrasoDiaProduto": true},{"estaDeAcordoComCobrancaEmCurso": true},{"quantidadeDiasAtrasoDeAcordoCobranca": true},{"comprometimentoRenda": true}]'
from parametro.parametro a
where codigo = 'RBM_VARIAVEIS_COMPORTAMENTAIS'

insert into Parametro.ParametroHistorico 
select Id, dbo.getdateBR(), Valor from Parametro.Parametro where codigo = 'RBM_VARIAVEIS_COMPORTAMENTAIS'
