declare @valor varchar(max)
declare @tipo tinyint -- 1 string, 2 inteiro, 3 numero e 4 data
declare @ParametroGrupo int


set @valor = (select Id from PropostaEtapa where Nome = 'Aguard. Averbação')
set @tipo  = 2
set @ParametroGrupo = 1 -- Crédito -- estático 
insert into Parametro.Parametro values (@ParametroGrupo,@tipo,'CODIGO_ETAPA_REVISAO','Etapa de Revisão','Código da etapa de revisão de proposta solicitado pelo parceiro ',1,@valor)
insert into Parametro.ParametroHistorico values (@@IDENTITY, dbo.getdateBR(),@valor)

