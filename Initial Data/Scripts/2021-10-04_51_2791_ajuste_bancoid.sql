update api.route
set [input] = '{
	"$schema": "http://json-schema.org/draft-07/schema",
	"$id": "http://example.com/example.json",
	"type": "object",
	"title": "The root schema",
	"description": "The root schema comprises the entire JSON document.",
	"default": {},
	"examples": [
		{
			"id": 6,
			"exibeDebitos": null,
			"pessoaId": null,
			"situacaoId": 3,
			"situacaoDescricao": "Aguard. Cadastro",
			"permiteCancelamento": 1,
			"situacaoCor": "#B95900",
			"motivo": null,
			"elegivelLuzEmDia": 0,
			"resultadoCrivo": {
				"flag": null,
				"qtMaxDebitosConcessionaria": null,
				"limite": null,
				"percentualMaxLimite": null
			},
			"decisaoAtual": false,
			"decisao": null,
			"permiteReanalise": 0,
			"operacao": {
				"produtoId": 1,
				"diaRecebimento": 1,
				"produtoNome": "Boleto",
				"convenioId": null,
				"convenioNome": null,
				"vencimento": "2021-08-04",
				"tabelaJurosId": 1,
				"tabelaJurosNome": "Tabela Boleto Padrão",
				"tabelaJurosValoresID": 12,
				"taxa": 17,
				"operacao": null,
				"valorContratado": 500,
				"prazo": 12,
				"iof": null,
				"prestacao": 108.43,
				"valorOriginal": null,
				"produtoOriginal": null,
				"limitePercentualDescontarFatura": 50,
				"limitePrestacao": 2076.59,
				"dadosAdicionais": []
			},
			"cliente": {
				"cpf": "01037551605",
				"nome": "JEAN LUCIO BENTO",
				"nascimento": "1978-01-26",
				"rg": "432523",
				"rgEmissor": "ssp",
				"rgUfId": 8,
				"rgUf": null,
				"rgEmissao": "2010-07-09T18:56:34.686Z",
				"sexo": 0,
				"estadoCivil": 0,
				"nacionalidadeId": 1,
				"nacionalidade": null,
				"naturalidadeUfId": 8,
				"naturalidadeUf": null,
				"naturalidadeCidadeId": 1905,
				"naturalidadeCidade": null,
				"grauInstrucaoId": 5,
				"grauInstrucao": null,
				"nomeMae": "Genilda da Silva",
				"nomeConjugue": null,
				"pep": false
			},
			"contatos": {
				"contato": {
					"email": null,
					"telefone": "27981097702",
					"telefoneExtra": null
				},
				"referencia": [
					{
						"nome": "Anderson Kil",
						"telefone": "27998787655",
						"grau": 0
					},
					{
						"nome": "Amber",
						"telefone": "27983884623",
						"grau": 0
					}
				]
			},
			"endereco": {
				"cep": "87050730",
				"logradouro": "Avenida Londrina",
				"numero": 234,
				"ufId": 16,
				"uf": "PR",
				"cidadeId": 1606,
				"cidade": "Maringá",
				"bairro": "Zona 08",
				"complemento": "casa"
			},
			"bancario": {
				"bancoId": "001",
				"banco": null,
				"agencia": 4232,
				"digito": "3",
				"numero": "6603-6",
				"conta": 1,
				"tipoConta": 0,
				"tipoOperacao": 12,
				"tempoConta": 3
			},
			"profissional": {
				"empresa": "Code",
				"ocupacaoId": 1,
				"ocupacao": "Assalariado",
				"profissaoId": 2,
				"profissao": null,
				"tempoEmpregoAtual": 1,
				"telefoneRH": "(27) 3339-9878",
				"pisPasep": 346775434,
				"renda": null,
				"outrasRendas": null,
				"tipoOutrasRendas": null
			},
			"anexo": [
				{
					"imagemId": 892,
					"documentoId": 8
				},
				{
					"imagemId": 893,
					"documentoId": 9
				},
				{
					"imagemId": 894,
					"documentoId": 10
				}
			],
			"unidade": {
				"nomeVendedor": null,
				"cpfVendedor": null,
				"celularVendedor": null,
				"loginAgente": "edson.alves",
				"nomeMatriz": "Matriz",
				"nomeSupervisor": "Jean Lucio Bento",
				"celularSupervisor": null
			},
			"debitosConveniada": [],
			"historicoChamada": []
		}
	],
	"required": [
		"id",
		"exibeDebitos",
		"pessoaId",
		"situacaoId",
		"situacaoDescricao",
		"permiteCancelamento",
		"situacaoCor",
		"motivo",
		"resultadoCrivo",
		"decisaoAtual",
		"decisao",
		"permiteReanalise",
		"operacao",
		"cliente",
		"contatos",
		"endereco",
		"bancario",
		"profissional",
		"anexo",
		"unidade"
	],
	"properties": {
		"id": {
			"$id": "#/properties/id",
			"type": "integer",
			"title": "The id schema",
			"description": "An explanation about the purpose of this instance.",
			"default": 0,
			"examples": [
				6
			]
		},
		"exibeDebitos": {
			"$id": "#/properties/exibeDebitos",
			"type": "null",
			"title": "The exibeDebitos schema",
			"description": "An explanation about the purpose of this instance.",
			"default": null,
			"examples": [
				null
			]
		},
		"pessoaId": {
			"$id": "#/properties/pessoaId",
			"type": "null",
			"title": "The pessoaId schema",
			"description": "An explanation about the purpose of this instance.",
			"default": null,
			"examples": [
				null
			]
		},
		"situacaoId": {
			"$id": "#/properties/situacaoId",
			"type": "integer",
			"title": "The situacaoId schema",
			"description": "An explanation about the purpose of this instance.",
			"default": 0,
			"examples": [
				3
			]
		},
		"situacaoDescricao": {
			"$id": "#/properties/situacaoDescricao",
			"type": "string",
			"title": "The situacaoDescricao schema",
			"description": "An explanation about the purpose of this instance.",
			"default": "",
			"examples": [
				"Aguard. Cadastro"
			]
		},
		"permiteCancelamento": {
			"$id": "#/properties/permiteCancelamento",
			"type": "integer",
			"title": "The permiteCancelamento schema",
			"description": "An explanation about the purpose of this instance.",
			"default": 0,
			"examples": [
				1
			]
		},
		"situacaoCor": {
			"$id": "#/properties/situacaoCor",
			"type": "string",
			"title": "The situacaoCor schema",
			"description": "An explanation about the purpose of this instance.",
			"default": "",
			"examples": [
				"#B95900"
			]
		},
		"motivo": {
			"$id": "#/properties/motivo",
			"type": "null",
			"title": "The motivo schema",
			"description": "An explanation about the purpose of this instance.",
			"default": null,
			"examples": [
				null
			]
		},
		"elegivelLuzEmDia": {
			"$id": "#/properties/elegivelLuzEmDia",
			"type": "integer",
			"title": "The elegivelLuzEmDia schema",
			"description": "An explanation about the purpose of this instance.",
			"default": 0,
			"examples": [
				0
			]
		},
		"resultadoCrivo": {
			"$id": "#/properties/resultadoCrivo",
			"type": "object",
			"title": "The resultadoCrivo schema",
			"description": "An explanation about the purpose of this instance.",
			"default": {},
			"examples": [
				{
					"flag": null,
					"qtMaxDebitosConcessionaria": null,
					"limite": null,
					"percentualMaxLimite": null
				}
			],
			"required": [
				"flag",
				"qtMaxDebitosConcessionaria",
				"limite",
				"percentualMaxLimite"
			],
			"properties": {
				"flag": {
					"$id": "#/properties/resultadoCrivo/properties/flag",
					"type": "null",
					"title": "The flag schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"qtMaxDebitosConcessionaria": {
					"$id": "#/properties/resultadoCrivo/properties/qtMaxDebitosConcessionaria",
					"type": "null",
					"title": "The qtMaxDebitosConcessionaria schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"limite": {
					"$id": "#/properties/resultadoCrivo/properties/limite",
					"type": "null",
					"title": "The limite schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"percentualMaxLimite": {
					"$id": "#/properties/resultadoCrivo/properties/percentualMaxLimite",
					"type": "null",
					"title": "The percentualMaxLimite schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				}
			},
			"additionalProperties": true
		},
		"decisaoAtual": {
			"$id": "#/properties/decisaoAtual",
			"type": "boolean",
			"title": "The decisaoAtual schema",
			"description": "An explanation about the purpose of this instance.",
			"default": false,
			"examples": [
				false
			]
		},
		"decisao": {
			"$id": "#/properties/decisao",
			"type": "null",
			"title": "The decisao schema",
			"description": "An explanation about the purpose of this instance.",
			"default": null,
			"examples": [
				null
			]
		},
		"permiteReanalise": {
			"$id": "#/properties/permiteReanalise",
			"type": "integer",
			"title": "The permiteReanalise schema",
			"description": "An explanation about the purpose of this instance.",
			"default": 0,
			"examples": [
				0
			]
		},
		"operacao": {
			"$id": "#/properties/operacao",
			"type": "object",
			"title": "The operacao schema",
			"description": "An explanation about the purpose of this instance.",
			"default": {},
			"examples": [
				{
					"produtoId": 1,
					"diaRecebimento": 1,
					"produtoNome": "Boleto",
					"convenioId": null,
					"convenioNome": null,
					"vencimento": "2021-08-04",
					"tabelaJurosId": 1,
					"tabelaJurosNome": "Tabela Boleto Padrão",
					"tabelaJurosValoresID": 12,
					"taxa": 17,
					"operacao": null,
					"valorContratado": 500,
					"prazo": 12,
					"iof": null,
					"prestacao": 108.43,
					"valorOriginal": null,
					"produtoOriginal": null,
					"limitePercentualDescontarFatura": 50,
					"limitePrestacao": 2076.59,
					"dadosAdicionais": []
				}
			],
			"required": [
				"produtoId",
				"diaRecebimento",
				"produtoNome",
				"convenioId",
				"convenioNome",
				"vencimento",
				"tabelaJurosId",
				"tabelaJurosNome",
				"tabelaJurosValoresID",
				"taxa",
				"operacao",
				"valorContratado",
				"prazo",
				"iof",
				"prestacao",
				"valorOriginal",
				"produtoOriginal",
				"limitePercentualDescontarFatura",
				"limitePrestacao",
				"dadosAdicionais"
			],
			"properties": {
				"produtoId": {
					"$id": "#/properties/operacao/properties/produtoId",
					"type": "integer",
					"title": "The produtoId schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						1
					]
				},
				"diaRecebimento": {
					"$id": "#/properties/operacao/properties/diaRecebimento",
					"type": "integer",
					"title": "The diaRecebimento schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						1
					]
				},
				"produtoNome": {
					"$id": "#/properties/operacao/properties/produtoNome",
					"type": "string",
					"title": "The produtoNome schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"Boleto"
					]
				},
				"convenioId": {
					"$id": "#/properties/operacao/properties/convenioId",
					"type": "null",
					"title": "The convenioId schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"convenioNome": {
					"$id": "#/properties/operacao/properties/convenioNome",
					"type": "null",
					"title": "The convenioNome schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"vencimento": {
					"$id": "#/properties/operacao/properties/vencimento",
					"type": "string",
					"title": "The vencimento schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"2021-08-04"
					]
				},
				"tabelaJurosId": {
					"$id": "#/properties/operacao/properties/tabelaJurosId",
					"type": "integer",
					"title": "The tabelaJurosId schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						1
					]
				},
				"tabelaJurosNome": {
					"$id": "#/properties/operacao/properties/tabelaJurosNome",
					"type": "string",
					"title": "The tabelaJurosNome schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"Tabela Boleto Padrão"
					]
				},
				"tabelaJurosValoresID": {
					"$id": "#/properties/operacao/properties/tabelaJurosValoresID",
					"type": "integer",
					"title": "The tabelaJurosValoresID schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						12
					]
				},
				"taxa": {
					"$id": "#/properties/operacao/properties/taxa",
					"type": "integer",
					"title": "The taxa schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						17
					]
				},
				"operacao": {
					"$id": "#/properties/operacao/properties/operacao",
					"type": "null",
					"title": "The operacao schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"valorContratado": {
					"$id": "#/properties/operacao/properties/valorContratado",
					"type": "integer",
					"title": "The valorContratado schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						500
					]
				},
				"prazo": {
					"$id": "#/properties/operacao/properties/prazo",
					"type": "integer",
					"title": "The prazo schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						12
					]
				},
				"iof": {
					"$id": "#/properties/operacao/properties/iof",
					"type": "null",
					"title": "The iof schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"prestacao": {
					"$id": "#/properties/operacao/properties/prestacao",
					"type": "number",
					"title": "The prestacao schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						108.43
					]
				},
				"valorOriginal": {
					"$id": "#/properties/operacao/properties/valorOriginal",
					"type": "null",
					"title": "The valorOriginal schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"produtoOriginal": {
					"$id": "#/properties/operacao/properties/produtoOriginal",
					"type": "null",
					"title": "The produtoOriginal schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"limitePercentualDescontarFatura": {
					"$id": "#/properties/operacao/properties/limitePercentualDescontarFatura",
					"type": "integer",
					"title": "The limitePercentualDescontarFatura schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						50
					]
				},
				"limitePrestacao": {
					"$id": "#/properties/operacao/properties/limitePrestacao",
					"type": "number",
					"title": "The limitePrestacao schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						2076.59
					]
				},
				"dadosAdicionais": {
					"$id": "#/properties/operacao/properties/dadosAdicionais",
					"type": "array",
					"title": "The dadosAdicionais schema",
					"description": "An explanation about the purpose of this instance.",
					"default": [],
					"examples": [
						[]
					],
					"additionalItems": true,
					"items": {
						"$id": "#/properties/operacao/properties/dadosAdicionais/items"
					}
				}
			},
			"additionalProperties": true
		},
		"cliente": {
			"$id": "#/properties/cliente",
			"type": "object",
			"title": "The cliente schema",
			"description": "An explanation about the purpose of this instance.",
			"default": {},
			"examples": [
				{
					"cpf": "01037551605",
					"nome": "JEAN LUCIO BENTO",
					"nascimento": "1978-01-26",
					"rg": "432523",
					"rgEmissor": "ssp",
					"rgUfId": 8,
					"rgUf": null,
					"rgEmissao": "2010-07-09T18:56:34.686Z",
					"sexo": 0,
					"estadoCivil": 0,
					"nacionalidadeId": 1,
					"nacionalidade": null,
					"naturalidadeUfId": 8,
					"naturalidadeUf": null,
					"naturalidadeCidadeId": 1905,
					"naturalidadeCidade": null,
					"grauInstrucaoId": 5,
					"grauInstrucao": null,
					"nomeMae": "Genilda da Silva",
					"nomeConjugue": null,
					"pep": false
				}
			],
			"required": [
				"cpf",
				"nome",
				"nascimento",
				"rg",
				"rgEmissor",
				"rgUfId",
				"rgUf",
				"rgEmissao",
				"sexo",
				"estadoCivil",
				"nacionalidadeId",
				"nacionalidade",
				"naturalidadeUfId",
				"naturalidadeUf",
				"naturalidadeCidadeId",
				"naturalidadeCidade",
				"grauInstrucaoId",
				"grauInstrucao",
				"nomeMae",
				"nomeConjugue",
				"pep"
			],
			"properties": {
				"cpf": {
					"$id": "#/properties/cliente/properties/cpf",
					"type": "string",
					"title": "The cpf schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"01037551605"
					]
				},
				"nome": {
					"$id": "#/properties/cliente/properties/nome",
					"type": "string",
					"title": "The nome schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"JEAN LUCIO BENTO"
					]
				},
				"nascimento": {
					"$id": "#/properties/cliente/properties/nascimento",
					"type": "string",
					"title": "The nascimento schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"1978-01-26"
					]
				},
				"rg": {
					"$id": "#/properties/cliente/properties/rg",
					"type": "string",
					"title": "The rg schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"432523"
					]
				},
				"rgEmissor": {
					"$id": "#/properties/cliente/properties/rgEmissor",
					"type": "string",
					"title": "The rgEmissor schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"ssp"
					]
				},
				"rgUfId": {
					"$id": "#/properties/cliente/properties/rgUfId",
					"type": "integer",
					"title": "The rgUfId schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						8
					]
				},
				"rgUf": {
					"$id": "#/properties/cliente/properties/rgUf",
					"type": "null",
					"title": "The rgUf schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"rgEmissao": {
					"$id": "#/properties/cliente/properties/rgEmissao",
					"type": "string",
					"title": "The rgEmissao schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"2010-07-09T18:56:34.686Z"
					]
				},
				"sexo": {
					"$id": "#/properties/cliente/properties/sexo",
					"type": "integer",
					"title": "The sexo schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						0
					]
				},
				"estadoCivil": {
					"$id": "#/properties/cliente/properties/estadoCivil",
					"type": "integer",
					"title": "The estadoCivil schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						0
					]
				},
				"nacionalidadeId": {
					"$id": "#/properties/cliente/properties/nacionalidadeId",
					"type": "integer",
					"title": "The nacionalidadeId schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						1
					]
				},
				"nacionalidade": {
					"$id": "#/properties/cliente/properties/nacionalidade",
					"type": "null",
					"title": "The nacionalidade schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"naturalidadeUfId": {
					"$id": "#/properties/cliente/properties/naturalidadeUfId",
					"type": "integer",
					"title": "The naturalidadeUfId schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						8
					]
				},
				"naturalidadeUf": {
					"$id": "#/properties/cliente/properties/naturalidadeUf",
					"type": "null",
					"title": "The naturalidadeUf schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"naturalidadeCidadeId": {
					"$id": "#/properties/cliente/properties/naturalidadeCidadeId",
					"type": "integer",
					"title": "The naturalidadeCidadeId schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						1905
					]
				},
				"naturalidadeCidade": {
					"$id": "#/properties/cliente/properties/naturalidadeCidade",
					"type": "null",
					"title": "The naturalidadeCidade schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"grauInstrucaoId": {
					"$id": "#/properties/cliente/properties/grauInstrucaoId",
					"type": "integer",
					"title": "The grauInstrucaoId schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						5
					]
				},
				"grauInstrucao": {
					"$id": "#/properties/cliente/properties/grauInstrucao",
					"type": "null",
					"title": "The grauInstrucao schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"nomeMae": {
					"$id": "#/properties/cliente/properties/nomeMae",
					"type": "string",
					"title": "The nomeMae schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"Genilda da Silva"
					]
				},
				"nomeConjugue": {
					"$id": "#/properties/cliente/properties/nomeConjugue",
					"type": "null",
					"title": "The nomeConjugue schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"pep": {
					"$id": "#/properties/cliente/properties/pep",
					"type": "boolean",
					"title": "The pep schema",
					"description": "An explanation about the purpose of this instance.",
					"default": false,
					"examples": [
						false
					]
				}
			},
			"additionalProperties": true
		},
		"contatos": {
			"$id": "#/properties/contatos",
			"type": "object",
			"title": "The contatos schema",
			"description": "An explanation about the purpose of this instance.",
			"default": {},
			"examples": [
				{
					"contato": {
						"email": null,
						"telefone": "27981097702",
						"telefoneExtra": null
					},
					"referencia": [
						{
							"nome": "Anderson Kil",
							"telefone": "27998787655",
							"grau": 0
						},
						{
							"nome": "Amber",
							"telefone": "27983884623",
							"grau": 0
						}
					]
				}
			],
			"required": [
				"contato",
				"referencia"
			],
			"properties": {
				"contato": {
					"$id": "#/properties/contatos/properties/contato",
					"type": "object",
					"title": "The contato schema",
					"description": "An explanation about the purpose of this instance.",
					"default": {},
					"examples": [
						{
							"email": null,
							"telefone": "27981097702",
							"telefoneExtra": null
						}
					],
					"required": [
						"email",
						"telefone",
						"telefoneExtra"
					],
					"properties": {
						"email": {
							"$id": "#/properties/contatos/properties/contato/properties/email",
							"type": "null",
							"title": "The email schema",
							"description": "An explanation about the purpose of this instance.",
							"default": null,
							"examples": [
								null
							]
						},
						"telefone": {
							"$id": "#/properties/contatos/properties/contato/properties/telefone",
							"type": "string",
							"title": "The telefone schema",
							"description": "An explanation about the purpose of this instance.",
							"default": "",
							"examples": [
								"27981097702"
							]
						},
						"telefoneExtra": {
							"$id": "#/properties/contatos/properties/contato/properties/telefoneExtra",
							"type": "null",
							"title": "The telefoneExtra schema",
							"description": "An explanation about the purpose of this instance.",
							"default": null,
							"examples": [
								null
							]
						}
					},
					"additionalProperties": true
				},
				"referencia": {
					"$id": "#/properties/contatos/properties/referencia",
					"type": "array",
					"title": "The referencia schema",
					"description": "An explanation about the purpose of this instance.",
					"default": [],
					"examples": [
						[
							{
								"nome": "Anderson Kil",
								"telefone": "27998787655",
								"grau": 0
							},
							{
								"nome": "Amber",
								"telefone": "27983884623",
								"grau": 0
							}
						]
					],
					"additionalItems": true,
					"items": {
						"$id": "#/properties/contatos/properties/referencia/items",
						"anyOf": [
							{
								"$id": "#/properties/contatos/properties/referencia/items/anyOf/0",
								"type": "object",
								"title": "The first anyOf schema",
								"description": "An explanation about the purpose of this instance.",
								"default": {},
								"examples": [
									{
										"nome": "Anderson Kil",
										"telefone": "27998787655",
										"grau": 0
									}
								],
								"required": [
									"nome",
									"telefone",
									"grau"
								],
								"properties": {
									"nome": {
										"$id": "#/properties/contatos/properties/referencia/items/anyOf/0/properties/nome",
										"type": "string",
										"title": "The nome schema",
										"description": "An explanation about the purpose of this instance.",
										"default": "",
										"examples": [
											"Anderson Kil"
										]
									},
									"telefone": {
										"$id": "#/properties/contatos/properties/referencia/items/anyOf/0/properties/telefone",
										"type": "string",
										"title": "The telefone schema",
										"description": "An explanation about the purpose of this instance.",
										"default": "",
										"examples": [
											"27998787655"
										]
									},
									"grau": {
										"$id": "#/properties/contatos/properties/referencia/items/anyOf/0/properties/grau",
										"type": "integer",
										"title": "The grau schema",
										"description": "An explanation about the purpose of this instance.",
										"default": 0,
										"examples": [
											0
										]
									}
								},
								"additionalProperties": true
							}
						]
					}
				}
			},
			"additionalProperties": true
		},
		"endereco": {
			"$id": "#/properties/endereco",
			"type": "object",
			"title": "The endereco schema",
			"description": "An explanation about the purpose of this instance.",
			"default": {},
			"examples": [
				{
					"cep": "87050730",
					"logradouro": "Avenida Londrina",
					"numero": 234,
					"ufId": 16,
					"uf": "PR",
					"cidadeId": 1606,
					"cidade": "Maringá",
					"bairro": "Zona 08",
					"complemento": "casa"
				}
			],
			"required": [
				"cep",
				"logradouro",
				"numero",
				"ufId",
				"uf",
				"cidadeId",
				"cidade",
				"bairro",
				"complemento"
			],
			"properties": {
				"cep": {
					"$id": "#/properties/endereco/properties/cep",
					"type": "string",
					"title": "The cep schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"87050730"
					]
				},
				"logradouro": {
					"$id": "#/properties/endereco/properties/logradouro",
					"type": "string",
					"title": "The logradouro schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"Avenida Londrina"
					]
				},
				"numero": {
					"$id": "#/properties/endereco/properties/numero",
					"type": "integer",
					"title": "The numero schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						234
					]
				},
				"ufId": {
					"$id": "#/properties/endereco/properties/ufId",
					"type": "integer",
					"title": "The ufId schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						16
					]
				},
				"uf": {
					"$id": "#/properties/endereco/properties/uf",
					"type": "string",
					"title": "The uf schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"PR"
					]
				},
				"cidadeId": {
					"$id": "#/properties/endereco/properties/cidadeId",
					"type": "integer",
					"title": "The cidadeId schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						1606
					]
				},
				"cidade": {
					"$id": "#/properties/endereco/properties/cidade",
					"type": "string",
					"title": "The cidade schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"Maringá"
					]
				},
				"bairro": {
					"$id": "#/properties/endereco/properties/bairro",
					"type": "string",
					"title": "The bairro schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"Zona 08"
					]
				},
				"complemento": {
					"$id": "#/properties/endereco/properties/complemento",
					"type": "string",
					"title": "The complemento schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"casa"
					]
				}
			},
			"additionalProperties": true
		},
		"bancario": {
			"$id": "#/properties/bancario",
			"type": "object",
			"title": "The bancario schema",
			"description": "An explanation about the purpose of this instance.",
			"default": {},
			"examples": [
				{
					"bancoId": "001",
					"banco": null,
					"agencia": 4232,
					"digito": "3",
					"numero": "6603-6",
					"conta": 1,
					"tipoConta": 0,
					"tipoOperacao": 12,
					"tempoConta": 3
				}
			],
			"required": [
				"bancoId",
				"banco",
				"agencia",
				"digito",
				"numero",
				"conta",
				"tipoConta",
				"tipoOperacao",
				"tempoConta"
			],
			"properties": {
				"bancoId": {
					"$id": "#/properties/bancario/properties/bancoId",
					"type": "string",
					"title": "The bancoId schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"001"
					]
				},
				"banco": {
					"$id": "#/properties/bancario/properties/banco",
					"type": "null",
					"title": "The banco schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"agencia": {
					"$id": "#/properties/bancario/properties/agencia",
					"type": "integer",
					"title": "The agencia schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						4232
					]
				},
				"digito": {
					"$id": "#/properties/bancario/properties/digito",
					"type": "string",
					"title": "The digito schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"3"
					]
				},
				"numero": {
					"$id": "#/properties/bancario/properties/numero",
					"type": "string",
					"title": "The numero schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"6603-6"
					]
				},
				"conta": {
					"$id": "#/properties/bancario/properties/conta",
					"type": "integer",
					"title": "The conta schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						1
					]
				},
				"tipoConta": {
					"$id": "#/properties/bancario/properties/tipoConta",
					"type": "integer",
					"title": "The tipoConta schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						0
					]
				},
				"tipoOperacao": {
					"$id": "#/properties/bancario/properties/tipoOperacao",
					"type": "integer",
					"title": "The tipoOperacao schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						12
					]
				},
				"tempoConta": {
					"$id": "#/properties/bancario/properties/tempoConta",
					"type": "integer",
					"title": "The tempoConta schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						3
					]
				}
			},
			"additionalProperties": true
		},
		"profissional": {
			"$id": "#/properties/profissional",
			"type": "object",
			"title": "The profissional schema",
			"description": "An explanation about the purpose of this instance.",
			"default": {},
			"examples": [
				{
					"empresa": "Code",
					"ocupacaoId": 1,
					"ocupacao": "Assalariado",
					"profissaoId": 2,
					"profissao": null,
					"tempoEmpregoAtual": 1,
					"telefoneRH": "(27) 3339-9878",
					"pisPasep": 346775434,
					"renda": null,
					"outrasRendas": null,
					"tipoOutrasRendas": null
				}
			],
			"required": [
				"empresa",
				"ocupacaoId",
				"ocupacao",
				"profissaoId",
				"profissao",
				"tempoEmpregoAtual",
				"telefoneRH",
				"pisPasep",
				"renda",
				"outrasRendas",
				"tipoOutrasRendas"
			],
			"properties": {
				"empresa": {
					"$id": "#/properties/profissional/properties/empresa",
					"type": "string",
					"title": "The empresa schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"Code"
					]
				},
				"ocupacaoId": {
					"$id": "#/properties/profissional/properties/ocupacaoId",
					"type": "integer",
					"title": "The ocupacaoId schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						1
					]
				},
				"ocupacao": {
					"$id": "#/properties/profissional/properties/ocupacao",
					"type": "string",
					"title": "The ocupacao schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"Assalariado"
					]
				},
				"profissaoId": {
					"$id": "#/properties/profissional/properties/profissaoId",
					"type": "integer",
					"title": "The profissaoId schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						2
					]
				},
				"profissao": {
					"$id": "#/properties/profissional/properties/profissao",
					"type": "null",
					"title": "The profissao schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"tempoEmpregoAtual": {
					"$id": "#/properties/profissional/properties/tempoEmpregoAtual",
					"type": "integer",
					"title": "The tempoEmpregoAtual schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						1
					]
				},
				"telefoneRH": {
					"$id": "#/properties/profissional/properties/telefoneRH",
					"type": "string",
					"title": "The telefoneRH schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"(27) 3339-9878"
					]
				},
				"pisPasep": {
					"$id": "#/properties/profissional/properties/pisPasep",
					"type": "integer",
					"title": "The pisPasep schema",
					"description": "An explanation about the purpose of this instance.",
					"default": 0,
					"examples": [
						346775434
					]
				},
				"renda": {
					"$id": "#/properties/profissional/properties/renda",
					"type": "null",
					"title": "The renda schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"outrasRendas": {
					"$id": "#/properties/profissional/properties/outrasRendas",
					"type": "null",
					"title": "The outrasRendas schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"tipoOutrasRendas": {
					"$id": "#/properties/profissional/properties/tipoOutrasRendas",
					"type": "null",
					"title": "The tipoOutrasRendas schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				}
			},
			"additionalProperties": true
		},
		"anexo": {
			"$id": "#/properties/anexo",
			"type": "array",
			"title": "The anexo schema",
			"description": "An explanation about the purpose of this instance.",
			"default": [],
			"examples": [
				[
					{
						"imagemId": 892,
						"documentoId": 8
					},
					{
						"imagemId": 893,
						"documentoId": 9
					}
				]
			],
			"additionalItems": true,
			"items": {
				"$id": "#/properties/anexo/items",
				"anyOf": [
					{
						"$id": "#/properties/anexo/items/anyOf/0",
						"type": "object",
						"title": "The first anyOf schema",
						"description": "An explanation about the purpose of this instance.",
						"default": {},
						"examples": [
							{
								"imagemId": 892,
								"documentoId": 8
							}
						],
						"required": [
							"imagemId",
							"documentoId"
						],
						"properties": {
							"imagemId": {
								"$id": "#/properties/anexo/items/anyOf/0/properties/imagemId",
								"type": "integer",
								"title": "The imagemId schema",
								"description": "An explanation about the purpose of this instance.",
								"default": 0,
								"examples": [
									892
								]
							},
							"documentoId": {
								"$id": "#/properties/anexo/items/anyOf/0/properties/documentoId",
								"type": "integer",
								"title": "The documentoId schema",
								"description": "An explanation about the purpose of this instance.",
								"default": 0,
								"examples": [
									8
								]
							}
						},
						"additionalProperties": true
					}
				]
			}
		},
		"unidade": {
			"$id": "#/properties/unidade",
			"type": "object",
			"title": "The unidade schema",
			"description": "An explanation about the purpose of this instance.",
			"default": {},
			"examples": [
				{
					"nomeVendedor": null,
					"cpfVendedor": null,
					"celularVendedor": null,
					"loginAgente": "edson.alves",
					"nomeMatriz": "Matriz",
					"nomeSupervisor": "Jean Lucio Bento",
					"celularSupervisor": null
				}
			],
			"required": [
				"nomeVendedor",
				"cpfVendedor",
				"celularVendedor",
				"loginAgente",
				"nomeMatriz",
				"nomeSupervisor",
				"celularSupervisor"
			],
			"properties": {
				"nomeVendedor": {
					"$id": "#/properties/unidade/properties/nomeVendedor",
					"type": "null",
					"title": "The nomeVendedor schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"cpfVendedor": {
					"$id": "#/properties/unidade/properties/cpfVendedor",
					"type": "null",
					"title": "The cpfVendedor schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"celularVendedor": {
					"$id": "#/properties/unidade/properties/celularVendedor",
					"type": "null",
					"title": "The celularVendedor schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				},
				"loginAgente": {
					"$id": "#/properties/unidade/properties/loginAgente",
					"type": "string",
					"title": "The loginAgente schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"edson.alves"
					]
				},
				"nomeMatriz": {
					"$id": "#/properties/unidade/properties/nomeMatriz",
					"type": "string",
					"title": "The nomeMatriz schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"Matriz"
					]
				},
				"nomeSupervisor": {
					"$id": "#/properties/unidade/properties/nomeSupervisor",
					"type": "string",
					"title": "The nomeSupervisor schema",
					"description": "An explanation about the purpose of this instance.",
					"default": "",
					"examples": [
						"Jean Lucio Bento"
					]
				},
				"celularSupervisor": {
					"$id": "#/properties/unidade/properties/celularSupervisor",
					"type": "null",
					"title": "The celularSupervisor schema",
					"description": "An explanation about the purpose of this instance.",
					"default": null,
					"examples": [
						null
					]
				}
			},
			"additionalProperties": true
		},
		"debitosConveniada": {
			"$id": "#/properties/debitosConveniada",
			"type": "array",
			"title": "The debitosConveniada schema",
			"description": "An explanation about the purpose of this instance.",
			"default": [],
			"examples": [
				[]
			],
			"additionalItems": true,
			"items": {
				"$id": "#/properties/debitosConveniada/items"
			}
		},
		"historicoChamada": {
			"$id": "#/properties/historicoChamada",
			"type": "array",
			"title": "The historicoChamada schema",
			"description": "An explanation about the purpose of this instance.",
			"default": [],
			"examples": [
				[]
			],
			"additionalItems": true,
			"items": {
				"$id": "#/properties/historicoChamada/items"
			}
		}
	},
	"additionalProperties": true
}'
where method = 'PUT' and route = '/api/proposta/mesa-de-credito/{id}'


update api.route
set [input] = '{
      "$schema": "http://json-schema.org/draft-07/schema",
      "$id": "http://example.com/example.json",
      "type": "object",
      "title": "The root schema",
      "description": "The root schema comprises the entire JSON document.",
      "default": {},
      "examples": [
        {
          "id": 6,
          "exibeDebitos": null,
          "pessoaId": null,
          "situacaoId": 3,
          "permiteCancelamento": 1,
          "motivo": null,
          "resultadoCrivo": {
            "flag": null,
            "qtMaxDebitosConcessionaria": null,
            "limite": null,
            "percentualMaxLimite": null
          },
          "decisaoAtual": false,
          "decisao": null,
          "permiteReanalise": 0,
          "operacao": {
            "produtoId": 1,
            "diaRecebimento": 1,
            "convenioId": null,
            "vencimento": "2021-08-04",
            "tabelaJurosId": 1,
            "tabelaJurosNome": "Tabela Boleto Padrão",
            "tabelaJurosValoresID": 12,
            "taxa": 17,
            "operacao": null,
            "valorContratado": 500,
            "prazo": 12,
            "iof": null,
            "prestacao": 108.43,
            "valorOriginal": null,
            "produtoOriginal": null,
            "limitePercentualDescontarFatura": 50,
            "limitePrestacao": 2076.59,
            "dadosAdicionais": []
          },
          "cliente": {
            "cpf": "01037551605",
            "nome": "JEAN LUCIO BENTO",
            "nascimento": "1978-01-26",
            "rg": "432523",
            "rgEmissor": "ssp",
            "rgUfId": 8,
            "rgUf": null,
            "rgEmissao": "2010-07-09T18:56:34.686Z",
            "sexo": 0,
            "estadoCivil": 0,
            "nacionalidadeId": 1,
            "nacionalidade": null,
            "naturalidadeUfId": 8,
            "naturalidadeUf": null,
            "naturalidadeCidadeId": 1905,
            "naturalidadeCidade": null,
            "grauInstrucaoId": 5,
            "grauInstrucao": null,
            "nomeMae": "Genilda da Silva",
            "nomeConjugue": null,
            "pep": false
          },
          "contatos": {
            "contato": {
              "email": null,
              "telefone": "27981097702",
              "telefoneExtra": null
            },
            "referencia": [
              {
                "nome": "Anderson Kil",
                "telefone": "27998787655",
                "grau": 0
              },
              {
                "nome": "Amber",
                "telefone": "27983884623",
                "grau": 0
              }
            ]
          },
          "endereco": {
            "cep": "87050730",
            "logradouro": "Avenida Londrina",
            "numero": 234,
            "ufId": 16,
            "uf": "PR",
            "cidadeId": 1606,
            "cidade": "Maringá",
            "bairro": "Zona 08",
            "complemento": "casa"
          },
          "bancario": {
            "bancoId": "001",
            "banco": null,
            "agencia": 4232,
            "digito": "3",
            "numero": "6603-6",
            "conta": 1,
            "tipoConta": 0,
            "tipoOperacao": 12,
            "tempoConta": 3
          },
          "profissional": {
            "empresa": "Code",
            "ocupacaoId": 1,
            "ocupacao": "Assalariado",
            "profissaoId": 2,
            "profissao": null,
            "tempoEmpregoAtual": 1,
            "telefoneRH": "(27) 3339-9878",
            "pisPasep": 346775434,
            "renda": null,
            "outrasRenda": null,
            "tipoOutrasRenda": null
          },
          "anexo": [
            {
              "imagemId": 892,
              "documentoId": 8
            },
            {
              "imagemId": 893,
              "documentoId": 9
            },
            {
              "imagemId": 894,
              "documentoId": 10
            }
          ],
          "unidade": {
            "nomeVendedor": null,
            "cpfVendedor": null,
            "celularVendedor": null,
            "loginAgente": "edson.alves",
            "nomeMatriz": "Matriz",
            "nomeSupervisor": "Jean Lucio Bento",
            "celularSupervisor": null
          },
          "debitosConveniada": [],
          "historicoChamada": []
        }
      ],
      "required": [
        "id",
        "operacao",
        "cliente",
        "contatos",
        "endereco",
        "profissional",
        "anexo",
        "unidade"
      ],
      "properties": {
        "id": {
          "$id": "#/properties/id",
          "type": "integer",
          "title": "The id schema",
          "description": "An explanation about the purpose of this instance.",
          "default": 0,
          "examples": [
            6
          ]
        },
        "exibeDebitos": {
          "$id": "#/properties/exibeDebitos",
          "type": "null",
          "title": "The exibeDebitos schema",
          "description": "An explanation about the purpose of this instance.",
          "default": null,
          "examples": [
            null
          ]
        },
        "motivo": {
          "$id": "#/properties/motivo",
          "type": "null",
          "title": "The motivo schema",
          "description": "An explanation about the purpose of this instance.",
          "default": null,
          "examples": [
            null
          ]
        },
        "operacao": {
          "$id": "#/properties/operacao",
          "type": "object",
          "title": "The operacao schema",
          "description": "An explanation about the purpose of this instance.",
          "default": {},
          "examples": [
            {
              "produtoId": 1,
              "diaRecebimento": 1,
              "convenioId": null,
              "vencimento": "2021-08-04",
              "tabelaJurosId": 1,
              "tabelaJurosValoresID": 12,
              "taxa": 17,
              "operacao": null,
              "valorContratado": 500,
              "prazo": 12,
              "prestacao": 108.43,
              "limitePrestacao": 2076.59,
              "dadosAdicionais": []
            }
          ],
          "required": [
            "produtoId",
            "diaRecebimento",
            "vencimento",
            "tabelaJurosId",
            "tabelaJurosValoresID",
            "valorContratado",
            "prazo",
            "prestacao"
          ],
          "properties": {
            "produtoId": {
              "$id": "#/properties/operacao/properties/produtoId",
              "type": "integer",
              "title": "The produtoId schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                1
              ]
            },
            "diaRecebimento": {
              "$id": "#/properties/operacao/properties/diaRecebimento",
              "type": "integer",
              "title": "The diaRecebimento schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                1
              ]
            },
            "convenioId": {
              "$id": "#/properties/operacao/properties/convenioId",
              "type": "null",
              "title": "The convenioId schema",
              "description": "An explanation about the purpose of this instance.",
              "default": null,
              "examples": [
                null
              ]
            },
            "vencimento": {
              "$id": "#/properties/operacao/properties/vencimento",
              "type": "string",
              "title": "The vencimento schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "2021-08-04"
              ]
            },
            "tabelaJurosId": {
              "$id": "#/properties/operacao/properties/tabelaJurosId",
              "type": "integer",
              "title": "The tabelaJurosId schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                1
              ]
            },
            "tabelaJurosValoresID": {
              "$id": "#/properties/operacao/properties/tabelaJurosValoresID",
              "type": "integer",
              "title": "The tabelaJurosValoresID schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                12
              ]
            },
            "valorContratado": {
              "$id": "#/properties/operacao/properties/valorContratado",
              "type": "integer",
              "title": "The valorContratado schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                500
              ]
            },
            "prazo": {
              "$id": "#/properties/operacao/properties/prazo",
              "type": "integer",
              "title": "The prazo schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                12
              ]
            },
            "prestacao": {
              "$id": "#/properties/operacao/properties/prestacao",
              "type": "number",
              "title": "The prestacao schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                108.43
              ]
            },
            "limitePrestacao": {
              "$id": "#/properties/operacao/properties/limitePrestacao",
              "type": "number",
              "title": "The limitePrestacao schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                2076.59
              ]
            },
            "dadosAdicionais": {
              "$id": "#/properties/operacao/properties/dadosAdicionais",
              "type": "array",
              "title": "The dadosAdicionais schema",
              "description": "An explanation about the purpose of this instance.",
              "default": [],
              "examples": [
                []
              ],
              "additionalItems": true,
              "items": {
                "$id": "#/properties/operacao/properties/dadosAdicionais/items"
              }
            }
          },
          "additionalProperties": true
        },
        "cliente": {
          "$id": "#/properties/cliente",
          "type": "object",
          "title": "The cliente schema",
          "description": "An explanation about the purpose of this instance.",
          "default": {},
          "examples": [
            {
              "cpf": "01037551605",
              "nome": "JEAN LUCIO BENTO",
              "nascimento": "1978-01-26",
              "rg": "432523",
              "rgEmissor": "ssp",
              "rgUfId": 8,
              "rgEmissao": "2010-07-09T18:56:34.686Z",
              "sexo": 0,
              "estadoCivil": 0,
              "nacionalidadeId": 1,
              "naturalidadeUfId": 8,
              "naturalidadeCidadeId": 1905,
              "grauInstrucaoId": 5,
              "nomeMae": "Genilda da Silva",
              "nomeConjugue": null,
              "pep": false
            }
          ],
          "required": [
            "cpf",
            "nome",
            "nascimento",
            "rg",
            "rgEmissor",
            "rgUfId",
            "rgEmissao",
            "sexo",
            "estadoCivil",
            "nacionalidadeId",
            "naturalidadeUfId",
            "naturalidadeCidadeId",
            "grauInstrucaoId",
            "nomeMae",
            "pep"
          ],
          "properties": {
            "cpf": {
              "$id": "#/properties/cliente/properties/cpf",
              "type": "string",
              "title": "The cpf schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "01037551605"
              ]
            },
            "nome": {
              "$id": "#/properties/cliente/properties/nome",
              "type": "string",
              "title": "The nome schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "JEAN LUCIO BENTO"
              ]
            },
            "nascimento": {
              "$id": "#/properties/cliente/properties/nascimento",
              "type": "string",
              "title": "The nascimento schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "1978-01-26"
              ]
            },
            "rg": {
              "$id": "#/properties/cliente/properties/rg",
              "type": "string",
              "title": "The rg schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "432523"
              ]
            },
            "rgEmissor": {
              "$id": "#/properties/cliente/properties/rgEmissor",
              "type": "string",
              "title": "The rgEmissor schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "ssp"
              ]
            },
            "rgUfId": {
              "$id": "#/properties/cliente/properties/rgUfId",
              "type": "integer",
              "title": "The rgUfId schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                8
              ]
            },
            "rgEmissao": {
              "$id": "#/properties/cliente/properties/rgEmissao",
              "type": "string",
              "title": "The rgEmissao schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "2010-07-09T18:56:34.686Z"
              ]
            },
            "sexo": {
              "$id": "#/properties/cliente/properties/sexo",
              "type": "integer",
              "title": "The sexo schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                0
              ]
            },
            "estadoCivil": {
              "$id": "#/properties/cliente/properties/estadoCivil",
              "type": "integer",
              "title": "The estadoCivil schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                0
              ]
            },
            "nacionalidadeId": {
              "$id": "#/properties/cliente/properties/nacionalidadeId",
              "type": "integer",
              "title": "The nacionalidadeId schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                1
              ]
            },
            "naturalidadeUfId": {
              "$id": "#/properties/cliente/properties/naturalidadeUfId",
              "type": "integer",
              "title": "The naturalidadeUfId schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                8
              ]
            },
            "naturalidadeCidadeId": {
              "$id": "#/properties/cliente/properties/naturalidadeCidadeId",
              "type": "integer",
              "title": "The naturalidadeCidadeId schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                1905
              ]
            },
            "naturalidadeCidade": {
              "$id": "#/properties/cliente/properties/naturalidadeCidade",
              "type": "null",
              "title": "The naturalidadeCidade schema",
              "description": "An explanation about the purpose of this instance.",
              "default": null,
              "examples": [
                null
              ]
            },
            "grauInstrucaoId": {
              "$id": "#/properties/cliente/properties/grauInstrucaoId",
              "type": "integer",
              "title": "The grauInstrucaoId schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                5
              ]
            },
            "nomeMae": {
              "$id": "#/properties/cliente/properties/nomeMae",
              "type": "string",
              "title": "The nomeMae schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "Genilda da Silva"
              ]
            },
            "pep": {
              "$id": "#/properties/cliente/properties/pep",
              "type": "boolean",
              "title": "The pep schema",
              "description": "An explanation about the purpose of this instance.",
              "default": false,
              "examples": [
                false
              ]
            }
          },
          "additionalProperties": true
        },
        "contatos": {
          "$id": "#/properties/contatos",
          "type": "object",
          "title": "The contatos schema",
          "description": "An explanation about the purpose of this instance.",
          "default": {},
          "examples": [
            {
              "contato": {
                "email": null,
                "telefone": "27981097702",
                "telefoneExtra": null
              },
              "referencia": [
                {
                  "nome": "Anderson Kil",
                  "telefone": "27998787655",
                  "grau": 0
                },
                {
                  "nome": "Amber",
                  "telefone": "27983884623",
                  "grau": 0
                }
              ]
            }
          ],
          "required": [
            "contato",
            "referencia"
          ],
          "properties": {
            "contato": {
              "$id": "#/properties/contatos/properties/contato",
              "type": "object",
              "title": "The contato schema",
              "description": "An explanation about the purpose of this instance.",
              "default": {},
              "examples": [
                {
                  "email": null,
                  "telefone": "27981097702",
                  "telefoneExtra": null
                }
              ],
              "required": [
                "telefone",
                "telefoneExtra"
              ],
              "properties": {
                "email": {
                  "$id": "#/properties/contatos/properties/contato/properties/email",
                  "type": "null",
                  "title": "The email schema",
                  "description": "An explanation about the purpose of this instance.",
                  "default": null,
                  "examples": [
                    null
                  ]
                },
                "telefone": {
                  "$id": "#/properties/contatos/properties/contato/properties/telefone",
                  "type": "string",
                  "title": "The telefone schema",
                  "description": "An explanation about the purpose of this instance.",
                  "default": "",
                  "examples": [
                    "27981097702"
                  ]
                },
                "telefoneExtra": {
                  "$id": "#/properties/contatos/properties/contato/properties/telefoneExtra",
                  "type": "null",
                  "title": "The telefoneExtra schema",
                  "description": "An explanation about the purpose of this instance.",
                  "default": null,
                  "examples": [
                    null
                  ]
                }
              },
              "additionalProperties": true
            },
            "referencia": {
              "$id": "#/properties/contatos/properties/referencia",
              "type": "array",
              "title": "The referencia schema",
              "description": "An explanation about the purpose of this instance.",
              "default": [],
              "examples": [
                [
                  {
                    "nome": "Anderson Kil",
                    "telefone": "27998787655",
                    "grau": 0
                  },
                  {
                    "nome": "Amber",
                    "telefone": "27983884623",
                    "grau": 0
                  }
                ]
              ],
              "additionalItems": true,
              "items": {
                "$id": "#/properties/contatos/properties/referencia/items",
                "anyOf": [
                  {
                    "$id": "#/properties/contatos/properties/referencia/items/anyOf/0",
                    "type": "object",
                    "title": "The first anyOf schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": {},
                    "examples": [
                      {
                        "nome": "Anderson Kil",
                        "telefone": "27998787655",
                        "grau": 0
                      }
                    ],
                    "required": [
                      "nome",
                      "telefone",
                      "grau"
                    ],
                    "properties": {
                      "nome": {
                        "$id": "#/properties/contatos/properties/referencia/items/anyOf/0/properties/nome",
                        "type": "string",
                        "title": "The nome schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": "",
                        "examples": [
                          "Anderson Kil"
                        ]
                      },
                      "telefone": {
                        "$id": "#/properties/contatos/properties/referencia/items/anyOf/0/properties/telefone",
                        "type": "string",
                        "title": "The telefone schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": "",
                        "examples": [
                          "27998787655"
                        ]
                      },
                      "grau": {
                        "$id": "#/properties/contatos/properties/referencia/items/anyOf/0/properties/grau",
                        "type": "integer",
                        "title": "The grau schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": 0,
                        "examples": [
                          0
                        ]
                      }
                    },
                    "additionalProperties": true
                  }
                ]
              }
            }
          },
          "additionalProperties": true
        },
        "endereco": {
          "$id": "#/properties/endereco",
          "type": "object",
          "title": "The endereco schema",
          "description": "An explanation about the purpose of this instance.",
          "default": {},
          "examples": [
            {
              "cep": "87050730",
              "logradouro": "Avenida Londrina",
              "numero": 234,
              "ufId": 16,
              "uf": "PR",
              "cidadeId": 1606,
              "cidade": "Maringá",
              "bairro": "Zona 08",
              "complemento": "casa"
            }
          ],
          "required": [
            "cep",
            "logradouro",
            "numero",
            "ufId",
            "cidadeId",
            "bairro"
          ],
          "properties": {
            "cep": {
              "$id": "#/properties/endereco/properties/cep",
              "type": "string",
              "title": "The cep schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "87050730"
              ]
            },
            "logradouro": {
              "$id": "#/properties/endereco/properties/logradouro",
              "type": "string",
              "title": "The logradouro schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "Avenida Londrina"
              ]
            },
            "numero": {
              "$id": "#/properties/endereco/properties/numero",
              "type": "integer",
              "title": "The numero schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                234
              ]
            },
            "ufId": {
              "$id": "#/properties/endereco/properties/ufId",
              "type": "integer",
              "title": "The ufId schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                16
              ]
            },
            "cidadeId": {
              "$id": "#/properties/endereco/properties/cidadeId",
              "type": "integer",
              "title": "The cidadeId schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                1606
              ]
            },
            "bairro": {
              "$id": "#/properties/endereco/properties/bairro",
              "type": "string",
              "title": "The bairro schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "Zona 08"
              ]
            },
            "complemento": {
              "$id": "#/properties/endereco/properties/complemento",
              "type": "string",
              "title": "The complemento schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "casa"
              ]
            }
          },
          "additionalProperties": true
        },
        "bancario": {
          "$id": "#/properties/bancario",
          "type": "object",
          "title": "The bancario schema",
          "description": "An explanation about the purpose of this instance.",
          "default": {},
          "examples": [
            {
              "bancoId": "001",
              "agencia": 4232,
              "digito": "3",
              "numero": "6603-6",
              "conta": 1,
              "tipoConta": 0,
              "tipoOperacao": 12,
              "tempoConta": 3
            }
          ],
          "required": [
            "bancoId",
            "agencia",
            "digito",
            "numero",
            "conta",
            "tipoConta",
            "tipoOperacao",
            "tempoConta"
          ],
          "properties": {
            "bancoId": {
              "$id": "#/properties/bancario/properties/bancoId",
              "type": "string",
              "title": "The bancoId schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "001"
              ]
            },
            "agencia": {
              "$id": "#/properties/bancario/properties/agencia",
              "type": "integer",
              "title": "The agencia schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                4232
              ]
            },
            "digito": {
              "$id": "#/properties/bancario/properties/digito",
              "type": "string",
              "title": "The digito schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "3"
              ]
            },
            "numero": {
              "$id": "#/properties/bancario/properties/numero",
              "type": "string",
              "title": "The numero schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "6603-6"
              ]
            },
            "conta": {
              "$id": "#/properties/bancario/properties/conta",
              "type": "integer",
              "title": "The conta schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                1
              ]
            },
            "tipoConta": {
              "$id": "#/properties/bancario/properties/tipoConta",
              "type": "integer",
              "title": "The tipoConta schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                0
              ]
            },
            "tipoOperacao": {
              "$id": "#/properties/bancario/properties/tipoOperacao",
              "type": "integer",
              "title": "The tipoOperacao schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                12
              ]
            },
            "tempoConta": {
              "$id": "#/properties/bancario/properties/tempoConta",
              "type": "integer",
              "title": "The tempoConta schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                3
              ]
            }
          },
          "additionalProperties": true
        },
        "profissional": {
          "$id": "#/properties/profissional",
          "type": "object",
          "title": "The profissional schema",
          "description": "An explanation about the purpose of this instance.",
          "default": {},
          "examples": [
            {
              "empresa": "Code",
              "ocupacaoId": 1,
              "profissaoId": 2,
              "tempoEmpregoAtual": 1,
              "telefoneRH": "(27) 3339-9878",
              "pisPasep": 346775434,
              "renda": null,
              "outrasRenda": null,
              "tipoOutrasRenda": null
            }
          ],
          "required": [
            "empresa",
            "ocupacaoId",
            "profissaoId",
            "tempoEmpregoAtual",
            "renda"
          ],
          "properties": {
            "empresa": {
              "$id": "#/properties/profissional/properties/empresa",
              "type": "string",
              "title": "The empresa schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "Code"
              ]
            },
            "ocupacaoId": {
              "$id": "#/properties/profissional/properties/ocupacaoId",
              "type": "integer",
              "title": "The ocupacaoId schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                1
              ]
            },
            "ocupacao": {
              "$id": "#/properties/profissional/properties/ocupacao",
              "type": "string",
              "title": "The ocupacao schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "Assalariado"
              ]
            },
            "profissaoId": {
              "$id": "#/properties/profissional/properties/profissaoId",
              "type": "integer",
              "title": "The profissaoId schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                2
              ]
            },
            "profissao": {
              "$id": "#/properties/profissional/properties/profissao",
              "type": "null",
              "title": "The profissao schema",
              "description": "An explanation about the purpose of this instance.",
              "default": null,
              "examples": [
                null
              ]
            },
            "tempoEmpregoAtual": {
              "$id": "#/properties/profissional/properties/tempoEmpregoAtual",
              "type": "integer",
              "title": "The tempoEmpregoAtual schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                1
              ]
            },
            "telefoneRH": {
              "$id": "#/properties/profissional/properties/telefoneRH",
              "type": "string",
              "title": "The telefoneRH schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "(27) 3339-9878"
              ]
            },
            "pisPasep": {
              "$id": "#/properties/profissional/properties/pisPasep",
              "type": "integer",
              "title": "The pisPasep schema",
              "description": "An explanation about the purpose of this instance.",
              "default": 0,
              "examples": [
                346775434
              ]
            },
            "renda": {
              "$id": "#/properties/profissional/properties/renda",
              "type": "null",
              "title": "The renda schema",
              "description": "An explanation about the purpose of this instance.",
              "default": null,
              "examples": [
                null
              ]
            },
            "outrasRenda": {
              "$id": "#/properties/profissional/properties/outrasRenda",
              "type": "null",
              "title": "The outrasRenda schema",
              "description": "An explanation about the purpose of this instance.",
              "default": null,
              "examples": [
                null
              ]
            },
            "tipoOutrasRenda": {
              "$id": "#/properties/profissional/properties/tipoOutrasRenda",
              "type": "null",
              "title": "The tipoOutrasRenda schema",
              "description": "An explanation about the purpose of this instance.",
              "default": null,
              "examples": [
                null
              ]
            }
          },
          "additionalProperties": true
        },
        "anexo": {
          "$id": "#/properties/anexo",
          "type": "array",
          "title": "The anexo schema",
          "description": "An explanation about the purpose of this instance.",
          "default": [],
          "examples": [
            [
              {
                "imagemId": 892,
                "documentoId": 8
              },
              {
                "imagemId": 893,
                "documentoId": 9
              }
            ]
          ],
          "additionalItems": true,
          "items": {
            "$id": "#/properties/anexo/items",
            "anyOf": [
              {
                "$id": "#/properties/anexo/items/anyOf/0",
                "type": "object",
                "title": "The first anyOf schema",
                "description": "An explanation about the purpose of this instance.",
                "default": {},
                "examples": [
                  {
                    "imagemId": 892,
                    "documentoId": 8
                  }
                ],
                "required": [
                  "imagemId",
                  "documentoId"
                ],
                "properties": {
                  "imagemId": {
                    "$id": "#/properties/anexo/items/anyOf/0/properties/imagemId",
                    "type": "integer",
                    "title": "The imagemId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                      892
                    ]
                  },
                  "documentoId": {
                    "$id": "#/properties/anexo/items/anyOf/0/properties/documentoId",
                    "type": "integer",
                    "title": "The documentoId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                      8
                    ]
                  }
                },
                "additionalProperties": true
              }
            ]
          }
        },
        "unidade": {
          "$id": "#/properties/unidade",
          "type": "object",
          "title": "The unidade schema",
          "description": "An explanation about the purpose of this instance.",
          "default": {},
          "examples": [
            {
              "nomeVendedor": null,
              "cpfVendedor": null,
              "celularVendedor": null,
              "loginAgente": "edson.alves",
              "nomeMatriz": "Matriz",
              "nomeSupervisor": "Jean Lucio Bento",
              "celularSupervisor": null
            }
          ],
          "required": [
            "nomeVendedor",
            "cpfVendedor",
            "celularVendedor",
            "loginAgente",
            "nomeMatriz",
            "nomeSupervisor",
            "celularSupervisor"
          ],
          "properties": {
            "nomeVendedor": {
              "$id": "#/properties/unidade/properties/nomeVendedor",
              "type": "null",
              "title": "The nomeVendedor schema",
              "description": "An explanation about the purpose of this instance.",
              "default": null,
              "examples": [
                null
              ]
            },
            "cpfVendedor": {
              "$id": "#/properties/unidade/properties/cpfVendedor",
              "type": "null",
              "title": "The cpfVendedor schema",
              "description": "An explanation about the purpose of this instance.",
              "default": null,
              "examples": [
                null
              ]
            },
            "celularVendedor": {
              "$id": "#/properties/unidade/properties/celularVendedor",
              "type": "null",
              "title": "The celularVendedor schema",
              "description": "An explanation about the purpose of this instance.",
              "default": null,
              "examples": [
                null
              ]
            },
            "loginAgente": {
              "$id": "#/properties/unidade/properties/loginAgente",
              "type": "string",
              "title": "The loginAgente schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "edson.alves"
              ]
            },
            "nomeMatriz": {
              "$id": "#/properties/unidade/properties/nomeMatriz",
              "type": "string",
              "title": "The nomeMatriz schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "Matriz"
              ]
            },
            "nomeSupervisor": {
              "$id": "#/properties/unidade/properties/nomeSupervisor",
              "type": "string",
              "title": "The nomeSupervisor schema",
              "description": "An explanation about the purpose of this instance.",
              "default": "",
              "examples": [
                "Jean Lucio Bento"
              ]
            },
            "celularSupervisor": {
              "$id": "#/properties/unidade/properties/celularSupervisor",
              "type": "null",
              "title": "The celularSupervisor schema",
              "description": "An explanation about the purpose of this instance.",
              "default": null,
              "examples": [
                null
              ]
            }
          },
          "additionalProperties": true
        },
        "debitosConveniada": {
          "$id": "#/properties/debitosConveniada",
          "type": "array",
          "title": "The debitosConveniada schema",
          "description": "An explanation about the purpose of this instance.",
          "default": [],
          "examples": [
            []
          ],
          "additionalItems": true,
          "items": {
            "$id": "#/properties/debitosConveniada/items"
          }
        },
        "historicoChamada": {
          "$id": "#/properties/historicoChamada",
          "type": "array",
          "title": "The historicoChamada schema",
          "description": "An explanation about the purpose of this instance.",
          "default": [],
          "examples": [
            []
          ],
          "additionalItems": true,
          "items": {
            "$id": "#/properties/historicoChamada/items"
          }
        }
      },
      "additionalProperties": true
    }'
where method = 'PUT' and route = '/api/proposta/entrada-mesa-de-credito/{id}'
