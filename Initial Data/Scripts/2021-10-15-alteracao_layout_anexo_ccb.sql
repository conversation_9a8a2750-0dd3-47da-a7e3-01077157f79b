update versaoLayoutContrato set Layout = '<!DOCTYPE html>
<html>
	<head>
		<style type="text/css">            @page {                margin: 20px 50px;                margin-bottom: 10px;            }                html,            body {                height: 100%;                margin: 0px;            }                body {                font-family: sans-serif;                color: #111;            }                h4 {                margin: 0px;                font-size: 13px;                font-weight: 700;                text-transform: uppercase;                margin-bottom: 3px;            }                p {                text-align: justify;                font-size: 12.5px;                margin: 0px;                line-height: 1.35em;                letter-spacing: 0.025em;            }                .page {                width: 100%;                height: 100%;                background-position: center;                background-repeat: no-repeat;            }                .page .header {                margin-bottom: 15px;            }                .page .header img.logo {                width: 130px;            }                .page .tables-info {                margin-top: 10px;                margin-bottom: 870px;                width: 100%;            }                .page .tables-info h5 {                margin: 0px;                font-size: 12.5px;                font-weight: 700;                text-transform: uppercase;                margin-bottom: 1px;                margin-left: 7px;            }                .page .tables-info h5 span.number {                display: inline-block;                width: 20px;            }                .page .tables-info .table {                margin-bottom: 5px;                padding: 0 1px;            }                .page .tables-info .table table {                width: 100%;                border-collapse: collapse;            }                .page .tables-info .table table tr {}                .page .tables-info .table table tr td {                font-size: 11.5px;                padding: 1px 4px;            }                .page .tables-info .table.table-1 table tr td.td-bairro {                width: 40% !important;            }                .page .tables-info .table.table-1 table tr td.td-cidade {                width: 30% !important;            }                .page .tables-info .table.table-1 table tr td.td-uf {                width: 12% !important;            }                .page .tables-info .table.table-1 table tr td.td-cep {                width: 23% !important;            }                .page .tables-info .table.table-1 table tr td.td-admissao {                width: 30% !important;            }                .page .tables-info .table.table-1 table tr td.td-matricula {                width: 12% !important;            }                .page .tables-info .table.table-1 table tr td.td-cargo {                width: 23% !important;            }                .page .tables-info .table.table-2 table tr td.td-cidade,            .page .tables-info .table.table-2 table tr td.td-uf,            .page .tables-info .table.table-2 table tr td.td-cep {                width: 33.3% !important;            }                .page .tables-info .table.table-3 table tr td.td-data-emissao {                width: 28%;            }                .page .tables-info .table.table-3 table tr td.td-prazo {                width: 16%;            }                .page .tables-info .table.table-3 table tr td.td-1-vencimento {                width: 28%;            }                .page .tables-info .table.table-3 table tr td.td-ultimo-vencimento {                width: 28%;            }                .page .tables-info .table.table-4 table tr td.td-banco,            .page .tables-info .table.table-4 table tr td.td-agencia,            .page .tables-info .table.table-4 table tr td.td-conta {                width: 33.3%;            }                .page .tables-signature {                width: 95%;                display: inherit;                margin: 0 auto;                margin-top: 20px;            }                .page .tables-signature table {                width: 100%;                border-collapse: collapse;            }                .page .tables-signature table tr {}                .page .tables-signature table tr td {                width: 50%;                height: 32px;                vertical-align: top;                padding: 3px 5px;            }                .page .tables-signature table tr:first-child td {                vertical-align: middle;                height: 20px;                padding: 1px 5px;            }                .page .tables-signature table tr:first-child td p {                line-height: 1em;            }                .page .tables-signature table tr td p {}                .page .tables-signature table tr td p span.title {                text-transform: uppercase;            }                .page .footer {                width: 100%;                border-top: 2px solid #111;                padding-top: 3px;                margin-top: 8px;            }                .page .footer hr {                width: 100%;                margin: 0px;                border: none;                border-top: 1px solid #111;                margin-bottom: 2px;            }                .page .footer p {                font-family: serif;                font-size: 12.5px;            }                .page .footer .left {                display: inline-block;                width: 60%;                float: left;                text-transform: uppercase;            }                .page .footer .right {                display: inline-block;                width: 39%;                float: right;                text-align: right;            }                .page-break {                page-break-after: always;            }                .page#first-page {}                .page#first-page .info-footer {                padding-top: 17px;            }                .page#first-page .info-footer hr {                margin: 0px;                margin-bottom: 7px;                width: 25%;                border: none;                border-top: 1px solid #111;            }                .page#first-page .info-footer p {                font-size: 11.5px;                letter-spacing: 0.025em;                line-height: 1.2em;            }                .page#second-page .tables-signature {                margin-top: 20px;                margin-bottom: 80px;            }        </style>
	</head>
	<body>
		<div class="page" id="first-page">
			<div class="header">
				<img alt="Embedded Image" class="logo" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAT0AAABECAIAAAB8o3V3AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAEa1SURBVHhe7X2FdxVJ8/b35+wuusSI4C4LLJLcuEGAENxhIbg7LM6ii7u7ewJBIkQgCQSIEBcISeD3PFU9N8FZ4D3n45wUzaSmp7q6u7qe7uq5c+f+v7e1VEu19LNRLW5rqZZ+PqrFbS3V0s9HtbitpVr6+agWt7VUSz8f1eK2lmrp56Na3NZSLf18VIvbWqqln49qcVtLtfTzUS1ua6mWfj76Am7fCJkTIc2RrCpJ5qrJxpmVTM47hFMUsRhLEkkOVUgoxhPrip1EWRWSXZqCKsFT/Nf2vENQJxo/S6KNqQZJdUJolUVUz0qF/xhRjbbKXNcm8QSFqz5onp0snVb3qzXwr9ULK6uGWiNpFfocWZJIHxO22mlaYtG7ouiClOZIaXvelxeyX32XWNCwHxIKyPhCiCx7bK58kfILio6cuLzqnz2r1u81Cbz91M5/mPmp/PcyP5X/0cxP5b+X+WG+8Nt2Hb8Xm2w69mn6KtyCzHl1Dq3M8XtTVV5enp2TH5fw6OqNu2cvRp29GH1O0vlL0dExCSkPnxQUllRUVJpyHBsZGgyhZlSTwNIkEIXw59Wr8sys3IQHaZeu3Dp7IercJSiPQrpw+Vb07fhHqRmFBcVVlepJHPWaZHQJT5XCSuNFufx9/briydPsu/eTL1+/e/7SrXMXmVAR9N++k5CW/qygsFgcSv8bki6oYlJ1rii1rhiASabiFnxVRWVFYWHJw0cZ127eh6HEYlGoERa7EXU/MSktJze//HUFrSGarF6oHqoyTdHLkvSKnuBaSUnZk4ys2PhHqAKdQhU4Xrt5Lzb+YcbTrLKyVyxvLIaEUto2EJWrMuqi3moibplFGb1SVVWFwS0uKcvMfIGxjot/GJeQej0qFn05j35diLp05Tbc4F5cStyDRw9Tn2Aoi4pLMVxUZBH7QRK1pka0gRb8GkpMTrMFj23cMqhxq2CTwNtP7fyHmZ/Kfy/zU/kfzfxU/nuZH+YL375bxJKV203HPk1fjpPViHJUMlmvysufZebcvBW379C5ZSt3jZ64NHTAVO+QcbaQ8d7BfyH5hP41YOicydPXrNt44NjJqxjR3PzC1xVwR/EI8RXRDEiTMf4jXgocvnpp9O/Zf3rx8u3jIlcE9om0BVIzavEJHuffe8KAYbOnzlq7fvOhM+eiE1PSC4tLKyoqRLNWYGrSHENk3lZWVZWWvXr6PBv69x48N3fxlqFjFgb1m+yDZgdPwNEb+vtMGDRizvS569dvOnT63M3klCfFJSWVVRVwdFEmba6pmabRugQDmswfJrQtN7fgfmzS0eOX1204OGnG2t6wWPA47+DxUuN439AJYQNnjJ+yfMXa3YePX74Xm4QJsbzc3iOtxD4W5hzEWmHHygpAArA/cfbGpq2H5y7cNGL8kt4DpkOtLfgvHHtHTBv51+K5izdt3XH89NkbybBYUXEFeiQ6UYMok34pQtlqZjNLruEUKAXss1/kp6Q+uXUn4eyFm/sPnduw5fCiZdsmTV896q/FoycsDRs40zdEzBg0LqDPxPBhc4aPWzxq4tLJs9YsXrF9/ZZDB45cPHP+5u07D1LTnublFZa9fPm6AmPCCmokEGeuL9Kde4lOzfzrOPdyaRnUudeQTj0Hd+g+qGOPQWA69kACP5g88i0eDBIYkTHC7/F2YVPwY8IfKkERKWh4ya8WeE+JXXNnzyEtO/X73d2nvqtt1IQlpmOfps/h1u4pOpZWMoi9ci3m79W7QvpPa9I2rJ6LTx1nrzqOnnWcvOo4ef7m6KWpjoNXPWdbQ1fvtl0GDh+35N9dJ27fS8zLK6qqpKuxCpIuxRwqrfLVq1ePn2Ri0Vu6cmdI+FSPVsENXLzrOXlDG5OjrY6jV10oR0J1zjaHJoFdeg7/a/KKXftO372fVFhYxHbS9UQxNcuqQg6+UfXy1eu0J89PnL62eMW24H6TXVuG1Hex1XUybf4NjBM64snuoApnWyO3gI7dBkVOW7V7/5n4B4/g65WVaDPVCkSpmcoNgalEJmtnA/jndXnFi9yC23cfbN56dOioBa06DGjg4lvHyVbHqedvjr3Ql7rsFCtl1c6231392nQaMGjEvDUbDly9fj8rOxczGauj/avNJQxrB5by8guB8137zoyPXNGm6+Df3X3ru3jXcYbCXuiRWIx2QxX1XGwOHn7o0cQpq3fsOhkbl4KymFNEGwgKpe3sgXRNq3r7prz89dPnL27fTTx64sqaDfsmzVwTNmjGH72GNG4e2KCxb31nHxkOtL8XrUdPUGOCgXlp4XouXvVdvRu4+bi2DOrUY3C/wbNmzF2/8d9Dx05fuXX3QcaznLKX5axKpxBWDkt+mRBYurYKgscH9Zu0ddfxbbtPbN5+9N8dx7buPP7vzmNgcEQmjlt2HMURMmCQVNjwO49vrSGsMipcsyCOqrmmEuajIPI/Jqy8Cpv8D4R37j01dc7att0iGrr7jJ20zHTs0/RVuFUz6ojCa6NjEpet3uvlP9bR3R8ogqv9SueAi1ge74SRswaP7mITSHi6tQrFUO3YeybtcebLV6+oXmshozW8LSgoirp9f8GyLV09R9R3EldQbxZtnAvo7kBULzj6r6za81di2FbHwdPJw2/AsDn7D57NeJZZ/vq1aLSIqzkiutdY8W5Ex81ZtLltlwjx6V4GrpKgXDoijRdH/9VR+8WrDh7+w8csgtc+zsh8VQ799GxxbunJOyQeL71CyBr3IHXLjmPwcpfmQewRZh+xFSr61YHTHDvFKcn0yNTu4PW7m58tcNzq9XuxNmIt5XbAWIokXBV8PeVhxu59Z8KHznBswungN2n5b6YW6SMVykznIOiF9ViLrUFjW8SwObv2nX2YmvHyJYJnQSz/mVreEjlmekpLe7Z8zd6wgdObteldz9Fb7cPEGtWA6AuTKGeyqqYPWPJGxiTK9GzRqX9o+NQVa/YkJqVLr+hpHLGvI8VtIw/fOQs3YldlcoW0FyBZzA0htrcYTUo6S5FMhojpURimmrzK1MwHvVevkpEzBe0KaxR78wYbQL8+E34AbnX05I9A682bgoJixG9BfSMdmvhjofjNsafBJ1HEEaLPcZniqUDOGlomyBMkTdv1jZy2Mvp2LKIjqmVCB6qwiOXmFu49cM4/LPJ3dz8W50jT4Sy3oGdY+YJerc6BRyoXhLu3DJkya01cQkrZy9Iqszphfa/CjhF42777hHfQeKDlF2evX6iBLZSGsW08laTVoZa6DgQY0Ws1oF23AXOXbbkX/5Dtr6rUlVXJGEqSZL5FaH3+cvSIsYs8Wve2WiiawUilwihWTY3SwRrJwauhi0//IbOOn72Wm1dYWYnqAF8uv4AxNpZRt+Ijp65s1rYvNTh5olO/wSCYDuwzgjUZEScCZmM31e/k6dE6dNKMNbdiEkvLyqDVCiA4KOwKQUs6cep62y79BYqoiDqNHirUJOMunaqRqf2qTmyGjpdldu14m879d+8/LU5H0kq/huy4nTV/fU3csg9CiCYQLLx+XQEeOzXIwIwgZCLB8SoqKzERQwT5EIYAyC6McAYQswtDRnmUAikP4ZrNZlkSS+IoSlg7JMELennjBhLIVMLGwSdk/A/BLZwPraHro6LC4qLlq3a2aNuvngyYmFu9HGOj+JFFg+sGjuKgekmTGTNm/u7m6xvy19mLt1+Vmzke3cjMylmwZHOrjv0piYIYWvoBC9qH2ZomOMx0CPFUJmkMxMxVBy+/kInXb98rffkSHUBXMDTJD9PnLd7cvH0YhH9x6lGXksQ/MEM9wCdbbiYa41uiH22oJ81gqEmZXoj0EMReuxH7svwVoCsujqPMEVyCNSyvevmyfO+hsz38RtVz8ZbmSWxCzVoREwJItNm0weSDUYeWNtCSEPBq1iFs2+6T2TkFgKvM2pWYRg8eu+gdPFYayWZLcRkd2kQARm2SpC/WYq59h7z91DMwbOK5S7cQB3EyotHM5sJOpy9Et+sWwaZqRTrQYisq4VirP7BHNZNAvRdD6Jo5YoeazevYY+jBoxc55clSYc2Bwn6WFLe/e/jOnPcPwGByBbcKLWSWlJbhSL78NSY7IqqyEvMU8oEonOI6xgsQBZZK4TeIPgRmEFZYYlcPYUAN8riKU2ErIFxS+hLqTK1CGKKXcO7ycuSDgQB41Fj28hXiLyAfqRTVv3wFAYX0D8ctB6+wqGTGvHXAm/gf0Sjm1kFS3KrTmLGEH+jELFcl2S85edV19q7r5N22c/+Dhy5XVhC3eYVFU2at9mjTx7ggfYIatIjxY4IWSfWwXoRYkkxjpGGmCmjo6TMm5n6SGjQ19dn0OeudWwQLEgDRXoJb1mWc245bHM0CjgaIfuGRw0214Laeo1d9R1vv8KmXrt3BEFigFS+n1Qxut2w/1q7LIOj/BfEwlXCOMIExE5cmNoN+LG1gJnuhNaII5aWR2mVnD/+1Gw9k5+RzRApLtmw/2rE79RONENP2Q5LatP1GLRPNZVdlSRrImeKefmP2HTpfWFLKTsjw16DKs8Bt14HWANloMaPcUlKNWxkCK5mq38ctkl0DutCzY4+BB4+eh+msFZ7u959wO33uuvdwK4DEXMTlDkdgDLhV/IAH7lQAiSth+WswwCSEgU+7MI4Ke8gLYimMguBVWPD7Dm5pr8oqU6O99rKXKMXaywh7MDgFo/PCD8OtccE3lXl5RavW7XVs4g//Q2xMuBI2NbaCHB4dLRkSk+g3IsZTztNArPqKg1cDV1+vgNHxyY8rq14XlpRMm722Sds+dFNWwUQxdQj1P7ogGHtdrFcyTZJm6FW6CNaxCVNXPn6ciXUpNf3Z9Lnr3VuHsAiUiMNhBbOKSCaUqCPS+3GJSerlVcvXVZgOh3Y2cPXuEz794uUYcWslCSxhsqo3W3ceb//nwHrO3tBg9v9aBSYFU6n2FDzqsgmj+SLJxP6yy9iP8NTWpkvfY6euYqMLD9i2+1Q324j6ztoXVoF9vpTtIdq0uKnCrpDGVMbqu/aUDCajxj7dfUZu33MKa4y1b7dTFXHLaYh9kUGpbqplH9FTndgvK2ktTFIKV+2XkKitE9bbIxdRE21ofO+7cAsCMCT6lVDoRxMWVa6WXDCZTJVCuKqRsIIcV2vgtgqzhq6034VbGok8JjkxmDJiPfzJzy/aue90s4796DqyVphIT51DMAmjO3gEtO822Mt/jHyU8pdPyF89/Ma06jQA+BQxBR6H9lfHng3cvf1CJtxAnPmyvPhl6Yr1e1p17lfPGQrFv1ELK1KfMGPs2DSw3Z+DPYPH+oSO8w2Z4BsyvrvvqBYd+jekfnURKwG0zrbxk5cnpTxGnIL2/716d9uuEXVxiQupNkOU013YMJRybRncuecQr8Bx8jnQBDDdvEY0bd8XoYHl61iK6Y6cgKRHKPW7h/+AEXOjYxI4TrQiXQ106XoCmoe+q0dKjZJ0MXfqKU21tezYz9N/LMzlGzzBO2hcd58Rrf8Id2zqr05vSrGFRGDbzgP2HDiTk5MPTzl57ppv2ARMHDIP6gSqFSFhioSRvbCSN2nbu4vnMM/AMXAIr6Bxf3gO92jTm3MHG6BGM6s0ikh1NtgzqO/kMxeiKqVD9AeLzl6IMri1YK9jWtfZq5G7n3vr0LZdB3bsOaS73xhv9CjkL1voeO+Q8T78lGt8T9/RnXsMad9tYNN2YQ0b+7AuRaxZA2gTCBw6csk4Hg//GbcfxskYC6Dl6fPslEdPHqU9xQyOI3gwSA9TM5CUT3lkeAjY81XYXlAZJFxF/uMnmXn5RUCdLJ7mLoASII2WGMSaPS0/6AaDpdvEyVjbZfOM1fjbcIu4jj7HnvI/7AXiCWaFS1fu9PQZUcdFwyocdYfG6BHOAb55h34DR8xd9c/e85dvJSQ+epCUpin+QSoWh3lLNgeGTXZtGSprAr3fwd0/pN/kC1djysrKESQnJqX38hvdoLGPIEpdXJcF8vVcvJp3CBs+dtHajfuuXL8bD/3JaYnUn3ovNuXwsctzFm326zMRsYA0j6s0VI2JXJaUkiYTbdXeg+d6+Y1t0JjwE1djM2T73VPb07H74JFjF2/ZdvRmdFxCIjU/SHyEjty+8+Dg0QtwBYAKGEBBQZG2Td2OiHJrFTJl5qrMnByxGg6VGK45iza5tkJMTgHCg3ajrTTodW7iD5vMWbz52OkrsQkP1VwJSWl345JPnb+5fM2e8CFzmrfvCwT+AjsTY7a2f4Tv3Hc2KycPoL13P2XwqHkOTf0MhHTR1hmBDfNq6ObjHzZ56px1ew6eibodn5CYyioS06JvP9i1/+zkGav9Qic6uPtx+Ngv3ly0IgIb2uzUNGD4mAUwhfhGdSJuuw6Sm0+YFGxurUJ7+o4aMGzupBlrVq7dvWvfqfOXbl2/eS824dED1piakMxOkU/kYN2KSTh19sboyL8dPFA1rSfGhDY6FWrv1GPIwSMXqmsUe5L7EtnvS82c9/59qYLC4gNHLw4btzAkfErYoOl9B8/AsU/ENDBIfQZOQ3qPDxtUk6ewvaAw5HG1d8TUiOGzl67akfwwneis+gC3EnhjRcUR+ES4rEAFppCJBF6xDf5bcKuINbYy9uJfRJiYVKbMXNuwsR9d0CQi6lfxFefmQejAlp3HYLjMzBeYdQT/Rhl0FBcXP8nIvHbj/tLl27rZhtV18cJi0nfgjPMXozHr8K5BRdXt2/EtO4TLsgadrEJmX8LDrUVQv0HTt+06CbfLyn6BTgIX1MxGMkYpKizBnIeZZfaCjX/0GoJSjZoEjJ64NCEpHbaADOaOAcNmOXoEwNXExeEfjNjhKzj93T0gYtjcQ8cuJTxIfcFHlMrpKMYAnB2xh0xPf3Y1Knb2wk1tu0TU4+TFXa54PNopG1RHWxfPoTv3ncSsyVZV8QbjpBkrnZsHWuYyuAWDdcnTb/SKNbujbic8ycgqLilFNKVTJBIGu6T05fPnL27dSVi/+VBg2CRMN/VdbHDo3ftP5+TmI7IqKipesGRr8w68eyfK2SnClbj1QljezXvYspVbb96KS03PyC8ogFNof9ij14w+sGjciI5dunL7H72G1nVBGC93DaxZWHUiEFiyfAcao7ZWDzl5+ka7LgOxo44YPmfGgvWbtx++cCk6Lv7Ro9SMzKwc1AWPRN9Rk8TYtKX0C3wVFhhMOtg8+/eeWA+xvVpP6hX74IgJdMiBI+chLQstFYDoTl+iz8TJGU+zps5Z69w8AHMZBIBtHH939wFD3t2nmveAAHkV0HzD2wtKMsLuPpg6/ftMvHQ1hqP+7n0ptByZhKuExDH3Epet3rlu04F7ccnwKyAFSVda3QB/G25lcITkVOlNUUnxwaMX23UdiNlRXNA4CqZGuL57m96R01ZdvhYDj8cigLJVRBV12c0u9xV4A/3Jk8wdu08OHDZn2JiFmHRL6K8Q4TQVDdx2DK/DfRoncq0FkzGW8chpK6/euJubXyTKVCV16w1bOgVh/Aamwfzy785joeGTR05Ydj8+BWpRM6zy95rdLTv1Z+gIv9RQUBdAJ1vjFsGTZq65ffdBUXEJlmXRrPeWqFjqwh8ymBzTHj/fufc0IucGjX3V1WQhFcw4eDk08R8wbHZySppseRAgvZ4yezUmNdSidhPc2lxbhgwYOvvw8UvPMl9gwMTqpiJURVvxTGp8/Toz68XpczfHTVgS2IfPEuQVFMoNsKrzl6JsQeMkPDFzAUErgUYjd38sDkdOXnr6LAsrgNoK/6w62MG36ONb2P11xrOsg8cu946Y1sjNT2GPUZbllwnzBTYL8Eh+fit6QPfjHq5ev3/PobOYVlIfP83NK3j16pWqlpZDTDh7NwxukVGVlZ2zffcJ7J6cmgWr0aq3QjIuSF4BY06du6afLwjp33fWsY/SZ3CbX1C0//D5keMX9Rs8I3zorIgRs8OHzeo3ZOaA4bPB9x86EwnLJlL/obOQwGAokanC4CGMIixIgRrCQ2YOHj1v2aodSSmy3r6LW3g3snQ5zc7JW7dpf8vO/br7jty07QguIRM7RATRZCj0rfelxEAkOWGCIjRo0oxVWGToo2piwS0A4NqKn/jdvZ+oIwdbV2K1oN+boWKeWcZF5Zuq3NyCqFsJWGcKi4vp36wMfQNuE1t06o89kiBBqyAf3H/KjVtxCCLUD8QHLSLmodxkAMYVVZXZL/IvX7tz934KWq7ZiUmpgX0nywaYceCvzrJIYsF06oUYb8K0VeigCFcrtuidTFZUVVVUVLJzz6lutpEN3HzUIAIYTAFM7bsN2rH7BGIAyNfErUmCsW62ETv2nCosRqABQxEPCgmtg0eS3YBvEJLExaWcOR/9Ip83kCFTWFQyfd4697ahMJHolGawU70cPPxhscs37pa+eimllWh5tp+2kiRTnRq0tKz88vW7vcOnaTzCxZb9kk45emInPGveBoR12kIYChMc1szi0lL6KFtsb3MNMnWBYFkp+KYq7fFTLDW9/MfUdZaNNJZZ4tYmY22W3M49Bq9Ysys1/alUpz6j+v8Dbj/c36KpOS/yY+Mf3rwVixXi9p2E6Jh4BCOYesBH3Y5DAo+EDQUS+RjmqzB4CKMIC1JAhJkZeyPqfszdxLTHzzBMWFE/Gicj6Hv2PGfX/jP+fSag1w5N/HoPmHLmwk0EMmiY2d/Kba3vxS1IxwMaj5280tVrqMCpGrfg67n6DB09HysVogDxvLeVb4lbtTiNzj90SmFwQD6neSKbIvjPP7iEhS76doLiFu6i8wIZJ0/sSTJz8lQVihiPY0F9MMDKYDUCPvmDDIuqNmw+0LJTuLScsTE85le5bdu4ecCQUQviH2B5pCJpiWiqLkx9hgUxn6eIUeF/rf4YIA91IkGbmW5cmgePHL8k50UeBqC8vKImbo3dHD0D+ky8fPUOzCS41SrESvZ6tf00DM2GA7RhUGXR42nM3SSf0An1+WmcQRc0o2v1G3t39xl57PRVUW5fr1AEpBOcJqqRZAgDcvTk5Z5+Y+T2oeLWpvf8G7j6YGMfG58oBUlUpQ2WM2aQwKtytlCrYyZGgw+oVSCKRojYscfQOs7erIK3Hs2GiIxTr/pOXn94Dl/zz7609OdwYnt1Nar4AtlxO+NjuAV4kAloASclJWWIS8EDbODBCP8Sp6VlIgBGBHAKAVwyAlKQS6TcOgbhCM2SuH2FJ5tahXTg8vIL9xw86x06HkF1vca2+q7ejk39AvtFYoFBKczvuiBDyTfiVqmGyd5gcV+2cmdDV976o9vRO+GFZDr1HHLyzFXszawRooFQlr4G4uCpOrnILBkMhRmrwCmvQIC4jYlr2RlxMl2Qg8qbK3T34eMWY3a35O3DiaNVaXU1cgl+gqPyb96UlpYNG7sQUZlEyIyTZTrwqu/q4x00/vzF29IwQ8YdjUrqNvnS6mqlb948z84dNWFp4+YS7Bnnk7nM2ftP7xGYm2vi1ngnE3EbGBZ59dod1vYuoTr2Sv6xZyqgeGPNBirYI27Zdrxlx/6ywrN26RFx27xjX+xXEXq922DRSv2mxho9ExFWVoHwZ+mqnVRrcCszAjvl1bx9v3Ub91GBJKsxLGzlMVvzValJcgmO+yAxdeGSf9vLp75op8YdygtoPbHB7ukzctPWo0+f5XIfwFFQVVBgxkKYz9Gn4mQoARTjHzw6dvLqvkPnkPbsP7P34DlEznsOnAG//7DyZ3G67/B5ChyAwFlkWsLkcdy9/8zuA2dOn7vxKO0pAKy45d5Vt6lyamqtQVjbBo2aCyg269AnJHxKcL9JLi2DcDpm4tIXuQVYZIFYTAoA8LfhltYRMiYDJTxIHT1hKcYPbie3EBAt0+INXL1nL9iY8TQHMtVm5Wg               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"/>
			</div>
			<h4>ANEXO I CÉDULA DE CRÉDITO BANCÁRIO DE N º:</h4>
			<div class="tables-info">
				<h4 style="text-decoration: underline; text-align: center;">DETALHAMENTO DE TÍTULOS/CHEQUE</h4>
				<br/>
				<h5>                    </span>IDENTIFICAÇÃO CHEQUE:</h5>
			<div class="table table-4">
				<table border="1" cellspacing="0">
					<tr>
						<td colspan="4">Banco: {{BANCO}}</td>
						<td colspan="4">Agência: {{AGENCIA}}</td>
					</tr>
					<tr>
						<td colspan="2">CPF: {{CPF}}</td>
						<td colspan="2">Vencimento:</td>
						<td colspan="2">Valor: R$</td>
						<td colspan="2">Número Documento:</td>
					</tr>
					<tr>
						<td colspan="8">Valor total: R$</td>
					</tr>
				</table>
			</div>
		</div>
		<div class="footer">
			<hr/>
			<p class="left">V – 4.0 JUN/2020 – ANEXO I</p>
			<p class="right pagination">Página 1</p>
		</div>
	</div>
</body>
</html>' where ProdutoId = 11 and Tipo = 1 and TipoDocumento = 'anexo-ccb'

