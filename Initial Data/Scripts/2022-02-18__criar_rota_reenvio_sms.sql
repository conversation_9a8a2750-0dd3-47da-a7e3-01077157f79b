
insert into api.route([controller_id],[name],[description],[route],[method],[procedure],[input],[output],response_type_id) values
(
    2,'Reenvio de SMS','Reenviar SMS para clientes de Proposta Digital','/api/proposta/reenvio_sms','POST','controller.stpProposta'
    ,'{
        "$schema": "http://json-schema.org/draft-07/schema",
        "$id": "http://example.com/example.json",
        "type": "object",
        "title": "The root schema",
        "description": "The root schema comprises the entire JSON document.",
        "default": {},
        "examples": [
            {
                "id": 2030190498
            }
        ],
        "required": [
            "id"
        ],
        "properties": {
            "id": {
                "$id": "#/properties/id",
                "type": "integer",
                "title": "The id schema",
                "description": "An explanation about the purpose of this instance.",
                "default": 0,
                "examples": [
                    2030190498
                ]
            }
        },
        "additionalProperties": true
    }'
    ,'{}',2
)