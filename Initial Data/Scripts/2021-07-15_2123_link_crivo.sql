if DB_NAME() = 'db-crefaz-prod'
    update externo.Origem set Parametros = '{"Link":"https://crivo.crefaz.com.br/ws/CreditTalkStateless.asmx","Usuario":"usercode1","Senha":"i1Ox31TwNY0c9JRlgo4XyrpmMX4LUR53"}' where Nome = 'Crivo'
else 
    update externo.Origem set Parametros = '{"Link":"https://crivo.crefaz.com.br/ws/CreditTalkStateless.asmx","Usuario":"usercode1","Senha":"i1Ox31TwNY0c9JRlgo4XyrpmMX4LUR53"}' where Nome = 'Crivo'

delete a from Parametro.ParametroHistorico a 
    join Parametro.Parametro b on a.ParametroId = b.Id
where b.Codigo in ('URL_SOAP_CRIVO','USUARIO_SOAP_CRIVO','SENHA_SOAP_CRIVO')

delete b from Parametro.Parametro b
where b.<PERSON>digo in ('URL_SOAP_CRIVO','USUARIO_SOAP_CRIVO','SENHA_SOAP_CRIVO')
