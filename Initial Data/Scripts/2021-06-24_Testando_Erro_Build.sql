insert into Migracao.Migracao (Arquivo, Criacao) VALUES('Teste da build criacao', dbo.getdateBR())

declare     @routeName varchar(255) = 'testando o rollback do build',
            @routeDesc varchar(MAX) = 'Registra o momento do aceite do contrato pelo APP',
            @route varchar(100) = '/api/proposta/teste-build-insert/{id}',
            @routeMethod varchar(10) = 'OPTIONS',
            @routeProcedure varchar(255) = 'controller.stpProposta',
            @routeInput varchar(MAX) = '{}',
            @routeOutput varchar(MAX) = '{}',
            @routeResponseId int = 2,
			@controllerId int = 3

insert into api.route
    (
        [controller_id],
        [name],
        [description],
        [route],
        [method],
        [procedure],
        [input],
        [output],
        [response_type_id]
    ) values (
        @controllerId,
        @routeName,
        @routeDesc,
        @route,
        @routeMethod,
        @routeProcedure,
        @routeInput,
        @routeOutput,
        @routeResponseId
    )