create table Unidade (
  Id int not null identity(1,2),
  CorrespondenteId int not null,
  UnidadeId int null,
  Tipo int, -- (<PERSON><PERSON>, Filial, Subestabelecido)
  CEP char(8) not null,
  CidadeId smallint not null,
  <PERSON><PERSON> varchar(50) not null,
  <PERSON>grad<PERSON><PERSON> varchar(100) not null,
  Numero varchar(20) not null,
  Complemento varchar(20) not null
)

alter table Unidade add constraint PK_Unidade primary key (Id)
alter table Unidade add constraint FK_Unidade_Correspondente foreign key (CorrespondenteId) references Correspondente(Id)
alter table Unidade add constraint FK_Unidade_Unidade foreign key (UnidadeId) references Unidade(Id)

insert into Parametro.Enum ([Codigo],	[Valor]) values
('tipoUnidade', '[{"id":0, "nome":"Matriz"},{"id":1, "nome":"Filial"}"},{"id":2, "nome":"Subestabelecido"}]')

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Enum tipoUnidade' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Unidade', @level2type=N'COLUMN',@level2name=N'Tipo'
