update api.route
set [input] = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "produtoId": 0,
            "convenioId": 0,
            "tabelaJurosId": 0,
            "valorPrestacao": 0,
            "vencimento": "2021-04-13T18:20:21.759Z",
            "renda": 1100.0
        }
    ],
    "required": [
        "produtoId",
        "convenioId",
        "tabelaJurosId",
        "valorPrestacao",
        "vencimento"
    ],
    "properties": {
        "produtoId": {
            "$id": "#/properties/produtoId",
            "type": "integer",
            "title": "The produtoId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                0
            ]
        },
        "convenioId": {
            "$id": "#/properties/convenioId",
            "type": "integer",
            "title": "The convenioId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                0
            ]
        },
        "tabelaJurosId": {
            "$id": "#/properties/tabelaJurosId",
            "type": "integer",
            "title": "The tabelaJurosId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                0
            ]
        },
        "valorPrestacao": {
            "$id": "#/properties/valorPrestacao",
            "type": "integer",
            "title": "The valorPrestacao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                0
            ]
        },
        "vencimento": {
            "$id": "#/properties/vencimento",
            "type": "string",
            "title": "The vencimento schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-04-13T18:20:21.759Z"
            ]
        },
        "renda": {
            "$id": "#/properties/renda",
            "type": "number",
            "title": "The renda schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0.0,
            "examples": [
                1100.0
            ]
        }
    },
    "additionalProperties": true
}'
where route = '/api/proposta/simulacao-solicitado'
and method = 'POST'