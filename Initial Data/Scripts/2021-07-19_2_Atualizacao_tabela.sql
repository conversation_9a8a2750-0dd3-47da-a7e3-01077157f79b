
update PropostaAbas set AbaOrdem = 1 where Nome = 'Documentação'
update PropostaAbas set AbaOrdem = 2 where Nome = 'Histórico CIA'
update PropostaAbas set AbaOrdem = 3 where Nome = 'Dad<PERSON>'
update PropostaAbas set AbaOrdem = 4 where Nome = 'Endereço'
update PropostaAbas set AbaOrdem = 5 where Nome = 'Profissional'
update PropostaAbas set AbaOrdem = 6 where Nome = 'Bancários'
update PropostaAbas set AbaOrdem = 7 where Nome = 'Operação'
update PropostaAbas set AbaOrdem = 8 where Nome = 'Contatos'
update PropostaAbas set AbaOrdem = 9 where Nome = 'Chamadas'
update PropostaAbas set AbaOrdem = 10 where Nome = 'Unidade'
insert PropostaAbas values ('Resumo Contato', null, 11)


insert into PropostaEtapaAbas values
((select id from PropostaAbas where Nome = 'Documentação'), 4),
((select id from PropostaAbas where Nome = 'Histórico CIA'), 4),
((select id from PropostaAbas where Nome = 'Dados <PERSON>ada<PERSON>'), 4),
((select id from PropostaAbas where Nome = 'Endereço'), 4),
((select id from PropostaAbas where Nome = 'Profissional'), 4),
((select id from PropostaAbas where Nome = 'Bancários'), 4),
((select id from PropostaAbas where Nome = 'Operação'), 4),
((select id from PropostaAbas where Nome = 'Contatos'), 4),
((select id from PropostaAbas where Nome = 'Chamadas'), 4),
((select id from PropostaAbas where Nome = 'Unidade'), 4),
((select id from PropostaAbas where Nome = 'Resumo Contato'), 4)

insert into PropostaEtapaAbas values
((select id from PropostaAbas where Nome = 'Documentação'), 5),
((select id from PropostaAbas where Nome = 'Histórico CIA'), 5),
((select id from PropostaAbas where Nome = 'Dados Cadastrais'), 5),
((select id from PropostaAbas where Nome = 'Endereço'), 5),
((select id from PropostaAbas where Nome = 'Profissional'), 5),
((select id from PropostaAbas where Nome = 'Bancários'), 5),
((select id from PropostaAbas where Nome = 'Operação'), 5),
((select id from PropostaAbas where Nome = 'Contatos'), 5),
((select id from PropostaAbas where Nome = 'Chamadas'), 5),
((select id from PropostaAbas where Nome = 'Unidade'), 5),
((select id from PropostaAbas where Nome = 'Resumo Contato'), 5)

insert into PropostaEtapaAbas values
((select id from PropostaAbas where Nome = 'Documentação'), 6),
((select id from PropostaAbas where Nome = 'Histórico CIA'), 6),
((select id from PropostaAbas where Nome = 'Dados Cadastrais'), 6),
((select id from PropostaAbas where Nome = 'Endereço'), 6),
((select id from PropostaAbas where Nome = 'Profissional'), 6),
((select id from PropostaAbas where Nome = 'Bancários'), 6),
((select id from PropostaAbas where Nome = 'Operação'), 6),
((select id from PropostaAbas where Nome = 'Contatos'), 6),
((select id from PropostaAbas where Nome = 'Chamadas'), 6),
((select id from PropostaAbas where Nome = 'Unidade'), 6),
((select id from PropostaAbas where Nome = 'Resumo Contato'), 6)

insert into PropostaEtapaAbas values
((select id from PropostaAbas where Nome = 'Documentação'), 7),
((select id from PropostaAbas where Nome = 'Histórico CIA'), 7),
((select id from PropostaAbas where Nome = 'Dados Cadastrais'), 7),
((select id from PropostaAbas where Nome = 'Endereço'), 7),
((select id from PropostaAbas where Nome = 'Profissional'), 7),
((select id from PropostaAbas where Nome = 'Bancários'), 7),
((select id from PropostaAbas where Nome = 'Operação'), 7),
((select id from PropostaAbas where Nome = 'Contatos'), 7),
((select id from PropostaAbas where Nome = 'Chamadas'), 7),
((select id from PropostaAbas where Nome = 'Unidade'), 7),
((select id from PropostaAbas where Nome = 'Resumo Contato'), 7)

insert into PropostaEtapaAbas values
((select id from PropostaAbas where Nome = 'Documentação'), 8),
((select id from PropostaAbas where Nome = 'Histórico CIA'), 8),
((select id from PropostaAbas where Nome = 'Dados Cadastrais'), 8),
((select id from PropostaAbas where Nome = 'Endereço'), 8),
((select id from PropostaAbas where Nome = 'Profissional'), 8),
((select id from PropostaAbas where Nome = 'Bancários'), 8),
((select id from PropostaAbas where Nome = 'Operação'), 8),
((select id from PropostaAbas where Nome = 'Contatos'), 8),
((select id from PropostaAbas where Nome = 'Chamadas'), 8),
((select id from PropostaAbas where Nome = 'Unidade'), 8),
((select id from PropostaAbas where Nome = 'Resumo Contato'), 8)

insert into PropostaEtapaAbas values
((select id from PropostaAbas where Nome = 'Documentação'), 9),
((select id from PropostaAbas where Nome = 'Histórico CIA'), 9),
((select id from PropostaAbas where Nome = 'Dados Cadastrais'), 9),
((select id from PropostaAbas where Nome = 'Endereço'), 9),
((select id from PropostaAbas where Nome = 'Profissional'), 9),
((select id from PropostaAbas where Nome = 'Bancários'), 9),
((select id from PropostaAbas where Nome = 'Operação'), 9),
((select id from PropostaAbas where Nome = 'Contatos'), 9),
((select id from PropostaAbas where Nome = 'Chamadas'), 9),
((select id from PropostaAbas where Nome = 'Unidade'), 9),
((select id from PropostaAbas where Nome = 'Resumo Contato'), 9)

insert into PropostaEtapaAbas values
((select id from PropostaAbas where Nome = 'Documentação'), 10),
((select id from PropostaAbas where Nome = 'Histórico CIA'), 10),
((select id from PropostaAbas where Nome = 'Dados Cadastrais'), 10),
((select id from PropostaAbas where Nome = 'Endereço'), 10),
((select id from PropostaAbas where Nome = 'Profissional'), 10),
((select id from PropostaAbas where Nome = 'Bancários'), 10),
((select id from PropostaAbas where Nome = 'Operação'), 10),
((select id from PropostaAbas where Nome = 'Contatos'), 10),
((select id from PropostaAbas where Nome = 'Chamadas'), 10),
((select id from PropostaAbas where Nome = 'Unidade'), 10),
((select id from PropostaAbas where Nome = 'Resumo Contato'), 10)

insert into PropostaEtapaAbas values
((select id from PropostaAbas where Nome = 'Documentação'), 11),
((select id from PropostaAbas where Nome = 'Histórico CIA'), 11),
((select id from PropostaAbas where Nome = 'Dados Cadastrais'), 11),
((select id from PropostaAbas where Nome = 'Endereço'), 11),
((select id from PropostaAbas where Nome = 'Profissional'), 11),
((select id from PropostaAbas where Nome = 'Bancários'), 11),
((select id from PropostaAbas where Nome = 'Operação'), 11),
((select id from PropostaAbas where Nome = 'Contatos'), 11),
((select id from PropostaAbas where Nome = 'Chamadas'), 11),
((select id from PropostaAbas where Nome = 'Unidade'), 11),
((select id from PropostaAbas where Nome = 'Resumo Contato'), 11)

insert into PropostaEtapaAbas values
((select id from PropostaAbas where Nome = 'Documentação'), 12),
((select id from PropostaAbas where Nome = 'Histórico CIA'), 12),
((select id from PropostaAbas where Nome = 'Dados Cadastrais'), 12),
((select id from PropostaAbas where Nome = 'Endereço'), 12),
((select id from PropostaAbas where Nome = 'Profissional'), 12),
((select id from PropostaAbas where Nome = 'Bancários'), 12),
((select id from PropostaAbas where Nome = 'Operação'), 12),
((select id from PropostaAbas where Nome = 'Contatos'), 12),
((select id from PropostaAbas where Nome = 'Chamadas'), 12),
((select id from PropostaAbas where Nome = 'Unidade'), 12),
((select id from PropostaAbas where Nome = 'Resumo Contato'), 12)

insert into PropostaEtapaAbas values
((select id from PropostaAbas where Nome = 'Documentação'), 13),
((select id from PropostaAbas where Nome = 'Histórico CIA'), 13),
((select id from PropostaAbas where Nome = 'Dados Cadastrais'), 13),
((select id from PropostaAbas where Nome = 'Endereço'), 13),
((select id from PropostaAbas where Nome = 'Profissional'), 13),
((select id from PropostaAbas where Nome = 'Bancários'), 13),
((select id from PropostaAbas where Nome = 'Operação'), 13),
((select id from PropostaAbas where Nome = 'Contatos'), 13),
((select id from PropostaAbas where Nome = 'Chamadas'), 13),
((select id from PropostaAbas where Nome = 'Unidade'), 13),
((select id from PropostaAbas where Nome = 'Resumo Contato'), 13)

