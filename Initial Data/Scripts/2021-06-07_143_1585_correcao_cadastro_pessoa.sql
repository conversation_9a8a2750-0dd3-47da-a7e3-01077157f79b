-- alterando estrutura de FK de PessoaFisica para Pessoa
ALTER TABLE [dbo].[Referencia] DROP CONSTRAINT [FK_Referencia_PessoaFisica] 
ALTER TABLE [dbo].[Referencia]  WITH CHECK ADD  CONSTRAINT [FK_Referencia_Pessoa] FOREIGN KEY([PessoaId])
REFERENCES [dbo].[Pessoa] ([Id])

-- adição de "referencias" no input da rota atualiza-pessoa
update api.route
set [input] = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "pessoa": {
                "id": 3,
                "nome": "Mariana da silva das neves",
                "nascimento": "1998-06-11",
                "cpf": "09487657831",
                "documentoEmissor": "123456",
                "documentoUFId": 8,
                "documentoEmissao": "1998-06-11",
                "pep": false,
                "sexo": 1,
                "estadoCivil": 2,
                "paisId": 1,
                "naturalidadeUfId": 1,
                "naturalidadeCidadeId": 1906,
                "grauInstrucao": 2,
                "nomeMae": "Elena Silva",
                "nomeConjuge": "Edson Igor Alves",
                "rg": "2938244",
                "pessoaFisicaId": 3
            },
            "endereco": {
                "cep": "12345678",
                "logradouro": "Rua das pedras",
                "numero": "9999",
                "ufid": 8,
                "cidadeId": 1906,
                "bairro": "Praia da costa ",
                "complemento": "AP 2113",
                "enderecoId": 3
            },
            "profissao": {
                "empresa": "FoxRound",
                "ocupacaoId": 2,
                "profissaoId": 2,
                "tempoempregoId": 1,
                "pis": "12344678",
                "renda": 8000.8,
                "outrasRendas": 38000.25,
                "tipoOutrasRendas": "Investimentos politicamente duvidosos",
                "telefoneRH": "12345678900",
                "rendaId": 1
            },
            "contatos": {
                "telefoneFixo": "12545356799",
                "telefoneCelular": "12545356799",
                "email": null,
                "contatosExtras": [
                    {
                        "contatoId": 24,
                        "pessoaId": 3,
                        "funcao": 1,
                        "telefone": "30999988809",
                        "email": ""
                    },
                    {
                        "contatoId": 25,
                        "pessoaId": 3,
                        "funcao": 1,
                        "telefone": "27999998811",
                        "email": ""
                    }
                ]
            },
            "referencias": [
                {
                    "referenciaId": 0,
                    "pessoaId": 3,
                    "nome": "Maria",
                    "telefone": "2733370001",
                    "grau": 0
                },
                {
                    "referenciaId": 1,
                    "pessoaId": 3,
                    "nome": "Jose",
                    "telefone": "2733380002",
                    "grau": 0
                }
            ]
        }
    ],
    "required": [
        "pessoa",
        "endereco",
        "profissao",
        "contatos",
        "referencias"
    ],
    "properties": {
        "pessoa": {
            "$id": "#/properties/pessoa",
            "type": "object",
            "title": "The pessoa schema",
            "description": "An explanation about the purpose of this instance.",
            "default": {},
            "examples": [
                {
                    "id": 3,
                    "nome": "Mariana da silva das neves",
                    "nascimento": "1998-06-11",
                    "cpf": "09487657831",
                    "documentoEmissor": "123456",
                    "documentoUFId": 8,
                    "documentoEmissao": "1998-06-11",
                    "pep": false,
                    "sexo": 1,
                    "estadoCivil": 2,
                    "paisId": 1,
                    "naturalidadeUfId": 1,
                    "naturalidadeCidadeId": 1906,
                    "grauInstrucao": 2,
                    "nomeMae": "Elena Silva",
                    "nomeConjuge": "Edson Igor Alves",
                    "rg": "2938244",
                    "pessoaFisicaId": 3
                }
            ],
            "required": [
                "id",
                "nome",
                "nascimento",
                "cpf",
                "documentoEmissor",
                "documentoUFId",
                "documentoEmissao",
                "pep",
                "sexo",
                "estadoCivil",
                "paisId",
                "naturalidadeUfId",
                "naturalidadeCidadeId",
                "grauInstrucao",
                "nomeMae",
                "nomeConjuge",
                "rg",
                "pessoaFisicaId"
            ],
            "properties": {
                "id": {
                    "$id": "#/properties/pessoa/properties/id",
                    "type": "integer",
                    "title": "The id schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        3
                    ]
                },
                "nome": {
                    "$id": "#/properties/pessoa/properties/nome",
                    "type": "string",
                    "title": "The nome schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "Mariana da silva das neves"
                    ]
                },
                "nascimento": {
                    "$id": "#/properties/pessoa/properties/nascimento",
                    "type": "string",
                    "title": "The nascimento schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "1998-06-11"
                    ]
                },
                "cpf": {
                    "$id": "#/properties/pessoa/properties/cpf",
                    "type": "string",
                    "title": "The cpf schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "09487657831"
                    ]
                },
                "documentoEmissor": {
                    "$id": "#/properties/pessoa/properties/documentoEmissor",
                    "type": "string",
                    "title": "The documentoEmissor schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "123456"
                    ]
                },
                "documentoUFId": {
                    "$id": "#/properties/pessoa/properties/documentoUFId",
                    "type": "integer",
                    "title": "The documentoUFId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        8
                    ]
                },
                "documentoEmissao": {
                    "$id": "#/properties/pessoa/properties/documentoEmissao",
                    "type": "string",
                    "title": "The documentoEmissao schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "1998-06-11"
                    ]
                },
                "pep": {
                    "$id": "#/properties/pessoa/properties/pep",
                    "type": "boolean",
                    "title": "The pep schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": false,
                    "examples": [
                        false
                    ]
                },
                "sexo": {
                    "$id": "#/properties/pessoa/properties/sexo",
                    "type": "integer",
                    "title": "The sexo schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1
                    ]
                },
                "estadoCivil": {
                    "$id": "#/properties/pessoa/properties/estadoCivil",
                    "type": "integer",
                    "title": "The estadoCivil schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        2
                    ]
                },
                "paisId": {
                    "$id": "#/properties/pessoa/properties/paisId",
                    "type": "integer",
                    "title": "The paisId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1
                    ]
                },
                "naturalidadeUfId": {
                    "$id": "#/properties/pessoa/properties/naturalidadeUfId",
                    "type": "integer",
                    "title": "The naturalidadeUfId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1
                    ]
                },
                "naturalidadeCidadeId": {
                    "$id": "#/properties/pessoa/properties/naturalidadeCidadeId",
                    "type": "integer",
                    "title": "The naturalidadeCidadeId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1906
                    ]
                },
                "grauInstrucao": {
                    "$id": "#/properties/pessoa/properties/grauInstrucao",
                    "type": "integer",
                    "title": "The grauInstrucao schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        2
                    ]
                },
                "nomeMae": {
                    "$id": "#/properties/pessoa/properties/nomeMae",
                    "type": "string",
                    "title": "The nomeMae schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "Elena Silva"
                    ]
                },
                "nomeConjuge": {
                    "$id": "#/properties/pessoa/properties/nomeConjuge",
                    "type": "string",
                    "title": "The nomeConjuge schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "Edson Igor Alves"
                    ]
                },
                "rg": {
                    "$id": "#/properties/pessoa/properties/rg",
                    "type": "string",
                    "title": "The rg schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "2938244"
                    ]
                },
                "pessoaFisicaId": {
                    "$id": "#/properties/pessoa/properties/pessoaFisicaId",
                    "type": "integer",
                    "title": "The pessoaFisicaId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        3
                    ]
                }
            },
            "additionalProperties": true
        },
        "endereco": {
            "$id": "#/properties/endereco",
            "type": "object",
            "title": "The endereco schema",
            "description": "An explanation about the purpose of this instance.",
            "default": {},
            "examples": [
                {
                    "cep": "12345678",
                    "logradouro": "Rua das pedras",
                    "numero": "9999",
                    "ufid": 8,
                    "cidadeId": 1906,
                    "bairro": "Praia da costa ",
                    "complemento": "AP 2113",
                    "enderecoId": 3
                }
            ],
            "required": [
                "cep",
                "logradouro",
                "numero",
                "ufid",
                "cidadeId",
                "bairro",
                "complemento",
                "enderecoId"
            ],
            "properties": {
                "cep": {
                    "$id": "#/properties/endereco/properties/cep",
                    "type": "string",
                    "title": "The cep schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "12345678"
                    ]
                },
                "logradouro": {
                    "$id": "#/properties/endereco/properties/logradouro",
                    "type": "string",
                    "title": "The logradouro schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "Rua das pedras"
                    ]
                },
                "numero": {
                    "$id": "#/properties/endereco/properties/numero",
                    "type": "string",
                    "title": "The numero schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "9999"
                    ]
                },
                "ufid": {
                    "$id": "#/properties/endereco/properties/ufid",
                    "type": "integer",
                    "title": "The ufid schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        8
                    ]
                },
                "cidadeId": {
                    "$id": "#/properties/endereco/properties/cidadeId",
                    "type": "integer",
                    "title": "The cidadeId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1906
                    ]
                },
                "bairro": {
                    "$id": "#/properties/endereco/properties/bairro",
                    "type": "string",
                    "title": "The bairro schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "Praia da costa "
                    ]
                },
                "complemento": {
                    "$id": "#/properties/endereco/properties/complemento",
                    "type": "string",
                    "title": "The complemento schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "AP 2113"
                    ]
                },
                "enderecoId": {
                    "$id": "#/properties/endereco/properties/enderecoId",
                    "type": "integer",
                    "title": "The enderecoId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        3
                    ]
                }
            },
            "additionalProperties": true
        },
        "profissao": {
            "$id": "#/properties/profissao",
            "type": "object",
            "title": "The profissao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": {},
            "examples": [
                {
                    "empresa": "FoxRound",
                    "ocupacaoId": 2,
                    "profissaoId": 2,
                    "tempoempregoId": 1,
                    "pis": "12344678",
                    "renda": 8000.8,
                    "outrasRendas": 38000.25,
                    "tipoOutrasRendas": "Investimentos politicamente duvidosos",
                    "telefoneRH": "12345678900",
                    "rendaId": 1
                }
            ],
            "required": [
                "empresa",
                "ocupacaoId",
                "profissaoId",
                "tempoempregoId",
                "pis",
                "renda",
                "outrasRendas",
                "tipoOutrasRendas",
                "telefoneRH",
                "rendaId"
            ],
            "properties": {
                "empresa": {
                    "$id": "#/properties/profissao/properties/empresa",
                    "type": "string",
                    "title": "The empresa schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "FoxRound"
                    ]
                },
                "ocupacaoId": {
                    "$id": "#/properties/profissao/properties/ocupacaoId",
                    "type": "integer",
                    "title": "The ocupacaoId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        2
                    ]
                },
                "profissaoId": {
                    "$id": "#/properties/profissao/properties/profissaoId",
                    "type": "integer",
                    "title": "The profissaoId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        2
                    ]
                },
                "tempoempregoId": {
                    "$id": "#/properties/profissao/properties/tempoempregoId",
                    "type": "integer",
                    "title": "The tempoempregoId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1
                    ]
                },
                "pis": {
                    "$id": "#/properties/profissao/properties/pis",
                    "type": "string",
                    "title": "The pis schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "12344678"
                    ]
                },
                "renda": {
                    "$id": "#/properties/profissao/properties/renda",
                    "type": "number",
                    "title": "The renda schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0.0,
                    "examples": [
                        8000.8
                    ]
                },
                "outrasRendas": {
                    "$id": "#/properties/profissao/properties/outrasRendas",
                    "type": "number",
                    "title": "The outrasRendas schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0.0,
                    "examples": [
                        38000.25
                    ]
                },
                "tipoOutrasRendas": {
                    "$id": "#/properties/profissao/properties/tipoOutrasRendas",
                    "type": "string",
                    "title": "The tipoOutrasRendas schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "Investimentos politicamente duvidosos"
                    ]
                },
                "telefoneRH": {
                    "$id": "#/properties/profissao/properties/telefoneRH",
                    "type": "string",
                    "title": "The telefoneRH schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "12345678900"
                    ]
                },
                "rendaId": {
                    "$id": "#/properties/profissao/properties/rendaId",
                    "type": "integer",
                    "title": "The rendaId schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": 0,
                    "examples": [
                        1
                    ]
                }
            },
            "additionalProperties": true
        },
        "contatos": {
            "$id": "#/properties/contatos",
            "type": "object",
            "title": "The contatos schema",
            "description": "An explanation about the purpose of this instance.",
            "default": {},
            "examples": [
                {
                    "telefoneFixo": "12545356799",
                    "telefoneCelular": "12545356799",
                    "email": null,
                    "contatosExtras": [
                        {
                            "contatoId": 24,
                            "pessoaId": 3,
                            "funcao": 1,
                            "telefone": "30999988809",
                            "email": ""
                        },
                        {
                            "contatoId": 25,
                            "pessoaId": 3,
                            "funcao": 1,
                            "telefone": "27999998811",
                            "email": ""
                        }
                    ]
                }
            ],
            "required": [
                "telefoneFixo",
                "telefoneCelular",
                "email",
                "contatosExtras"
            ],
            "properties": {
                "telefoneFixo": {
                    "$id": "#/properties/contatos/properties/telefoneFixo",
                    "type": "string",
                    "title": "The telefoneFixo schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "12545356799"
                    ]
                },
                "telefoneCelular": {
                    "$id": "#/properties/contatos/properties/telefoneCelular",
                    "type": "string",
                    "title": "The telefoneCelular schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": "",
                    "examples": [
                        "12545356799"
                    ]
                },
                "email": {
                    "$id": "#/properties/contatos/properties/email",
                    "type": "null",
                    "title": "The email schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": null,
                    "examples": [
                        null
                    ]
                },
                "contatosExtras": {
                    "$id": "#/properties/contatos/properties/contatosExtras",
                    "type": "array",
                    "title": "The contatosExtras schema",
                    "description": "An explanation about the purpose of this instance.",
                    "default": [],
                    "examples": [
                        [
                            {
                                "contatoId": 24,
                                "pessoaId": 3,
                                "funcao": 1,
                                "telefone": "30999988809",
                                "email": ""
                            },
                            {
                                "contatoId": 25,
                                "pessoaId": 3,
                                "funcao": 1,
                                "telefone": "27999998811",
                                "email": ""
                            }
                        ]
                    ],
                    "additionalItems": true,
                    "items": {
                        "$id": "#/properties/contatos/properties/contatosExtras/items",
                        "anyOf": [
                            {
                                "$id": "#/properties/contatos/properties/contatosExtras/items/anyOf/0",
                                "type": "object",
                                "title": "The first anyOf schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": {},
                                "examples": [
                                    {
                                        "contatoId": 24,
                                        "pessoaId": 3,
                                        "funcao": 1,
                                        "telefone": "30999988809",
                                        "email": ""
                                    }
                                ],
                                "required": [
                                    "contatoId",
                                    "pessoaId",
                                    "funcao",
                                    "telefone",
                                    "email"
                                ],
                                "properties": {
                                    "contatoId": {
                                        "$id": "#/properties/contatos/properties/contatosExtras/items/anyOf/0/properties/contatoId",
                                        "type": "integer",
                                        "title": "The contatoId schema",
                                        "description": "An explanation about the purpose of this instance.",
                                        "default": 0,
                                        "examples": [
                                            24
                                        ]
                                    },
                                    "pessoaId": {
                                        "$id": "#/properties/contatos/properties/contatosExtras/items/anyOf/0/properties/pessoaId",
                                        "type": "integer",
                                        "title": "The pessoaId schema",
                                        "description": "An explanation about the purpose of this instance.",
                                        "default": 0,
                                        "examples": [
                                            3
                                        ]
                                    },
                                    "funcao": {
                                        "$id": "#/properties/contatos/properties/contatosExtras/items/anyOf/0/properties/funcao",
                                        "type": "integer",
                                        "title": "The funcao schema",
                                        "description": "An explanation about the purpose of this instance.",
                                        "default": 0,
                                        "examples": [
                                            1
                                        ]
                                    },
                                    "telefone": {
                                        "$id": "#/properties/contatos/properties/contatosExtras/items/anyOf/0/properties/telefone",
                                        "type": "string",
                                        "title": "The telefone schema",
                                        "description": "An explanation about the purpose of this instance.",
                                        "default": "",
                                        "examples": [
                                            "30999988809"
                                        ]
                                    },
                                    "email": {
                                        "$id": "#/properties/contatos/properties/contatosExtras/items/anyOf/0/properties/email",
                                        "type": "string",
                                        "title": "The email schema",
                                        "description": "An explanation about the purpose of this instance.",
                                        "default": "",
                                        "examples": [
                                            ""
                                        ]
                                    }
                                },
                                "additionalProperties": true
                            }
                        ]
                    }
                }
            },
            "additionalProperties": true
        },
        "referencias": {
            "$id": "#/properties/referencias",
            "type": "array",
            "title": "The referencias schema",
            "description": "An explanation about the purpose of this instance.",
            "default": [],
            "examples": [
                [
                    {
                        "referenciaId": 0,
                        "pessoaId": 3,
                        "nome": "Maria",
                        "telefone": "2733370001",
                        "grau": 0
                    },
                    {
                        "referenciaId": 1,
                        "pessoaId": 3,
                        "nome": "Jose",
                        "telefone": "2733380002",
                        "grau": 0
                    }
                ]
            ],
            "additionalItems": true,
            "items": {
                "$id": "#/properties/referencias/items",
                "anyOf": [
                    {
                        "$id": "#/properties/referencias/items/anyOf/0",
                        "type": "object",
                        "title": "The first anyOf schema",
                        "description": "An explanation about the purpose of this instance.",
                        "default": {},
                        "examples": [
                            {
                                "referenciaId": 0,
                                "pessoaId": 3,
                                "nome": "Maria",
                                "telefone": "2733370001",
                                "grau": 0
                            }
                        ],
                        "required": [
                            "referenciaId",
                            "pessoaId",
                            "nome",
                            "telefone",
                            "grau"
                        ],
                        "properties": {
                            "referenciaId": {
                                "$id": "#/properties/referencias/items/anyOf/0/properties/referenciaId",
                                "type": "integer",
                                "title": "The referenciaId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    0
                                ]
                            },
                            "pessoaId": {
                                "$id": "#/properties/referencias/items/anyOf/0/properties/pessoaId",
                                "type": "integer",
                                "title": "The pessoaId schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    3
                                ]
                            },
                            "nome": {
                                "$id": "#/properties/referencias/items/anyOf/0/properties/nome",
                                "type": "string",
                                "title": "The nome schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "Maria"
                                ]
                            },
                            "telefone": {
                                "$id": "#/properties/referencias/items/anyOf/0/properties/telefone",
                                "type": "string",
                                "title": "The telefone schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": "",
                                "examples": [
                                    "2733370001"
                                ]
                            },
                            "grau": {
                                "$id": "#/properties/referencias/items/anyOf/0/properties/grau",
                                "type": "integer",
                                "title": "The grau schema",
                                "description": "An explanation about the purpose of this instance.",
                                "default": 0,
                                "examples": [
                                    0
                                ]
                            }
                        },
                        "additionalProperties": true
                    }
                ]
            }
        }
    },
    "additionalProperties": true
}'
where method = 'PUT'
and route = '/api/pessoa/Cadastro-Pessoa-Atualiza/{id}'