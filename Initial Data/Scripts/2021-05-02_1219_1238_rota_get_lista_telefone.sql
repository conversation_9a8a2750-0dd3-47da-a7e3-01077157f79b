BEGIN

    --
    -- Id do controller
    -- N<PERSON> necessário definir valor
    --
    declare @controllerId int 

    --
    -- Variáveis de inserção
    -- Defina os valores do controlador caso seja inserção
    -- Defina os valores da rota para inserção de nova rota
    --
    declare @controllerName varchar(50) = 'Blocklist',
            @controllerUser varchar(50) = 'crefazapisqlstag',
            @controllerDesc varchar(MAX) = 'Lista de telefones Bloqueados',
            @routeName varchar(255) = 'Listagem de Telefones Bloqueados',
            @routeDesc varchar(MAX) = 'Lista de Telefones Bloqueados',
            @route varchar(100) = '/api/bloqueado/telefone/lista',
            @routeMethod varchar(10) = 'POST',
            @routeProcedure varchar(255) = 'controller.stpBlocklist',
            @routeInput varchar(MAX) =  '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "filtroTelefone": "27999999999",
            "ordenacao": "inclusao",
            "ordenacaoAsc": false,
            "pagina": 1
        }
    ],
    "required": [],
    "properties": {
        "filtroTelefone": {
            "$id": "#/properties/filtroTelefone",
            "type": "string",
            "title": "The filtroTelefone schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "27999999999"
            ]
        },
        "ordenacao": {
            "$id": "#/properties/ordenacao",
            "type": "string",
            "title": "The ordenacao schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "inclusao"
            ]
        },
        "ordenacaoAsc": {
            "$id": "#/properties/ordenacaoAsc",
            "type": "boolean",
            "title": "The ordenacaoAsc schema",
            "description": "An explanation about the purpose of this instance.",
            "default": false,
            "examples": [
                false
            ]
        },
        "pagina": {
            "$id": "#/properties/pagina",
            "type": "integer",
            "title": "The pagina schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        }
    },
    "additionalProperties": true
}',
            @routeOutput varchar(MAX) = '{}',
            @routeResponseId int = 2

    select @controllerId = Id from api.controller where [name] = @controllerName

    if (@controllerId IS NULL)
    BEGIN
        insert into api.controller
        (
            [user],
            [name],
            [description]
        )
        VALUES
        (
            @controllerUser,
            @controllerName,
            @controllerDesc
        )

        set @controllerId = @@IDENTITY
    END

    insert into api.route
    (
        [controller_id],
        [name],
        [description],
        [route],
        [method],
        [procedure],
        [input],
        [output],
        [response_type_id]
    ) values (
        @controllerId,
        @routeName,
        @routeDesc,
        @route,
        @routeMethod,
        @routeProcedure,
        @routeInput,
        @routeOutput,
        @routeResponseId
    )

END
-- mudança para criar um novo commit