BEGIN
    --
    -- Id do controller
    -- Não necessário definir valor
    --
    declare @controllerId int

    --
    -- Variáveis de inserção
    -- Defina os valores do controlador caso seja inserção
    -- Defina os valores da rota para inserção de nova rota
    --
    declare @controllerName varchar(50) = 'Usuario',
            @controllerUser varchar(50) = 'crefazapisqlsprod',
            @controllerDesc varchar(MAX) = 'Acesso as propriedades do usuario do aplicativo',
            @routeName varchar(255) = 'Usuario',
            @routeDesc varchar(MAX) = 'Atualiza senha login rbm.integracao',
            @route varchar(max) = '/api/usuario/atualiza-senha',
            @routeMethod varchar(10) = 'POST',
            @routeProcedure varchar(255) = 'controller.stpUsuario',
            @routeInput varchar(MAX) = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "login": "login.login",
            "cpfcnpj": "12345678910",
            "senha": "(hash md5)"
        }
    ],
    "required": [
        "login",
        "cpfcnpj",
        "senha"
    ],
    "properties": {
        "login": {
            "$id": "#/properties/login",
            "type": "string",
            "title": "The login schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "login.login"
            ]
        },
        "cpfcnpj": {
            "$id": "#/properties/cpfcnpj",
            "type": "string",
            "title": "The cpfcnpj schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "12345678910"
            ]
        },
        "senha": {
            "$id": "#/properties/senha",
            "type": "string",
            "title": "The senha schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "(hash md5)"
            ]
        }
    },
    "additionalProperties": true
}',
            @routeOutput varchar(MAX) = '{}',
            @routeResponseId int = 2

    select @controllerId = Id from api.controller where [name] = @controllerName

    if (@controllerId IS NULL)
    BEGIN
        insert into api.controller
        (
            [user],
            [name],
            [description]
        )
        VALUES
        (
            @controllerUser,
            @controllerName,
            @controllerDesc
        )

        set @controllerId = @@IDENTITY
    END

    insert into api.route
    (
        [controller_id],
        [name],
        [description],
        [route],
        [method],
        [procedure],
        [input],
        [output],
        [response_type_id]
    ) values (
        @controllerId,
        @routeName,
        @routeDesc,
        @route,
        @routeMethod,
        @routeProcedure,
        @routeInput,
        @routeOutput,
        @routeResponseId
    )

END
-- mudança para criar um novo commit