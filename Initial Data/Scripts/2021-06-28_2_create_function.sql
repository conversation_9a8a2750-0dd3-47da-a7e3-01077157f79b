create FUNCTION dbo.fncCalculoTempoEspera(@analistaId int, @PropostaId int, @tipo int = 1)
RETURNS varchar (155) AS
BEGIN
    declare @dateTimeDistribuicao datetime,
            @dateTimeAbertura datetime,
            @retorno varchar(155)

    select @dateTimeDistribuicao = min([Data]) from PropostaStatusHistorico a with(nolock) where a.PropostaId = @PropostaId and AnalistaId = @analistaId
    select @dateTimeAbertura = min([DataAcao]) from PropostaStatusHistorico a with(nolock) where a.PropostaId = @PropostaId and AnalistaId = @analistaId
    
    if(@tipo = 1) --retorna varchar no formato time
    begin    
        if(@dateTimeAbertura is null)
        begin 
            set @retorno = dbo.fncFormataSegundos(DATEDIFF(second,@dateTimeDistribuicao,@dateTimeAbertura))
        end
        else
        begin
            set @retorno = dbo.fncFormataSegundos(DATEDIFF(second,@dateTimeDistribuicao,dbo.getdateBR()))
        end
    end
    if(@tipo = 2) --retorna diferença de minutos
    begin    
        if(@dateTimeAbertura is null)
        begin 
            set @retorno = cast(DATEDIFF(minute,@dateTimeDistribuicao,@dateTimeAbertura) as varchar)


        end
        else
        begin
            set @retorno = cast(DATEDIFF(minute,@dateTimeDistribuicao,dbo.getdateBR()) as varchar)
        end
    end

    return @retorno
END