BEGIN

    --
    -- Id do controller
    -- N<PERSON> necessário definir valor
    --
    declare @controllerId int

    --
    -- Variáveis de inserção
    -- Defina os valores do controlador caso seja inserção
    -- Defina os valores da rota para inserção de nova rota
    --
    declare @controllerName varchar(50) = 'Propostas',
            @controllerUser varchar(50) = 'crefazapisqlstag',
            @controllerDesc varchar(MAX) = 'Acessa a API de Propostas',
            @routeName varchar(255) = 'Cálculo de Vencimento',
            @routeDesc varchar(MAX) = 'Retorna um array com as datas de vencimentos calculadas',
            @route varchar(100) = '/api/proposta/calculo-vencimento',
            @routeMethod varchar(10) = 'POST',
            @routeProcedure varchar(255) = 'controller.stpProposta',
            @routeInput varchar(MAX) = '{
    "$schema": "http://json-schema.org/draft-07/schema",
    "$id": "http://example.com/example.json",
    "type": "object",
    "title": "The root schema",
    "description": "The root schema comprises the entire JSON document.",
    "default": {},
    "examples": [
        {
            "propostaId": 1,
            "convenioId": 1,
            "rota": null,
            "leitura": "2021-06-07",
            "vencimento": null
        }
    ],
    "required": [
        "propostaId",
        "convenioId",
        "leitura"
    ],
    "properties": {
        "propostaId": {
            "$id": "#/properties/propostaId",
            "type": "integer",
            "title": "The propostaId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "convenioId": {
            "$id": "#/properties/convenioId",
            "type": "integer",
            "title": "The convenioId schema",
            "description": "An explanation about the purpose of this instance.",
            "default": 0,
            "examples": [
                1
            ]
        },
        "rota": {
            "$id": "#/properties/rota",
            "type": "null",
            "title": "The rota schema",
            "description": "An explanation about the purpose of this instance.",
            "default": null,
            "examples": [
                null
            ]
        },
        "leitura": {
            "$id": "#/properties/leitura",
            "type": "string",
            "title": "The leitura schema",
            "description": "An explanation about the purpose of this instance.",
            "default": "",
            "examples": [
                "2021-06-07"
            ]
        },
        "vencimento": {
            "$id": "#/properties/vencimento",
            "type": "null",
            "title": "The vencimento schema",
            "description": "An explanation about the purpose of this instance.",
            "default": null,
            "examples": [
                null
            ]
        }
    },
    "additionalProperties": true
}',
            @routeOutput varchar(MAX) = '{}',
            @routeResponseId int = 2

    select @controllerId = Id from api.controller where [name] = @controllerName

    if (@controllerId IS NULL)
    BEGIN
        insert into api.controller
        (
            [user],
            [name],
            [description]
        )
        VALUES
        (
            @controllerUser,
            @controllerName,
            @controllerDesc
        )

        set @controllerId = @@IDENTITY
    END

    insert into api.route
    (
        [controller_id],
        [name],
        [description],
        [route],
        [method],
        [procedure],
        [input],
        [output],
        [response_type_id]
    ) values (
        @controllerId,
        @routeName,
        @routeDesc,
        @route,
        @routeMethod,
        @routeProcedure,
        @routeInput,
        @routeOutput,
        @routeResponseId
    )

END
-- mudança para criar um novo commit
