declare @informacao as TABLE (
    informacaoIdOld INT,
    informacaoIdNew INT
)

insert into @informacao
select a.Id, b.Id from externo.Informacao a 
inner join externo.Informacao b on a.Tag = REPLACE(b.Tag, '(Oferta de Produtos) - Oferta', '(<PERSON>ré<PERSON><PERSON>á<PERSON><PERSON>) - Oferta')
where a.Tag like '%(<PERSON>ré-Aná<PERSON><PERSON>) - Oferta%' and b.Tag like '%(Oferta de Produtos) - Oferta%'

UPDATE b
set InformacaoId = a.informacaoIdNew
from @informacao a
join externo.ConsultaDetalhes b on a.informacaoIdOld = b.InformacaoId 