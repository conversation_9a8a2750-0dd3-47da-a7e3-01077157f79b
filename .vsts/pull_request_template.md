Checklist de Avaliação de Pr Padrão

- [ ] A Pull Request(PR) possui task atrelada a mesma
- [ ] O nome da pr esta no padrão descrito na IT de Codificação
- [ ] A task possui nome e esse nome representa corretamente o que foi alterado em código
- [ ] A task está vinculada a branch de destino correta
- [ ] A PR está configurada para commit automático
- [ ] O nome dos arquivos das pastas Data e Script segue o padrão de nome do projeto
- [ ] Os arquivos das pastas Data e Script estão nomeados de forma que executem na ordem correta de alteração de dados
- [ ] Existe algum arquivo da pasta Data ou Script que esteja sendo alterado
- [ ] Alteração de campos ou tabelas seguem o padrão de nome PascalCase
- [ ] Campos e tabelas novas possuem descrição e esse descrição está clara e objetiva para que alguém de fora do projeto saiba para que serve o campo
- [ ] PK's, Fk's, Constraints e Indices estão nomeados conforma padrão de projeto
- [ ] A indentação do código proposto segue os padrões do projeto
- [ ] As variáveis novas que foram declaradas estão sendo usadas no código
- [ ] O código proposto atende o que foi solicitado em task
- [ ] O código proposto está simples e objetivo
- [ ] Objetos novos de banco possuem o script de concessão de acesso aos usuários de aplicação
- [ ] O autor incluiu todos os testes unitários de funções, procedure e/ou typeRoutes