
alter function [dbo].[fncVerificaAcessoProposta] (@usuarioId int, @propostaid int) returns bit as
begin
    declare @unidadeId int,
            @correspondenteId int,
            @return bit = 0,
            @UsuarioTipo int,
            @usuarioInterno int,
            @usuarioBackOffice int,
            @usuarioSupVendas int,
            @usuarioVendedor int,
            @usuariocliente varchar(11)
            
    select @usuarioInterno = id from Seguranca.UsuarioTipo with(nolock) where nome = 'Interno'
    select @usuarioBackOffice = id from Seguranca.UsuarioTipo with(nolock) where nome = 'BackOffice'
    select @usuarioSupVendas = id from Seguranca.UsuarioTipo with(nolock) where nome = 'Supervisor de Unidade'
    select @usuarioVendedor = id from Seguranca.UsuarioTipo with(nolock) where nome = 'Vendedor'

    select @UsuarioTipo = UsuarioTipoId from Seguranca.Usuario with(nolock) where Id = @usuarioId
    select @usuariocliente = login from Seguranca.Usuario a with(nolock) where Id = @usuarioId and PessoaId is not null
    select @unidadeId = UnidadeId from Proposta a with(nolock) where id = @propostaid
    select @correspondenteId = b.CorrespondenteId from UsuarioUnidade a with(nolock) join Unidade b with(nolock) on b.id = a.UnidadeId and b.tipo = 0 where a.UsuarioId = @usuarioId

    if @UsuarioTipo = @usuarioInterno
    begin
        set @return = 1
    end
    else if @usuariocliente is not null --usuario cliente
    begin
        select @return = 1 from Proposta a with(nolock) where a.id = @propostaid and a.cpf = @usuariocliente
    end
    else if @UsuarioTipo = @usuarioVendedor
    begin
        select @return = 1
        from Proposta a with(nolock)
        join Seguranca.Usuario b with(nolock) on b.UnidadeId = a.UnidadeId
        where a.id = @propostaid and b.id = @usuarioId
    end
    else if @UsuarioTipo not in (@usuarioSupVendas,@usuarioBackOffice)
    begin
        ;with cteHierarquiaProposta as(
            select a.id,b.HierarquiaNivelDetalheId,c.HierarquiaNivelDetalheId HierarquiaSuperior
            from Proposta a with(nolock)
            join CorrespondenteHierarquiaNivelDetalhe b with(nolock) on b.UnidadeId = a.UnidadeId and b.DataFim is null
            join HierarquiaNivelDetalhe c with(nolock) on c.id = b.HierarquiaNivelDetalheId
            where a.id = @propostaid
            
            union all

            select a.Id,b.id HierarquiaNivelDetalheId,b.HierarquiaNivelDetalheId HierarquiaSuperior
            from cteHierarquiaProposta a
            join HierarquiaNivelDetalhe b with(nolock) on b.id = a.HierarquiaSuperior
        )
        
        select @return = 1 
        from cteHierarquiaProposta a
        join UsuarioHierarquiaNivelDetalhe b on b.HierarquiaNivelDetalheId = a.HierarquiaNivelDetalheId and b.DataFim is null
        where UsuarioId = @usuarioId
    end
    else if @UsuarioTipo in (@usuarioSupVendas,@usuarioBackOffice)
    begin
        if @correspondenteId is not null
        begin
            select @return = 1 
            from Unidade a with(nolock)
            where a.tipo <> 0 and a.CorrespondenteId = @correspondenteId and a.id = @unidadeId
        end
        else
        begin
            ;with cteUnidades as (
                select b.id,b.unidadeId
                from UsuarioUnidade a with(nolock)
                join Unidade b with(nolock) on b.Id = a.UnidadeId and b.tipo <> 0 
                where a.UsuarioId = @usuarioId
                
                union all

                select a.id,a.UnidadeId
                from Unidade a with(nolock)
                join cteUnidades b on b.id = a.UnidadeId
                where a.tipo <> 0 and a.UnidadeId is not null
            )

            select @return = 1 
            from cteUnidades a
            where a.id = @unidadeId
        end
    end

    return @return 
end