alter function dbo.fncCadastroCredenciadoRegrasDeNegocio (@put_data varchar(max), @method varchar(10)) returns varchar(max)
as
begin
    declare @errors varchar(max) = '[]'

    declare @dadosGerais varchar(max),
            @endereco varchar(max),
            @visita varchar(max),
            @socios varchar(max),
            @comercial varchar(max),
            @dadosBancarios varchar(max),
            @documentos varchar(max),
            @regioes varchar(max),
            @produtos varchar(max),
            @unidades varchar(max),
            @hierarquia varchar(max),
            @credenciadoId int,
            @correspondenteId int,
            @tipoCredenciado tinyint,
            @subTipoCredenciado int,
            @cpfCnpj varchar(20),
            @tipoCredenciadoCorrespondente tinyint,
            @tipoCredenciadoConvenio tinyint,
            @ativo bit,
            @statusCredenciado int

    declare @contato table (
        id int,
        nome varchar(200),
        email varchar(300),
        telefone varchar(20),
        tipoContato tinyint,
        observacao varchar(300)
    )

    declare @dadosBancariosTabela table( 
        banco varchar(10), 
        agencia varchar(10), 
        agenciaDigito char(1), 
        contaNumero varchar(10),
        conta int,
        tipoConta tinyint,
        tempoConta int,
        tipoChavePix tinyint,
        chavePix varchar(20)
    )

    declare @unidadesTabela table (unidadeId int, hierarquia varchar(max))
    declare @sociosTabela table (nome varchar(200), ordem int identity(1,1))

    select @tipoCredenciadoCorrespondente = id from Parametro.fncRetornaEnumTabela('tipoCredenciado') where nome = 'Correspondente'
    select @tipoCredenciadoConvenio = id from Parametro.fncRetornaEnumTabela('tipoCredenciado') where nome = 'Conveniada'

    select @credenciadoId = json_value(@put_data, '$.id')
    select @correspondenteId = CorrespondenteId from Credenciado with(nolock) where Id = @credenciadoId

    select
        @statusCredenciado = statusCredenciado,
        @dadosGerais = dadosGerais,
        @endereco = endereco,
        @visita = visita,
        @socios = socios,
        @comercial = comercial,
        @dadosBancarios = dadosBancarios,
        @documentos = documentos,
        @regioes = regioes,
        @produtos = produtos,
        @unidades = unidades
    from openjson(@put_data)
    with(
        statusCredenciado int,
        dadosGerais nvarchar(max) as json,
        endereco nvarchar(max) as json,
        visita nvarchar(max) as json,
        socios nvarchar(max) as json,
        comercial nvarchar(max) as json,
        dadosBancarios nvarchar(max) as json,
        documentos nvarchar(max) as json,
        regioes nvarchar(max) as json,
        produtos nvarchar(max) as json,
        unidades nvarchar(max) as json
    )

    -- Hierarquia do Credenciado (matriz)
    select @hierarquia = hierarquia from openjson(@comercial) with(hierarquia nvarchar(max) as json)

    insert into @contato
    select
        *
    from openjson(@put_data, '$.contato')
    with(
        id int,
        nome varchar(200),
        email varchar(300),
        telefone varchar(20),
        tipoContato tinyint,
        observacao varchar(300)
    )

    insert into @dadosBancariosTabela
    select * from openjson(@dadosBancarios)
    with(
        banco varchar(10), 
        agencia varchar(10), 
        agenciaDigito char(1), 
        contaNumero varchar(10),
        conta int,
        tipoConta tinyint,
        tempoConta int,
        tipoChavePix tinyint,
        chavePix varchar(20)
    )

    -- Insert da hierarquia da Matriz
    insert into @unidadesTabela 
    select b.id, @hierarquia from Credenciado a with(nolock)
    join Unidade b with(nolock) on a.correspondenteId = b.correspondenteId and b.tipo = 0
    where a.id = @credenciadoId

    -- Insert recursivo da hierarquia das Unidades
    ;with cteUnidadeRecursiva as (
    select 
        a.unidadeId,
        b.hierarquia,
        a.unidades
    from openjson(@unidades)
    with(
        unidadeId int,
        unidades nvarchar(max) as json,
        comercial nvarchar(max) as json
    ) a
    cross apply openjson(comercial) with(hierarquia nvarchar(max) as json) b
    union all
    select 
        b.unidadeId,
        c.hierarquia,
        b.unidades
    from cteUnidadeRecursiva a
    cross apply openjson(a.unidades)
    with(
        unidadeId int, 
        unidades nvarchar(max) as json, 
        comercial nvarchar(max) as json
    ) b
    cross apply openjson(comercial) with(hierarquia nvarchar(max) as json) c
    )
    insert into @unidadesTabela
    select unidadeId, hierarquia from cteUnidadeRecursiva a with(nolock)
    
    insert into @sociosTabela (nome)
    select * from openjson(@socios) with(nome varchar(200))

    select
        @tipoCredenciado = tipoCredenciado,
        @subTipoCredenciado = subTipoCredenciado,
        @cpfCnpj = cpfCnpj,
        @ativo = ativo
    from openjson(@dadosGerais)
    with(
        tipoCredenciado tinyint,
        subTipoCredenciado int,
        cpfCnpj varchar(20),
        ativo bit
    )

    -- Validação se o credenciado existe caso seja a rota de edição.
    if lower(@method) = 'put' and not exists(select * from Credenciado with(nolock) where Id = @credenciadoId)
        set @errors = json_modify(@errors, 'append $', 'Credenciado não existe.')

    -- As duas validações abaixo : IMPROVEMENT 6639
    -- Não será possível trocar o tipo de um Credenciado
    if lower(@method) = 'put' and @tipoCredenciado <> (select iif(a.convenioId is not null, @tipoCredenciadoConvenio, @tipoCredenciadoCorrespondente) 
                                                        from Credenciado a with(nolock) 
                                                        where a.id = @credenciadoId)
        set @errors = json_modify(@errors, 'append $', 'Não é possível alterar o tipo do Credenciado.')

    -- Não será possível trocar o subTipo de um Credenciado
    if lower(@method) = 'put' and @subTipoCredenciado <> (select iif(b.id is not null, b.convenioTipoId, c.correspondenteSubTipo) 
                                                            from Credenciado a with(nolock) 
                                                            left join Convenio b with(nolock) on a.ConvenioId = b.Id 
                                                            left join Correspondente c with(nolock) on a.CorrespondenteId = c.Id 
                                                            where a.id = @credenciadoId)
        set @errors = json_modify(@errors, 'append $', 'Não é possível alterar o subTipo do Credenciado.')

    -- Pela definição do PBI, não deve ser possível reativar uma matriz (a flag deverá ser desabilitada permanentemente)
    if lower(@method) = 'put' and exists (select 1 
                                            from Credenciado a with(nolock)
                                            join Correspondente b with(nolock) on a.CorrespondenteId = b.Id
                                            where a.Id = @credenciadoId and @ativo = 1 and b.Ativo = 0
                                            )
        set @errors = json_modify(@errors, 'append $', 'Não é possível reativar uma Matriz.')
    
    -- Validando cadastro do credenciado pelo tipo do mesmo
    if lower(@method) = 'post' and exists(select * from Credenciado a with(nolock) join PessoaJuridica b with(nolock) on a.PessoaId = b.PessoaId left join Convenio c with(nolock) on a.ConvenioId = c.Id left join Correspondente d with(nolock) on a.CorrespondenteId = d.Id
    where b.CNPJ = @cpfCnpj 
    and ((c.id is null and @tipoCredenciado = @tipoCredenciadoCorrespondente) or (d.id is null and @tipoCredenciado = @tipoCredenciadoConvenio))
    and isnull(c.Ativo, d.Ativo) = 1)
        set @errors = json_modify(@errors, 'append $', 'CNPJ já cadastrado para o mesmo tipo e subtipo de credenciamento. Verifique e tente novamente?')

    -- Validações em comuns
    -- Validar tipo/subtipo
    if @tipoCredenciado not in (select id from Parametro.fncRetornaEnumTabela('tipoCredenciado'))
        set @errors = json_modify(@errors, 'append $','Tipo inválido.')
    
    if (@subTipoCredenciado not in (select b.id from Parametro.Enum a
                                    outer apply (select * from openjson(a.valor)
                                                with (
                                                    id int,
                                                    nome varchar(50),
                                                    tipoCanal tinyint
                                                )) b
                                where codigo = 'tipoCorrespondenteTipoCanal') and @tipoCredenciado = 0)
    or (@subTipoCredenciado not in (select id from ConvenioTipo with(nolock)) and @tipoCredenciado = 1)
        set @errors = json_modify(@errors, 'append $','Subtipo inválido.')

    if @statusCredenciado not in (select id from Parametro.fncRetornaEnumTabela('statusCredenciado'))
        set @errors = json_modify(@errors, 'append $', 'Status do Credenciado inválido.')
    
    -- CNPJ
    if len(@cpfCnpj) > 11 and dbo.fncValida_CNPJ(@cpfCnpj) = 0
    begin
        set @errors = json_modify(@errors, 'append $','CNPJ inválido')
    end
    -- CPF
    else if len(@cpfCnpj) <= 11 and dbo.fncValida_CPF(@cpfCnpj) = 0
    begin
        set @errors = json_modify(@errors, 'append $','CPF inválido')
    end

    -- validar telefones
    -- Campo telefone e array contato não são obrigatórios
    if exists (select 1 from @contato) and not exists(select * from @contato where (len(telefone) = 10 or len(telefone) = 11) or telefone is null)
        set @errors = JSON_MODIFY(@errors, 'append $', 'Telefone inválido.')

    -- validar email
    if exists (select * from @contato where dbo.fncValidaEmail(email) <> 1 and email is not null)
        set @errors = JSON_MODIFY(@errors, 'append $', 'Email inválido.')

    -- validar regioes
    -- Como identificar se é -> principal bit -> 1 - Se for Matriz do corrrespondente ou Conveniada, 0 - se for unidade
    if exists (select 1 from openjson(@regioes) where [value] is not null)
        set @errors = api.fncErrorMerge(@errors, dbo.fncCredenciadoValidaRegioes(@regioes, 1))

    -- se banco informado, deve existir em integracao.dadosBanco
    if exists(select 1 from @dadosBancariosTabela where banco is not null)
        if not exists(select 1 from @dadosBancariosTabela a join Integracao.DadosBanco b with(nolock) on a.banco = b.CodigoBancario)
            set @errors = JSON_MODIFY(@errors, 'append $', 'Banco informado não cadastrado.')
    
    -- agenciaDigito deve ser ou numerico, ou X.
    -- Dados bancários não são de preenchimento obrigatório.
    if exists (select 1 from @dadosBancariosTabela where (isnull(agenciaDigito, '[]') = '[]' or upper(trim(agenciaDigito)) not in ('', 'X')) and agenciaDigito not like '%[0-9]%')
        set @errors = JSON_MODIFY(@errors, 'append $', 'Digito de agência incorreto.')

    -- Data de visita não pode ser futura
    if @visita is not null and cast(JSON_VALUE(@visita, '$.dataHora') as datetime) > dbo.getdateBR()
        set @errors = JSON_MODIFY(@errors, 'append $', 'Data e hora não podem ser maior que a data e hora atual em [visita].')

    -- Pelo menos duas palavras no nome do sócio
    if exists (select 1 from @sociosTabela where dbo.fncValidaNome(nome, 2) <> 1)
        set @errors = JSON_MODIFY(@errors, 'append $', 'É necessário ter pelo menos um nome e um sobrenome para o sócio.')

    -- Validações exclusivas para o tipo Correspondente
    if @tipoCredenciado = @tipoCredenciadoCorrespondente
    begin

        -- Validação sobre o tipoModalidade do Credenciado
        if not exists (select 1 from Parametro.fncRetornaEnumTabela('tipoModalidade') a join openjson(@produtos) with (tipoModalidade tinyint) b on a.Id = b.tipoModalidade or b.tipoModalidade is null)
            set @errors = JSON_MODIFY(@errors, 'append $', 'Tipo de modalidade inválida para o Credenciado.')

        -- Validações sobre hierarquia
        select @errors = api.fncErrorMerge(@errors, dbo.fncHierarquiaValidaEdicaoCredenciado(hierarquia, unidadeId)) from @unidadesTabela

        -- Validação recursiva de produtos e regiões
        -- if exists (select 1 from openjson(@produtos) where [value] is not null)
        select @errors = api.fncErrorMerge(@errors, dbo.fncValidaConfiguracaoProdutoRegiaoUnidade(@put_data)) -- Atualmente essa validação só está sendo aplicada paras as unidades, descartando a matriz

        -- Validação de cadastroUnidadeRegrasDeNegocio
        set @unidades = JSON_MODIFY(@unidades, '$.tipoCredenciado', @tipoCredenciado)
        set @unidades = JSON_MODIFY(@unidades, '$.subTipoCredenciado', @subtipoCredenciado) 
        set @errors = api.fncErrorMerge(@errors, dbo.fncCadastroUnidadeRegrasDeNegocio((select json_query(@unidades) unidades for json path, without_array_wrapper, include_null_values)))
    end

    --- Validação de informações de Endereço
    if exists(select 1 from openjson(@endereco) with(cep varchar(9)) a 
                where LEN(dbo.formataCEP(a.cep, 0)) <> 8
                and nullif(trim(a.cep), '') is not null
    )
        set @errors = JSON_MODIFY(@errors, 'append $', 'CEP Inválido do Crendenciado')  

    if exists(select 1 from openjson(@unidades) a
                outer APPLY openjson(a.value, '$.endereco') 
                with(
                    cep VARCHAR(9)
                ) b
                where LEN(dbo.formataCEP(b.cep, 0)) <> 8 and nullif(trim(b.cep), '') is not null
    )
        set @errors = JSON_MODIFY(@errors, 'append $', 'CEP Inválido da Unidade/Subunidade') 


    return @errors
end