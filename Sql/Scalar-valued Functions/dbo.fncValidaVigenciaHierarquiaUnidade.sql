alter function [dbo].[fncValidaVigenciaHierarquiaUnidade] (@unidadeId int,@dataInicio date, @dataFim date, @origem varchar(60)) returns bit
as 
begin 

    declare @dataVigenciaAtual date,
            @datasConcomitantes bit = 0

    declare @CorrespondenteHierarquiaNivelDetalhe as table(
        Id int,
        CorrespondenteId int,
        UnidadeId int,
        HierarquiaNivelDetalheId int,
        DataInicio date,
        DataFim date
    )

    insert into @CorrespondenteHierarquiaNivelDetalhe
    select * from CorrespondenteHierarquiaNivelDetalhe with(nolock) where UnidadeId = @unidadeId

    select @dataVigenciaAtual = dataInicio from @CorrespondenteHierarquiaNivelDetalhe where UnidadeId = @unidadeId and DataFim is null

    if @origem = 'IntegracaoUsuarioRBM'
    begin
        ;with cteListaConcomitancia as (
            select
                Id,
                DataInicio,
                DataFim,
                1 sep
            from @CorrespondenteHierarquiaNivelDetalhe

            union all

            Select 
                b.Id,
                b.DataInicio,
                b.<PERSON>,
                2 sep
            from cteListaConcomitancia a 
            join @CorrespondenteHierarquiaNivelDetalhe b on b.DataInicio = a.DataFim and b.Id <> a.Id
        )

        select @datasConcomitantes = 1 from cteListaConcomitancia group by id having count(*) > 1

        if @datasConcomitantes = 1
            --se existir datainicio igual datafim é erro
            return 0
        else if (select count(1) from @CorrespondenteHierarquiaNivelDetalhe where Datafim is null) > 1
            --mais de uma datafim null é erro
            return 0
        else if exists (select 1 from @CorrespondenteHierarquiaNivelDetalhe group by DataInicio having count(DataInicio) > 1)
            --mais de uma datainicio igual é erro
            return 0
        else if exists (select 1 from @CorrespondenteHierarquiaNivelDetalhe group by DataFim having count(DataFim) > 1)
            --mais de uma datafim iguais é erro
            return 0
    end
    else if exists(select 1 from @CorrespondenteHierarquiaNivelDetalhe where UnidadeId = @unidadeId and @dataInicio is not null and datafim is null)
        return 0
    if exists(select 1 from @CorrespondenteHierarquiaNivelDetalhe where UnidadeId = @unidadeId and @dataInicio > @dataFim)
        return 0
    else if exists(select 1 from @CorrespondenteHierarquiaNivelDetalhe where UnidadeId = @unidadeId and @dataInicio between DataInicio and DataFim)
        return 0
    else if exists (select 1 from @CorrespondenteHierarquiaNivelDetalhe where UnidadeId = @unidadeId and @dataFim < DataInicio and datafim is null)
        return 0
    -- Valida data Final
    else if exists (select 1 from @CorrespondenteHierarquiaNivelDetalhe where UnidadeId = @unidadeId and datafim is not null 
                    and (
                        (@dataFim between DataInicio and datafim) 
                            or 
                        (@dataFim > DataInicio and @dataVigenciaAtual < DataFim)
                    )
                )
        return 0
    else if (@dataInicio is not null and @dataFim is not null) -- Ou será criado uma nova vigência ou finalizado uma
        return 0

    return 1
end