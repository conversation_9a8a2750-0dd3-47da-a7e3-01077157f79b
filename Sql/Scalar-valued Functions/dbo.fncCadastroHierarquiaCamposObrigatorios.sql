create function [dbo].[fncCadastroHierarquiaCamposObrigatorios] (@put_data varchar(max), @errors varchar(max)) 
returns varchar(max)
as 
begin

    declare @regrasValidacaoJson as table (
        Tag varchar(50),
        Obrigatorio bit,
        Mensagem varchar(255)
    )

    declare @json_entrada as table (
        [key] varchar(100),
        [value] varchar(max),
        [type] int
    )

    declare @hierarquiaNivel as table(
        id int,
        nome varchar(max),
        ordem int,
        id_secundario varchar(36)
    ) 

    declare @arvorePosicoes as table(
        id int,
        ativo bit,
        nome varchar(100),
        usuarioId int,
        vigenciaInicial date,
        id_secundario varchar(36)
    )

    declare @output_errors as table(
        mensagem varchar(100)
    )

    declare @posicoes nvarchar(max),
            @iterador varchar(100),
            @contador varchar(36) = '',
            @erro varchar(100),
            @obrigatorio bit,
            @valor varchar(max),
            @tipo int

    declare @camposObrigatoriosJson varchar(max) = 
    '{
        "nome": true,
        "ativo": false,
        "ordem": true,
        "hierarquiaNivel": true,
        "perfis": true,
        "posicoes": true
    }'

    -- Atribuições

    insert into @regrasValidacaoJson (Tag, Obrigatorio, Mensagem)
    select [key] as tag, [value] as valor, concat('O campo [', [key], '] é obrigatório.') [Mensagem] from openjson(@camposObrigatoriosJson)

    insert into @json_entrada select * from openjson(@put_data)

    insert into @hierarquiaNivel
    select *, (select * from vwNovoGuid) from openjson(@put_data, '$.hierarquiaNivel')
    with(
        id int,
        nome varchar(100),
        ordem int
    )

    select
        @posicoes = posicoes
    from openjson(@put_data)
    with(
        posicoes nvarchar(max) as json
    )

    -- Pegando os dados da árvore de posições
    ;with ctePosicoes as (

        select * from openjson(@posicoes)
        with (
            id int,
            ativo bit,
            nome varchar(100),
            usuarioId int,
            vigenciaInicial date,
            posicoes nvarchar(max) as json
        )

        union all

        select b.*
        from ctePosicoes a
        cross apply (
            select * from openjson(a.posicoes)
            with(
                id int,
                ativo bit,
                nome varchar(100),
                usuarioId int,
                vigenciaInicial date,
                posicoes nvarchar(max) as json
            )
        ) b

    ) 

    insert into @arvorePosicoes
    select distinct id, ativo, nome, usuarioId, vigenciaInicial, (select * from vwNovoGuid) from ctePosicoes
    

    while 1 = 1
    begin

        select top 1 
            @iterador = a.[key],
            @valor = a.[value],
            @tipo = a.[type],
            @obrigatorio = b.Obrigatorio,
            @erro = b.Mensagem
        from @json_entrada a join @regrasValidacaoJson b on a.[key] = b.Tag

        if @iterador is null break

        if @obrigatorio = 1
        begin 

            -- Valor
            if @valor = '' and @tipo in (1, 2, 3)
                set @errors = json_modify(@errors, 'append $', @erro)

            -- Array
            if @tipo = 4
            begin
                if not exists (select 1 from openjson(@valor))
                    set @errors = json_modify(@errors, 'append $', @erro)
                    
                else if @iterador = 'hierarquiaNivel'
                begin

                    while 1 = 1
                    begin
                        if @contador is null break

                        select @contador = max(id_secundario) from @hierarquiaNivel

                        if exists (select 1 from @hierarquiaNivel where id_secundario = @contador and nome = '' or nome is null)
                            set @errors = json_modify(@errors, 'append $', concat('O campo [nome] é obrigatório em ', @iterador))

                        if exists (select 1 from @hierarquiaNivel where id_secundario = @contador and ordem is null)
                            set @errors = json_modify(@errors, 'append $', concat('O campo [ordem] é obrigatório em ', @iterador))

                        delete from @hierarquiaNivel where id_secundario = @contador
                    end

                end

                else if @iterador = 'posicoes'
                begin
                    set @contador = ''
                    
                    while exists (select 1 from @arvorePosicoes)
                    begin
                        if @contador is null break

                        select @contador = max(id_secundario) from @arvorePosicoes

                        if exists (select a.nome from @arvorePosicoes a where a.id_secundario = @contador and a.nome = '')
                            set @errors = json_modify(@errors, 'append $', concat('O campo [nome] é obrigatório em ', @iterador))

                        delete from @arvorePosicoes where id_secundario = @contador
                        
                    end

                end

            end
        end

        delete a from @json_entrada a where a.[key] = @iterador

        set @iterador = null

    end

    
    insert into @output_errors
    select distinct concat('"', [value], '"') from openjson(@errors)

    return (select concat('[', string_agg(mensagem, ' , '), ']') from @output_errors)

end
