alter function [dbo].[fncCadastroUsuarioCamposObrigatorios] (@put_data varchar(max), @errors varchar(max)) returns varchar(max)
as 
begin

    declare @json varchar(max)
    declare @regrasValidacaoJson table ( Tag varchar(50), Obrigatorio bit, Mensagem varchar(255))

    declare @camposObrigatoriosJson varchar(max) = '{
        "ativo": false,
        "bloqueado": false,
        "dadosGerais": {
            "tipoUsuarioId": true,
            "nome":true,
            "perfilUsuarioId": true,
            "cpf": true,
            "rg": false,
            "dataNascimento": true,
            "pep": false,
            "departamentoId": true
        },
        "contato": {
            "telefone": false,
            "email": false
        },
        "endereco": {
            "cep": false,
            "logradouro": false,
            "numero": false,
            "ufId": true,
            "cidadeId": true,
            "bairro": false,
            "complemento": false
        },
        "dadosComerciais": {
            "termoMidia": false,
            "atuacaoPromotora": false,
            "codigoAgente": false,
            "classificacaoId": false,
            "matrizId": false,
            "unidadeId": false
        },
        "certificacoes": {
                "id": false,
                "certificacaoId": true,
                "tipoCertificacaoId": true,
                "validade": true
        }
    }'
    
    -- Declaração das tabelas que irão ser populadas pelo put data
    declare @perfilUsuario table(
        id int
    )

    declare @dadosComerciais table(
        termoMidia bit,
        atuacaoPromotora bit,
        codigoAgente varchar(60),
        classificacaoId int,
        matrizId int
    )

    declare @unidades table(
        id int
    )

    declare @certificacoes table(
        id int,
        certificacaoId int,
        tipoCertificacaoId int,
        validade date
    )

    -- Insert em dadosComerciais
    insert into @dadosComerciais
        select
            termoMidia,
            atuacaoPromotora,
            codigoAgente,
            classificacaoId,
            matrizId
        from openjson(@put_data, '$.dadosComerciais')
        with(
            termoMidia bit,
            atuacaoPromotora bit,
            codigoAgente varchar(60),
            classificacaoId int,
            matrizId int
        )

    insert into @perfilUsuario
        select
            [value]
        from openjson(@put_data, '$.dadosGerais.perfilUsuarioId')

    -- Insert de unidadesId
    insert into @unidades
        select
            [value]
        from openjson(@put_data, '$.dadosComerciais.unidadeId')
    
    -- Insert de certificacoes
    insert into @certificacoes
        select
            id,
            certificacaoId,
            tipoCertificacaoId,
            validade
        from openjson(@put_data, '$.certificacoes')
        with(
            id int,
            certificacaoId int,
            tipoCertificacaoId int,
            validade date
        )
    
    ;with cte as (
    select *, [key] as tag, concat('O campo "', [key], '" é obrigatório.') [Mensagem] from openjson(@camposObrigatoriosJson)
    union all 
    select b.*, concat(a.tag,'.',b.[key]) tag, concat('O campo "', b.[key], '" é obrigatório.') [Mensagem] from cte a 
        cross APPLY openjson(a.[VALUE]) b
        where a.TYPE in (4,5)
    )

    insert into @regrasValidacaoJson (Tag, Obrigatorio, Mensagem)
    select 
        tag, 
        [value] as valor, 
        Mensagem 
    from cte where [type] not in(4,5)

    -- Realizando update na obrigatoriedade de alguns campos de acordo com o perfil informado.
    if exists(select * from Seguranca.UsuarioTipo with(nolock) where Id = (json_value(@put_data, '$.dadosGerais.tipoUsuarioId')) and DadosComerciais = 1)
    begin
        if exists(select * from Seguranca.UsuarioTipo with(nolock) where Id = (json_value(@put_data, '$.dadosGerais.tipoUsuarioId')) and Classificacao = 1)
        begin
            update @regrasValidacaoJson
                set Obrigatorio = 1
            where tag = 'dadosComerciais.classificacaoId'
        end
    end
    
    -- Inserindo os erros encontrados de acordo com as obrigatoriedades informadas no json de padronização ao topo da procedure
    -- Retirando da validação todos os atributos que são arrays, pois eles são validados de maneira diferente
    SELECT
        @errors = json_modify(@errors, 'append $', replace(a.Mensagem, '_', '.'))
    FROM @regrasValidacaoJson a
    where a.Obrigatorio = 1 and (JSON_VALUE(@put_data, '$.'+a.Tag) is null or trim(JSON_VALUE(@put_data, '$.'+a.Tag)) = '')
          and a.Tag <> 'dadosComerciais.unidadeId' and a.Tag <> 'certificacoes.certificacaoId' and a.Tag <> 'certificacoes.tipoCertificacaoId'
          and a.Tag <> 'certificacoes.validade' and a.Tag <> 'dadosGerais.perfilUsuarioId'
    
    -- Deletando lixos para poder validar se existe alguma informacao
    delete from @perfilUsuario where id is null or id = ''

    if not exists (select * from @perfilUsuario)
    begin
        set @errors = JSON_MODIFY(@errors, 'append $', 'O campo "dadosGerais.perfilUsuarioId" é obrigatório.')
    end

    -- Validações do objeto dadosComerciais
    if exists(select * from Seguranca.UsuarioTipo with(nolock) where Id = (json_value(@put_data, '$.dadosGerais.tipoUsuarioId')) and DadosComerciais = 1)
    begin
        if not exists (select * from @dadosComerciais)
        begin
            set @errors = JSON_MODIFY(@errors, 'append $', 'É obrigatório informar os dados de "dados comerciais".')
        end
        -- Como é um array, o tratamento precisa ser realizado de forma diferente
        -- if exists (select top 1 id from @unidades where id is null) or ((select top 1 id from @unidades) = '')
        -- begin
        --     set @errors = JSON_MODIFY(@errors, 'append $', 'O campo "dadosComerciais.unidadeId" é obrigatório.')
        -- end

        -- if exists(select * from Seguranca.UsuarioTipo with(nolock) where Id = (json_value(@put_data, '$.dadosGerais.tipoUsuarioId')) and UnidadesBackOffice = 0)
        --    and (select count(*) from @unidades) > 1
        -- begin
        --     set @errors = JSON_MODIFY(@errors, 'append $', 'O perfil não permite o cadastro de múltiplas unidades.')
        -- end
    end
    
    -- Os campos do objeto certificação só são obrigatórios se estiver algo preenchido, e como é um array, preciso realizar de maneira separada.
    if exists(select * from @certificacoes)
    begin
        if exists(select * from Seguranca.UsuarioTipo with(nolock) where Id = (json_value(@put_data, '$.dadosGerais.tipoUsuarioId')) and Certificacoes = 1)
        begin
            if (select top 1 certificacaoId from @certificacoes) is null or (select top 1 certificacaoId from @certificacoes) = ''
            begin
                set @errors = JSON_MODIFY(@errors, 'append $', 'O campo "certificacoes.certificacaoId" é obrigatório.')
            end

            if (select top 1 tipoCertificacaoId from @certificacoes) is null or (select top 1 tipoCertificacaoId from @certificacoes) = ''
            begin
                set @errors = JSON_MODIFY(@errors, 'append $', 'O campo "certificacoes.tipoCertificacaoId" é obrigatório.')
            end

            if (select top 1 validade from @certificacoes) is null or (select top 1 validade from @certificacoes) = ''
            begin
                set @errors = JSON_MODIFY(@errors, 'append $', 'O campo "certificacoes.validade" é obrigatório.')
            end
        end
    end
    
    return @errors

end