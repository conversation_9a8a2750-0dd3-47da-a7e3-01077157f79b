alter function [dbo].[fncCadastroCredenciadoCamposObrigatorios] (@put_data varchar(max)) 
returns varchar(max)
as 
begin

    declare @camposObrigatoriosGeraisJson varchar(max) = 
        '{
            "id": false,
            "statusCredenciado": false,
            "dadosGerais": {
                "ativo": true,
                "unicard": true,
                "analisePromotora": true,
                "tipoCredenciado": true,
                "subTipoCredenciado": true,
                "classificacaoCredenciado": true,
                "cpfCnpj": true,
                "nome": true
            },
            "endereco": {
                "cep": false,
                "logradouro": false,
                "numero": false,
                "ufId": true,
                "cidadeId": true,
                "bairro": false,
                "complemento": false
            },
            "contato": false,
            "visita": {
                "dataHora": true
            },
            "socios": false,
            "comercial": {
                "hierarquia": {
                    "id": false,
                    "dataVigenciaInicial": true,
                    "niveis": false
                    }
                },
            "dadosBancarios": {
                "chavePix" : true
            },
            "documentos": {
                "tipoDocumentoCredenciado": true
            },
            "regioes": false,
            "produtos": {
                "tipoModalidade": true
            },
            "dadosConsignado": {
                "convenioRiscoId": true,
                "diaVencimento": true,
                "percentualCalculoMargem": true,
				"averbadora": false,
                "codigoConvenio": false,
                "codigoConvenioReserva": false
            }
        }'
        
    declare
        @tipoCredenciado int,
        @tipoCredenciadoCorrespondente int,
        @tipoCredenciadoConvenio int, 
        @nivel int,
        @iteradorUnidade uniqueidentifier,
        @editado bit,
        @iterador varchar(36),
        @tag varchar(150),
        @mensagem varchar(255),
        @produtos varchar(max),
        @opcoes varchar(max),
        @errors varchar(max) = '[]',
        @convenioTipoConsignado smallint,
        @subTipoCredenciado smallint,
		@averbadoraEconsig int,
		@averbadora tinyint

    declare @regrasValidacaoJson as table (
        Tag varchar(250),
        Obrigatorio bit,
        Mensagem varchar(255)
    )

    declare @jsonEntrada as table (
        [tag] varchar(250),
        [key] varchar(250),
        [value] varchar(max)
    )

    declare @comparaJson as table (
        tagPrimaria varchar(100),
        tagSecundaria varchar(100),
        [key] varchar(250),
        [mensagem] varchar(500),
        [id] varchar(36)
    )

    declare @ArvoreCredenciadoUnidades as table (
            credenciadoId int,
            nivel int,
            unidadeId int,
            editado bit,
            dadosGerais nvarchar(max),
            endereco nvarchar(max),
            visita nvarchar(max),
            hierarquia nvarchar(max),
            dadosBancarios nvarchar(max),
            documentos nvarchar(max),
            regioes nvarchar(max),
            produtos nvarchar(max),
            dadosConsignado nvarchar(max),
            unidades nvarchar(max),
            unidadeGuid uniqueidentifier
        )

    select @tipoCredenciadoCorrespondente = id from Parametro.fncRetornaEnumTabela('tipoCredenciado') where nome = 'Correspondente'
    select @tipoCredenciadoConvenio = id from Parametro.fncRetornaEnumTabela('tipoCredenciado') where nome = 'Conveniada'
    select @convenioTipoConsignado = Id from ConvenioTipo with(nolock) where Nome = 'Consignado Privado'
	select @averbadoraEconsig = id from Averbadora with(nolock) where nome = 'E.Consig'
    
    -- Campos Obrigatórios
    ;with cteRegrasValidacaoJson as (
            select *, [key] as tag, concat('O campo [', [key], '] é obrigatório.') [Mensagem] 
            from openjson(@camposObrigatoriosGeraisJson)

            union all

            select b.*, concat(a.tag,'.',b.[key]) tag, concat('O campo [', b.[key], '] é obrigatório') [Mensagem] 
            from cteRegrasValidacaoJson a 
            cross APPLY openjson(a.[VALUE]) b
            where a.TYPE in (4,5)
        )
    insert into @regrasValidacaoJson
    select [tag], [value], mensagem from cteRegrasValidacaoJson where [type] not in (4,5)

    -- Hierarquia de Unidades
    ;with cteHierarquiaCredenciadoUnidade as (
      -- Credenciado (Matriz)
        select 
            id as credenciadoId,
            0 as nivel,
            null as unidadeId,
            cast(1 as bit) editado, -- Por default, editado sempre virá como 1 para matriz
            json_query(dadosGerais) dadosGerais,
            endereco,
            visita,
            hierarquia,
            dadosBancarios,
            documentos,
            regioes,
            produtos,
            dadosConsignado,
            unidades,
            (select [guid] from vwNovoGuid) as unidadeGuid
            from openjson(@put_data)
                with (
                    id int,
                    dadosGerais nvarchar(max) '$.dadosGerais' as json,
                    endereco nvarchar(max) '$.endereco' as json,
                    visita nvarchar(max) '$.visita' as json,
                    hierarquia nvarchar(max) '$.comercial' as json,
                    dadosBancarios nvarchar(max) '$.dadosBancarios' as json,
                    documentos nvarchar(max) '$.documentos' as json,
                    regioes nvarchar(max) '$.regioes' as json,
                    produtos nvarchar(max) '$.produtos' as json,
                    dadosConsignado nvarchar(max) '$.dadosConsignado' as json,
                    unidades nvarchar(max) '$.unidades' as json
                )

        union all

        -- Unidades
        select 
            null as credenciadoId,
            1 as nivel,
            unidadeId,
            editado,
            json_query(dadosGerais) dadosGerais,
            endereco,
            null as visita,
            hierarquia,
            null as dadosBancarios,
            null as documentos,
            regioes,
            produtos,
            null as dadosConsignado,
            unidades,
            (select [guid] from vwNovoGuid) as unidadeGuid
            from openjson(@put_data, '$.unidades')
                with (
                    unidadeId int,
                    editado bit,
                    dadosGerais nvarchar(max) '$.dadosGerais' as json,
                    endereco nvarchar(max) '$.endereco' as json,
                    regioes nvarchar(max) '$.regioes' as json,
                    produtos nvarchar(max) '$.produtos' as json,
                    hierarquia nvarchar(max) '$.comercial' as json,
                    unidades nvarchar(max) '$.unidades' as json
                )

        union all

      -- SubUnidades
        select 
            null as credenciadoId,
            (a.nivel + 1) as nivel,
            b.unidadeId,
            b.editado,
            json_query(b.dadosGerais) dadosGerais,
            b.endereco,
            null as visita,
            b.hierarquia,
            null as dadosBancarios,
            null as documentos,
            b.regioes,
            b.produtos,
            null as dadosConsignado,
            b.unidades,
            (select [guid] from vwNovoGuid) as unidadeGuid
        from cteHierarquiaCredenciadoUnidade a
            cross apply (
            select * from openjson(a.unidades)
                with (
                    unidadeId int,
                    editado bit,
                    dadosGerais nvarchar(max) '$.dadosGerais' as json,
                    endereco nvarchar(max) '$.endereco' as json,
                    regioes nvarchar(max) '$.regioes' as json,
                    produtos nvarchar(max) '$.produtos' as json,
                    hierarquia nvarchar(max) '$.comercial' as json,
                    unidades nvarchar(max) '$.unidades' as json
                )
            ) b
        where a.nivel > 0
    )

    insert into @ArvoreCredenciadoUnidades
    select * from cteHierarquiaCredenciadoUnidade

    -- Atribuições e validações prévias
    select @tipoCredenciado = json_value(dadosGerais, '$.tipoCredenciado') from @ArvoreCredenciadoUnidades where nivel = 0 
    select @subTipoCredenciado = json_value(dadosGerais, '$.subTipoCredenciado') from @ArvoreCredenciadoUnidades where nivel = 0 
	select @averbadora = averbadora from openjson(@put_data,'$.dadosConsignado') with(averbadora tinyint)

    if @tipoCredenciado not in (@tipoCredenciadoCorrespondente, @tipoCredenciadoConvenio)
    begin
        set @errors = json_modify(@errors, 'append $', 'Tipo de credenciado inválido em dadosGerais.tipoCredenciado')
        return @errors
    end

    if @tipoCredenciado = @tipoCredenciadoConvenio and exists (select 1 from @jsonEntrada where ([tag] like '%produtos%' or [tag] like '%unidades%') and [value] is not null)
    begin
        set @errors = json_modify(@errors, 'append $', 'OS campos [produtos] e [unidades] não são válidos para credenciados do tipo convenio')
        return @errors
    end 

    -- Validação dos objetos
    while exists (select 1 from @ArvoreCredenciadoUnidades)
    begin
        set @put_data = null

        select @iteradorUnidade = max(unidadeGuid) from @ArvoreCredenciadoUnidades where nivel = (select min(nivel) from @ArvoreCredenciadoUnidades)
        select @editado = editado, @nivel = nivel from @ArvoreCredenciadoUnidades where unidadeGuid = @iteradorUnidade

        if @iteradorUnidade is null break

        if @editado = 1 -- As validações só irão ocorrer para objetos como essa flag true
        begin

            set @put_data = (
                select 
                    credenciadoId as id,
                    nivel,
                    editado,
                    json_query(dadosGerais) dadosGerais,
                    json_query(endereco) endereco,
                    json_query(visita) visita,
                    json_query(hierarquia) comercial,
                    json_query(dadosBancarios) dadosBancarios,
                    json_query(documentos) documentos,
                    json_query(regioes) regioes,
                    json_query(produtos) produtos,
                    json_query(dadosConsignado) dadosConsignado
                from @ArvoreCredenciadoUnidades 
                where unidadeGuid = @iteradorUnidade
                for json path, without_array_wrapper, include_null_values
            )

            ;with cteJsonEntrada as (
                select *, [key] as tag from openjson(@put_data)
                union all 
                select b.*, concat(a.tag,'.',b.[key]) tag from cteJsonEntrada a 
                    cross APPLY openjson(a.[VALUE]) b
                    where a.TYPE in (4,5)
            )

            insert into @jsonEntrada
            select [tag], [key], [value] from cteJsonEntrada where [type] not in (4,5)
            or dbo.fncRemoveCaracteresNumericos(tag) = 'produtosprodutospropriedadeopcoes'

            -- PUT (CPF não virá) | Para unidades o campo deixa de ser obrigatório
            if exists (select 1 from @jsonEntrada where [tag] = 'id' and [value] is not null) or (@nivel > 0 and @tipoCredenciado = @tipoCredenciadoCorrespondente) -- SubUnidades do correspondente
                update @regrasValidacaoJson set Obrigatorio = 0 where [tag] = 'dadosGerais.cpfCnpj' 

            -- Esses campos não existem para as unidades ou credenciados do tipo Convenio
            if @nivel > 0 or @tipoCredenciado = @tipoCredenciadoConvenio
                update @regrasValidacaoJson set Obrigatorio = 0 where [tag] in ('dadosGerais.classificacaoCredenciado', 'dadosGerais.analisePromotora', 'dadosGerais.unicard')

            -- Consignado privado (IMPROVEMENT 5313)
            if @nivel > 0 or (@tipoCredenciado = @tipoCredenciadoConvenio and @subTipoCredenciado <> @convenioTipoConsignado) or @tipoCredenciado <> @tipoCredenciadoConvenio
                update @regrasValidacaoJson set Obrigatorio = 0 where [tag] in ('dadosConsignado.convenioRiscoId', 'dadosConsignado.diaVencimento', 'dadosConsignado.percentualCalculoMargem')

            if @nivel > 0 and @tipoCredenciado = @tipoCredenciadoConvenio and @subTipoCredenciado = @convenioTipoConsignado
               update @regrasValidacaoJson set Obrigatorio = 0 where [tag] in ('dadosGerais.nome', 'endereco.cidadeId', 'endereco.ufId')
            
            -- Campos que não existem para Unidades
            if @nivel > 0
                update @regrasValidacaoJson set Obrigatorio = 0 where [tag] in ('dadosGerais.tipoCredenciado', 'dadosGerais.subTipoCredenciado')

            -- Se o campo "visita" não for preenchido, a dataHora deixa de ser obrigatória
            if not exists (select 1 from @jsonEntrada where [tag] = 'visita.visita' and cast([value] as bit) = 1)
                update @regrasValidacaoJson set Obrigatorio = 0 where [tag] = 'visita.dataHora'

            -- Se o campo "tipoChave" não estiver preenchido, a chave do pix deixa de ser obrigatória
            if not exists (select 1 from @jsonEntrada where [tag] = 'dadosBancarios.tipoChavePix' and [value] is not null) or @nivel > 0
                update @regrasValidacaoJson set Obrigatorio = 0 where [tag] = 'dadosBancarios.chavePix'

            -- Se não tiver arquivo anexado, tipo de documento deixa de ser obrigatório 
            if not exists (select 1 from @jsonEntrada where [tag] like '%documentos%' and [value] is not null) or @nivel > 0
                update @regrasValidacaoJson set Obrigatorio = 0 where [tag] = 'documentos.tipoDocumentoCredenciado'

            -- Se não tiver produtos, tipoModalidade deixa de ser obrigatória
            if not exists (select 1 from @jsonEntrada where dbo.fncRemoveCaracteresNumericos([tag]) = 'produtosprodutosid')
                update @regrasValidacaoJson set Obrigatorio = 0 where [tag] = 'produtos.tipoModalidade'
            else
                update @regrasValidacaoJson set Obrigatorio = 1 where [tag] = 'produtos.tipoModalidade'

            -- Se não tiver niveis com posições, a data de vigencia deixa de ser obrigatória
            if not exists (select 1 from @jsonEntrada where [key] = 'posicaoId')
                update @regrasValidacaoJson set Obrigatorio = 0 where [tag] = 'comercial.hierarquia.dataVigenciaInicial'
            else
                update @regrasValidacaoJson set Obrigatorio = 1 where [tag] = 'comercial.hierarquia.dataVigenciaInicial'

            if @subTipoCredenciado = @convenioTipoConsignado and @tipoCredenciado = @tipoCredenciadoConvenio and exists (select 1 from @ArvoreCredenciadoUnidades where unidadeGuid = @iteradorUnidade and nivel = 0)
            begin
                update @regrasValidacaoJson set Obrigatorio = 1 where [tag] = 'dadosConsignado.averbadora'
            end
            else if @subTipoCredenciado = @convenioTipoConsignado and @tipoCredenciado = @tipoCredenciadoConvenio and exists (select 1 from @ArvoreCredenciadoUnidades where unidadeGuid = @iteradorUnidade and nivel > 0)
            begin
                --condição else necessaria para remover a obrigatoriedade da averbadora para as unidades filiais
                update @regrasValidacaoJson set Obrigatorio = 0 where [tag] = 'dadosConsignado.averbadora'
            end 

            -- validando dados convenios consignado
			if @subTipoCredenciado = @convenioTipoConsignado and @tipoCredenciado = @tipoCredenciadoConvenio and @averbadora = @averbadoraEconsig
			begin
				update @regrasValidacaoJson set Obrigatorio = 1 where [tag] = 'dadosConsignado.codigoConvenio'
				update @regrasValidacaoJson set Obrigatorio = 1 where [tag] = 'dadosConsignado.codigoConvenioReserva'
			end

            -- Cias elétricas e/ou Tabela Juros devem ser informadas nos produtos CDC e Energia
            if exists (
                select 1
                from Produto a with(nolock) 
                join @jsonEntrada b on a.Id = b.[value] 
                join @jsonEntrada c on replace(b.tag, '.id', '.propriedade.opcoes') = c.tag
                join CredenciadoProdutoPropriedadeTipo d with(nolock) on a.Id = d.ProdutoId and d.TipoCredenciado = @tipoCredenciadoConvenio -- Regra de negócio adicionada por conta do bug 20354 - improvement 18102
                where dbo.fncRemoveCaracteresNumericos(b.[tag]) = 'produtosprodutosid' and a.Nome in ('CDC', 'Energia', 'Financiamento Crefaz')
                and isnull(c.[value], '[]') = '[]'
                and d.visualizaPropriedade = 1
            )
                set @errors = json_modify(@errors, 'append $', 'Tabela de Juros ou Cias Elétricas não informadas em [produtos].')

            insert into @comparaJson
            select a.[tag], isnull(b.Tag, a.Tag), [key], a.Mensagem, (select * from vwNovoGuid) 
            from @regrasValidacaoJson a 
            left join @jsonEntrada b on dbo.fncRemoveCaracteresNumericos(a.tag) = dbo.fncRemoveCaracteresNumericos(b.tag)
            where a.Obrigatorio = 1
            and (b.[value] is null or trim(b.[value]) = '')

            while 1 = 1
            begin 
                select @iterador = max(id) from @comparaJson
                select @mensagem = concat(mensagem, ' em ', tagSecundaria) from @comparaJson where id = @iterador
                if @iterador is null break

                set @errors = json_modify(@errors, 'append $', @mensagem)
                    
                delete from @comparaJson where id = @iterador
            end

            delete from @jsonEntrada
        end

        delete from @ArvoreCredenciadoUnidades where unidadeGuid = @iteradorUnidade 
    end

    return @errors
end