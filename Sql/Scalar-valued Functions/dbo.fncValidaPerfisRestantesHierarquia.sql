ALTER FUNCTION [dbo].[fncValidaPerfisRestantesHierarquia] (@usuarioId int, @perfisId varchar(max)) 
RETURNS BIT AS
BEGIN 
    declare @return_data bit,
            @contaHierarquiaVigente int,
            @contaHierarquiaAtualizada int,
            @output_data int

    declare @hierarquiaPerfilVigente as table(
        hierarquiaId int,
        usuarioPerfilId int,
        HierarquiaPerfilId int
    )

    declare @hierarquiaUsuario as table (
        hierarquiaId int
    )

    declare @hirarquiaPerfilAtualizada as table (
        hierarquiaId int,
        usuarioPerfilId int,
        HierarquiaPerfilId int
    )

    --usuario de responsabilidade
    insert into @hierarquiaUsuario
    select distinct c.HierarquiaId
    from UsuarioHierarquiaNivelDetalhe a with(nolock)
    join HierarquiaNivelDetalhe b with(nolock) on a.HierarquiaNivelDetalheId = b.Id
    join HierarquiaNivel c with(nolock) on b.HierarquiaNivelId = c.Id
    where a.UsuarioId = @usuarioId and a.DataFim is null

    insert into @hierarquiaPerfilVigente
    select c.hierarquiaId, b.PerfilId, a.PerfilId 
    from Seguranca.PerfilUsuario a with(nolock) 
    join HierarquiaPerfil b with(nolock) on a.PerfilId = b.PerfilId
    join @hierarquiaUsuario c on b.HierarquiaId = c.hierarquiaId
    where a.UsuarioId = @usuarioId

    select @contaHierarquiaVigente = count(*) from @hierarquiaUsuario
    select @contaHierarquiaAtualizada = count(distinct a.hierarquiaId) from @hierarquiaPerfilVigente a join openjson(@perfisId) b on a.usuarioPerfilId = b.[value]

    if @contaHierarquiaVigente = @contaHierarquiaAtualizada
    begin
        return 1
    end

    else
    begin 
        return 0
    end
    return 0
END
