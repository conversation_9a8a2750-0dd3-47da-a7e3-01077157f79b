CREATE function [dbo].[fncFormataTelefone] (@Telefone VARCHAR(16), @pontuacao bit) returns varchar(18) as
begin
    declare @result varchar(16)
    
    set @result = replace(replace(replace(replace(replace(@Telefone,' ',''),'-',''),'(',''),')',''),'_','')

    if PATINDEX('%[^0-9]%', @result) <> 0
        RETURN null

    if @pontuacao = 0 
        return @result
    
    if len(@result) = 10
        return '('+substring(@result,1,2)+') '+substring(@result,3,4)+'-'+substring(@result,7,4)
    
    if len(@result) = 11
        return '('+substring(@result,1,2)+') '+substring(@result,3,5)+'-'+substring(@result,8,4)

    return null 

end

