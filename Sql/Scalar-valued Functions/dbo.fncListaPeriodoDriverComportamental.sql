ALTER FUNCTION dbo.fncListaPeriodoDriverComportamental (@id int)--(@cpf varchar(11), @criterio varchar(100))
returns VARCHAR(MAX)
as
BEGIN
    DECLARE @resultado VARCHAR(MAX) ='[]',
            @periodo INT

    DECLARE @clientesMesmaReferenciaUltimosDias as TABLE (
        telefone VARCHAR(11),
        cpf VARCHAR(11),
        periodo_30 bit,
        periodo_60 bit,
        periodo_90 bit
    )

        INSERT into @clientesMesmaReferenciaUltimosDias
        select distinct(c.Telefone) as telefone,
                e.CPF,
                case when a.Cadastro >= cast(DATEADD(day, -30, dbo.getdateBR()) as date) then 1 else 0 end,
                case when a.Cadastro >= cast(DATEADD(day, -60, dbo.getdateBR()) as date) then 1 else 0 end,
                1
        from Proposta a WITH(nolock)
        join PessoaFisica b WITH(nolock) on b.PessoaId = a.PessoaId
        join Referencia c WITH(nolock) on c.PessoaId = b.<PERSON><PERSON>oaId
        join Referencia d WITH(nolock) on d.telefone = c.telefone
        join PessoaFisica e WITH(nolock) on e.PessoaId <> d.PessoaId
        where a.Cadastro >= cast(DATEADD(day, -90, dbo.getdateBR()) as date)
        and a.id = @id

    if exists (select * from @clientesMesmaReferenciaUltimosDias where periodo_30 = 1)
        set @resultado = json_modify(@resultado,'append $',(
            select a.telefone,
            json_query((select distinct b.cpf as cpf from @clientesMesmaReferenciaUltimosDias b where b.telefone = a.telefone and periodo_30 = 1 for json PATH)) as listaCPF
            from @clientesMesmaReferenciaUltimosDias a
            where periodo_30 = 1
            FOR JSON PATH, root('clientesComMesmaPrimariaUltimo30Dias'))
        )

    if exists (select * from @clientesMesmaReferenciaUltimosDias where periodo_60 = 1)
        set @resultado = json_modify(@resultado,'append $',(
            select a.telefone,
            json_query((select distinct b.cpf as cpf from @clientesMesmaReferenciaUltimosDias b where b.telefone = a.telefone and periodo_60 = 1 for json PATH)) as listaCPF
            from @clientesMesmaReferenciaUltimosDias a
            where periodo_60 = 1
            FOR JSON PATH, root('clientesComMesmaReferenciaUltimo60Dias'))
        )

    if exists (select * from @clientesMesmaReferenciaUltimosDias where periodo_90 = 1)
        set @resultado = json_modify(@resultado,'append $',(
            select a.telefone,
            json_query((select distinct b.cpf as cpf from @clientesMesmaReferenciaUltimosDias b where b.telefone = a.telefone and periodo_90 = 1 for json PATH)) as listaCPF
            from @clientesMesmaReferenciaUltimosDias a
            where periodo_90 = 1
            FOR JSON PATH, root('clientesComMesmaReferenciaUltimo90Dias'))
        )


    DECLARE @telefoneFixoClientes as TABLE (
        telefoneFixo VARCHAR(11),
        cpf VARCHAR(11),
        periodo_30 bit,
        periodo_60 bit,
        periodo_90 bit
    )


        insert into @telefoneFixoClientes
        select distinct b.Telefone as telefoneFixo,
                        d.CPF,
                        case when a.Cadastro >= cast(DATEADD(day, -30, dbo.getdateBR()) as date) then 1 else 0 end,
                        case when a.Cadastro >= cast(DATEADD(day, -60, dbo.getdateBR()) as date) then 1 else 0 end,
                        1
        from Proposta a WITH(nolock)
        join PropostaTelefoneExtra b with(nolock) on b.propostaId = a.id
        join PropostaTelefoneExtra c with(nolock) on c.Telefone = b.Telefone
        join Proposta d with(nolock) on d.cpf <> a.cpf
        where a.Cadastro >= cast(DATEADD(day, -90, dbo.getdateBR()) as date)
        and a.id = @id and b.TipoTelefone = 2


    if exists (select * from @telefoneFixoClientes where periodo_30 = 1)
        set @resultado = json_modify(@resultado,'append $',(
            select a.telefoneFixo,
            json_query((select distinct b.cpf as cpf from @telefoneFixoClientes b where b.telefoneFixo = a.telefoneFixo and periodo_30 = 1 for json PATH)) as listaCPF
            from @telefoneFixoClientes a
            where periodo_30 = 1
            for json PATH, root('clientesComMesmoTelefoneFixoInformadoUltimo30Dias'))
        )

    if exists (select * from @telefoneFixoClientes where periodo_60 = 1)
        set @resultado = json_modify(@resultado,'append $',(
            select a.telefoneFixo,
            json_query((select distinct b.cpf as cpf from @telefoneFixoClientes b where b.telefoneFixo = a.telefoneFixo and periodo_60 = 1 for json PATH)) as listaCPF
            from @telefoneFixoClientes a
            where periodo_60 = 1
            for json PATH, root('clientesComMesmoTelefoneFixoInformadoUltimo60Dias'))
        )

    if exists (select * from @telefoneFixoClientes where periodo_90 = 1)
        set @resultado = json_modify(@resultado,'append $',(
            select a.telefoneFixo,
            json_query((select distinct b.cpf as cpf from @telefoneFixoClientes b where b.telefoneFixo = a.telefoneFixo and periodo_90 = 1 for json PATH)) as listaCPF
            from @telefoneFixoClientes a
            where periodo_90 = 1
            for json PATH, root('clientesComMesmoTelefoneFixoInformadoUltimo90Dias'))
        )



    DECLARE @telefoneCelularClientes as TABLE (
        telefoneCelular VARCHAR(11),
        cpf VARCHAR(11),
        periodo_30 bit,
        periodo_60 bit,
        periodo_90 bit
    )


    insert into @telefoneCelularClientes
    select distinct a.Telefone as telefoneCelular,
                    b.CPF,
                    case when a.Cadastro >= cast(DATEADD(day, -30, dbo.getdateBR()) as date) then 1 else 0 end,
                    case when a.Cadastro >= cast(DATEADD(day, -60, dbo.getdateBR()) as date) then 1 else 0 end,
                    1
    from Proposta a WITH(nolock)
    join Proposta b WITH(nolock) on b.Telefone = a.Telefone and a.id <> b.id
    where a.Cadastro >= cast(DATEADD(day, -90, dbo.getdateBR()) as date)
    and a.Telefone is not null and a.TipoTelefone = 1
    and a.id = @id



    if exists(select * from @telefoneCelularClientes where periodo_30 = 1)
        set @resultado = json_modify(@resultado,'append $',(
            select a.telefoneCelular,
            json_query((select distinct(b.cpf) as cpf from @telefoneCelularClientes b where b.telefoneCelular = a.telefoneCelular and periodo_30 = 1 for json PATH)) as listaCPF
            from @telefoneCelularClientes a
            where periodo_30 = 1
            for json PATH, root('clientesInformaramMesmoCelularInformadoUltimo30Dias'))
        )

    if exists(select * from @telefoneCelularClientes where periodo_60 = 1)
        set @resultado = json_modify(@resultado,'append $',(
            select a.telefoneCelular,
            json_query((select distinct(b.cpf) as cpf from @telefoneCelularClientes b where b.telefoneCelular = a.telefoneCelular and periodo_60 = 1 for json PATH)) as listaCPF
            from @telefoneCelularClientes a
            where periodo_60 = 1
            for json PATH, root('clientesInformaramMesmoCelularInformadoUltimo60Dias'))
        )

    if exists(select * from @telefoneCelularClientes where periodo_90 = 1)
        set @resultado = json_modify(@resultado,'append $',(
            select a.telefoneCelular,
            json_query((select distinct(b.cpf) as cpf from @telefoneCelularClientes b where b.telefoneCelular = a.telefoneCelular and periodo_90 = 1 for json PATH)) as listaCPF
            from @telefoneCelularClientes a
            where periodo_90 = 1
            for json PATH, root('clientesInformaramMesmoCelularPropostaUltimo90Dias'))
        )



    declare @ciaEletricas table (
        ciaEletrica varchar(100),
        valor numeric(15,2),
        periodo_90 bit,
        periodo_180 bit,
        periodo_270 bit
        )


        insert @ciaEletricas
        select  d.Nome as ciaEletrica,
                c.Valor as valor,
                case when a.Cadastro >= cast(DATEADD(day, -90, dbo.getdateBR()) as date) then 1 else 0 end,
                case when a.Cadastro >= cast(DATEADD(day, -180, dbo.getdateBR()) as date) then 1 else 0 end,
                1
        from Proposta a WITH(nolock)
        join PessoaFisica b WITH(nolock) on b.PessoaId = a.PessoaId
        join PessoaConvenioDadosAdicionais c WITH(nolock) on c.PessoaId = b.PessoaId and a.Id = c.PropostaId
        join ConvenioDados d WITH(nolock) on d.Id = c.ConvenioDadosId and d.Chave = 1
        join Convenio e WITH(nolock) on e.Id = d.ConvenioId
        join ConvenioTipo f WITH(nolock) on f.Id = e.ConvenioTipoId and f.Nome = 'Cia Elétrica'
        where a.Cadastro >= cast(DATEADD(day, -270, dbo.getdateBR()) as date)
        and d.dataExclusao is null
        and a.id = @id



    if exists (select * from @ciaEletricas where periodo_90 = 1)
        set @resultado =  (
            select ciaEletrica, valor
            from @ciaEletricas
            where periodo_90 = 1
            for json PATH, root('companhiasEletricasUtilizadasUltimos90dias')
        )

    if exists (select * from @ciaEletricas where periodo_180 = 1)
        set @resultado =  (
            select ciaEletrica, valor
            from @ciaEletricas
            where periodo_180 = 1
        for json PATH, root('companhiasEletricasUtilizadasUltimos180dias')
    )

    if exists (select * from @ciaEletricas where periodo_270 = 1)
        set @resultado =  (
            select ciaEletrica, valor
            from @ciaEletricas
            where periodo_270 = 1
        for json PATH, root('companhiasEletricasUtilizadasUltimos270dias')
    )



    DECLARE @telefonesCelularPropostacpf as table (
        telefoneCelular varchar(11),
        cpf varchar(11),
        periodo_90 bit,
        periodo_180 bit,
        periodo_270 bit
        )


        ;WITH telefonesCelularProposta3 (telefoneCelular) as (
            select distinct b.Telefone as telefoneCelular
            from Proposta a WITH(nolock)
            join Proposta b with(nolock) on b.CPF = a.CPF
            where a.Cadastro >= cast(DATEADD(day, -270, dbo.getdateBR()) as date)
            and a.Telefone is not null
            and a.id = @id
        )

        insert @telefonesCelularPropostacpf
        select  t.telefoneCelular,
                a.CPF,
                case when a.Cadastro >= cast(DATEADD(day, -90, dbo.getdateBR()) as date) then 1 else 0 end,
                case when a.Cadastro >= cast(DATEADD(day, -180, dbo.getdateBR()) as date) then 1 else 0 end,
                1
        from (
            select a.Telefone as telefoneCelular, count(*) as quantidadeCPF
            from Proposta a WITH(nolock)
            join telefonesCelularProposta3 b on a.Telefone = b.telefoneCelular
            where a.Cadastro >= cast(DATEADD(day, -270, dbo.getdateBR()) as date)
            and a.Telefone is not null
            and a.id <> @id
            group by a.Telefone
            HAVING count(*) >= 3
        ) t
        join Proposta a with(nolock) on a.Telefone = t.telefoneCelular
        where a.Cadastro >= cast(DATEADD(day, -270, dbo.getdateBR()) as date)
        and a.Telefone is not null
        and a.id <> @id



    if exists(select * from @telefonesCelularPropostacpf where periodo_90 = 1)
        set @resultado = json_modify(@resultado,'append $',(
            select a.telefoneCelular,
            json_query((select distinct b.cpf as cpf from @telefonesCelularPropostacpf b where b.telefoneCelular = a.telefoneCelular and periodo_90 = 1 for json PATH)) as listaCPF
            from @telefonesCelularPropostacpf a
            where periodo_90 = 1
            for json PATH, root('clientesMesmoCelularEmMaisDe3PropostasUltimos180Dias'))
        )

    if exists(select * from @telefonesCelularPropostacpf where periodo_180 = 1)
        set @resultado = json_modify(@resultado,'append $',(
            select a.telefoneCelular,
            json_query((select distinct b.cpf as cpf from @telefonesCelularPropostacpf b where b.telefoneCelular = a.telefoneCelular and periodo_180 = 1 for json PATH)) as listaCPF
            from @telefonesCelularPropostacpf a
            where periodo_180 = 1
            for json PATH, root('clientesMesmoCelularEmMaisDe3PropostasUltimos180Dias'))
        )

    if exists(select * from @telefonesCelularPropostacpf where periodo_270 = 1)
        set @resultado = json_modify(@resultado,'append $',(
            select a.telefoneCelular,
            json_query((select distinct b.cpf as cpf from @telefonesCelularPropostacpf b where b.telefoneCelular = a.telefoneCelular and periodo_270 = 1 for json PATH)) as listaCPF
            from @telefonesCelularPropostacpf a
            where periodo_270 = 1
            for json PATH, root('clientesMesmoCelularEmMaisDe3PropostasUltimos270Dias'))
        )


    DECLARE @listaCPFsmesmaUnidadeConsumidora as TABLE (
        id int,
        ciaEletrica VARCHAR(100),
        unidadeConsumidora VARCHAR(100),
        cpf VARCHAR(11),
        periodo_3 bit,
        periodo_6 bit,
        periodo_9 bit
    )


    insert into @listaCPFsmesmaUnidadeConsumidora
    select  d.Id,
            d.Nome,
            a.Valor,
            k.CPF,
            case when f.Cadastro >= cast(DATEADD(MONTH, -3, dbo.getdateBR()) as date) then 1 else 0 end,
            case when f.Cadastro >= cast(DATEADD(MONTH, -6, dbo.getdateBR()) as date) then 1 else 0 end,
            1
    from PessoaConvenioDadosAdicionais a WITH(nolock)
    join PessoaFisica b WITH(nolock) on b.PessoaId = a.PessoaId
    join Proposta f WITH(nolock) on a.PropostaId = f.Id
    join ConvenioDados c WITH(nolock) on c.Id = a.ConvenioDadosId and c.Chave = 1 and c.dataExclusao is null
    join Convenio d WITH(nolock) on d.Id = c.ConvenioId
    join ConvenioTipo e WITH(nolock) on e.Id = d.ConvenioTipoId and e.Nome = 'Cia Elétrica'

    join Convenio g with(nolock) on g.id = d.id
    join ConvenioTipo h with(nolock) on h.id = g.ConvenioTipoId and h.Nome = 'Cia Elétrica'
    join ConvenioDados i with(nolock) on i.ConvenioId = g.id and i.chave = 1
    join PessoaConvenioDadosAdicionais j WITH(nolock) on j.ConvenioDadosId = i.id
    join Proposta l with(nolock) on l.Id = j.PropostaId
    join PessoaFisica k with(nolock) on k.PessoaId = a.PessoaId and k.CPF <> b.CPF
    where f.Cadastro >= cast(DATEADD(MONTH, -9, dbo.getdateBR()) as date)
    AND f.id = @id


    if exists(select * from @listaCPFsmesmaUnidadeConsumidora where periodo_3 = 1)
        set @resultado = json_modify(@resultado,'append $',(
            select
                a.ciaEletrica,
                a.unidadeConsumidora,
                json_query((select distinct b.cpf as cpf from @listaCPFsmesmaUnidadeConsumidora b where b.id = a.id and periodo_3 = 1 for json path)) as listaCPF
            from @listaCPFsmesmaUnidadeConsumidora a
            where periodo_3 = 1
            for json path, root('listaCPFsComMesmaUnidadeConsumidoraUltimos3Meses'))
        )

    if exists(select * from @listaCPFsmesmaUnidadeConsumidora where periodo_6 = 1)
        set @resultado = json_modify(@resultado,'append $',(
            select
                a.ciaEletrica,
                a.unidadeConsumidora,
                json_query((select distinct b.cpf as cpf from @listaCPFsmesmaUnidadeConsumidora b where b.id = a.id and periodo_6 = 1 for json path)) as listaCPF
            from @listaCPFsmesmaUnidadeConsumidora a
            where periodo_6 = 1
            for json path, root('listaCPFsComMesmaUnidadeConsumidoraUltimos6Meses'))
        )

    if exists(select * from @listaCPFsmesmaUnidadeConsumidora where periodo_9 = 1)
        set @resultado = json_modify(@resultado,'append $',(
            select
                a.ciaEletrica,
                a.unidadeConsumidora,
                json_query((select distinct b.cpf as cpf from @listaCPFsmesmaUnidadeConsumidora b where b.id = a.id and periodo_9 = 1 for json path)) as listaCPF
            from @listaCPFsmesmaUnidadeConsumidora a
            where periodo_9 = 1
            for json path, root('listaCPFsComMesmaUnidadeConsumidoraUltimos9Meses'))
        )



    DECLARE @telefonesTelefonePropostaQuantidade as table (
        telefone varchar(11),
        quantidade int,
        periodo int
        )

    set @periodo = 9

    while @periodo > 0
    begin
        ;WITH telefonesTelefoneProposta (telefone) as (
            SELECT distinct(telefone) as telefone
            from (
                select a.Telefone as telefone
                from Proposta a WITH(nolock)
                where a.Cadastro >= cast(DATEADD(MONTH, -@periodo, dbo.getdateBR()) as date)
                and a.Telefone is not null
                and a.id = @id
                UNION all
                select b.Telefone as telefone
                from Proposta a WITH(nolock)
                join PropostaTelefoneExtra b with(nolock) on b.PropostaId = a.id
                where a.Cadastro >= cast(DATEADD(MONTH, -@periodo, dbo.getdateBR()) as date)
                and a.TelefoneFixo is not null
                and a.id = @id
                ) t
        )

        insert @telefonesTelefonePropostaQuantidade
        select t.telefone, sum(t.quantidade) as quantidade, @periodo periodo
        from (
            select a.Telefone as telefone, count(*) as quantidade
            from Proposta a WITH(nolock)
            join telefonesTelefoneProposta b on a.Telefone = b.telefone
            where a.Cadastro >= cast(DATEADD(MONTH, -@periodo, dbo.getdateBR()) as date)
            and a.Telefone is not null
            and a.id <> @id
            group by a.Telefone
            HAVING count(*) > 3
            UNION ALL
            select a.TelefoneFixo as telefone, count(*) as quantidade
            from Proposta a WITH(nolock)
            join PropostaTelefoneExtra b with(nolock) on b.PropostaId = a.id
            join telefonesTelefoneProposta c on c.Telefone = b.telefone
            where a.Cadastro >= cast(DATEADD(MONTH, -@periodo, dbo.getdateBR()) as date)
            and a.TelefoneFixo is not null
            and a.id <> @id
            group by a.TelefoneFixo
            HAVING count(*) > 3
        ) t
        group by t.telefone

        set @periodo -= 3
    end

    if exists (select * from @telefonesTelefonePropostaQuantidade where periodo = 3)
        set @resultado = json_modify(@resultado,'append $',(
            select a.telefone, a.quantidade
            from @telefonesTelefonePropostaQuantidade a
            where periodo = 3
            for json PATH, root('quantidadeVezesQueAlgumTelefoneFoiInformadoMais3PropostasUltimos3Meses'))
        )

    if exists (select * from @telefonesTelefonePropostaQuantidade where periodo = 6)
        set @resultado = json_modify(@resultado,'append $',(
            select a.telefone, a.quantidade
            from @telefonesTelefonePropostaQuantidade a
            where periodo = 6
            for json PATH, root('quantidadeVezesQueAlgumTelefoneFoiInformadoMais3PropostasUltimos6Meses'))
        )

    if exists (select * from @telefonesTelefonePropostaQuantidade where periodo = 9)
        set @resultado = json_modify(@resultado,'append $',(
            select a.telefone, a.quantidade
            from @telefonesTelefonePropostaQuantidade a
            where periodo = 9
            for json PATH, root('quantidadeVezesQueAlgumTelefoneFoiInformadoMais3PropostasUltimos9Meses'))
        )

    RETURN @resultado
END