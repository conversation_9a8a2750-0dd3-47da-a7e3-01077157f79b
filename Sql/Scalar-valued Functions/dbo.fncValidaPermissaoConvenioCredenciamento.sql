create function [dbo].[fncValidaPermissaoConvenioCredenciamento] (@usuarioId int,@convenioId int) returns bit as
begin
    --função serve para validar pelo credenciamento se o usuario tem pemissão de atuar com o convenio informado
    declare @unidadeId int,
            @correspondenteId int,
            @MatrizId int,
            @credenciadoId int,
            @convenioTipoId int,
            @tipoRelacionamentoProdutoCredenciado tinyint

    
    declare @ProdutosLiberados table(
        Id int,
        Valor int
    )

    select @tipoRelacionamentoProdutoCredenciado = id from Parametro.fncRetornaEnumTabela('tipoRelacionamentoProdutoCredenciado') where nome = 'Empresas restritas'
    select @convenioTipoId = id from ConvenioTipo with(nolock) where DescricaoTecnica = 'consignado_privado'
    select @unidadeId = UnidadeId,@correspondenteId = CorrespondenteId from Seguranca.Usuario with(nolock) where id = @usuarioId

    select @MatrizId = id from Unidade with(nolock) where CorrespondenteId = @correspondenteId and ativo = 1 and tipo = 0 ---matriz

    select @credenciadoId = id From Credenciado with(nolock) where CorrespondenteId = @correspondenteId

    if @tipoRelacionamentoProdutoCredenciado = 2--'Empresas restritas'
    begin
        insert into @ProdutosLiberados
        select a.ProdutoId,b.ValorId
        from CredenciadoProduto a with(nolock)
        left join CredenciadoProdutoPropriedade b with(nolock) on b.CredenciadoProdutoId = a.id
        left join CredenciadoProdutoPropriedadeTipo c with(nolock) on c.ProdutoId = b.ValorId and c.TipoRelacionamentoProdutoCredenciado = @tipoRelacionamentoProdutoCredenciado
        where CredenciadoId = @credenciadoId and UnidadeId = @unidadeId
        group by a.ProdutoId,b.ValorId

        if @@ROWCOUNT = 0
        begin
            insert into @ProdutosLiberados
            select a.ProdutoId, b.ValorId
            from CredenciadoProduto a with(nolock)
            left join CredenciadoProdutoPropriedade b with(nolock) on b.CredenciadoProdutoId = a.id
            left join CredenciadoProdutoPropriedadeTipo c with(nolock) on c.ProdutoId = b.ValorId and c.TipoRelacionamentoProdutoCredenciado = @tipoRelacionamentoProdutoCredenciado
            where (CredenciadoId = @credenciadoId and UnidadeId = @MatrizId) or (CredenciadoId = @credenciadoId and unidadeId is null)
            --where (CredenciadoId = 36 and UnidadeId = 9) or (CredenciadoId = 36 and unidadeId is null)
            group by a.ProdutoId,b.ValorId
        end
    
        if exists (select 1 from @ProdutosLiberados a where valor = @convenioId)
            return 1
        else if exists(select 1 from Convenio with(nolock) where id = @convenioId and restrito = 0 and ConvenioTipoId = @convenioTipoId)
            return 1
        else 
            return 0
    
    end

    return 0
end
