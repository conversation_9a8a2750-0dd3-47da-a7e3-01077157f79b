create function [dbo].[fncVerificaIntervalosAcompanhamento] (@usaCache tinyint,@naoUsaCache tinyint,@combinaCacheData tinyint,@QUANTIDADE_DIAS_CACHE_PROPOSTAS tinyint,@filtroDataInicial datetime,@filtroDataFinal datetime) 
returns tinyint as
begin
    declare @resposta tinyint
    
    if dateadd(day,-@QUANTIDADE_DIAS_CACHE_PROPOSTAS,dbo.getdateBR()) > @filtroDataFinal
        set @resposta = @naoUsaCache
    
    else if dateadd(day,-@QUANTIDADE_DIAS_CACHE_PROPOSTAS,dbo.getdateBR()) <= @filtroDataInicial
        set @resposta = @usaCache
    
    else
        set @resposta = @combinaCacheData

    return @resposta
end
