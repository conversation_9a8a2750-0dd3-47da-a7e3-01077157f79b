alter FUNCTION [dbo].[fncCalculaTempoEmprego](@dataInicial date,@dataFinal date) returns varchar(50) 
as
begin
    declare @vConsignetDtAdmissaoCrivo varchar(10), 
            @Anos int,
            @Meses int,
            @Dias int

    if @dataInicial is not null
    begin
        -- Calcula os anos
        SET @Anos = DATEDIFF(YEAR, @dataInicial, @dataFinal);
        IF DATEADD(YEAR, @Anos, @dataInicial) > @dataFinal
            SET @Anos = @Anos - 1;

        -- <PERSON>cula os meses restantes
        SET @Meses = DATEDIFF(MONTH, DATEADD(YEAR, @Anos, @dataInicial), @dataFinal);
        IF DATEADD(MONTH, @Meses, DATEADD(YEAR, @Anos, @dataInicial)) > @dataFinal
            SET @Meses = @Meses - 1;

        -- Calcula os dias restantes
        SET @Dias = DATEDIFF(DAY, DATEADD(MONTH, @Meses, DATEADD(YEAR, @Anos, @dataInicial)), @dataFinal);

        return CONCAT(@Anos, ' ano(s), ', @Meses, ' mês(es) e ', @Dias, ' dia(s)');
    end    

    return null
end