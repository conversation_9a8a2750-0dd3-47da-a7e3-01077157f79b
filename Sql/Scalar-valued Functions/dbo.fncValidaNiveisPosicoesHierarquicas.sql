alter function [dbo].[fncValidaNiveisPosicoesHierarquicas] (@put_data varchar(max)) returns varchar(max)
as  
begin  
    DECLARE     @hierarquiaNivelAux varchar(max),
                @key int = 0,
                @hierarquiaNivel nvarchar(max),
                @posicoes nvarchar(max),
                @id int,
                @errors varchar(max) = '[]'

    declare @hierarquiaNivelTabela table(
        id int,
        nome varchar(255),
        ordem int,
        ordemInsert int identity(1,1)
    )

    declare @arvoreHierarquia as table(
        hierarquiaId int,
        hierarquiaNivelId int,
        hierarquiaNivelDetalheid int,
        idTabela varchar(36),
        pai varchar(36),
        nivel int,
        id int,
        ativo bit,
        nome varchar(100),
        usuarioId int,
        vigenciaInicial datetime2(0),
        dataInativacao datetime2(0),
        usuarioAnterior nvarchar(max),
        realocacao bit
    )

    select
        @id = id,
        @hierarquiaNivel = hierarquiaNivel,
        @posicoes = posicoes
    from openjson(@put_data)
    with(
        id int,
        hierarquiaNivel nvarchar(max) as json,
        posicoes nvarchar(max) as json
    )

    -- Realiza o insert na arvore de hierarquia com todas as relações já definidas corretamente
    ;with cteArvoreHierarquia as (
        select 
            *, 
            1 as nivel, 
            cast(null as varchar(36)) pai, 
            cast((select [guid] from dbo.vwNovoGuid) as varchar(36)) idTabela 
        from openjson(@posicoes) 
        with( 
            id int, 
            ativo bit, 
            nome varchar(200), 
            usuarioId int, 
            vigenciaInicial date, 
            dataInativacao date, 
            usuarioAnterior nvarchar(max) as json, 
            posicoes nvarchar(max) as json
        )
        union all 
        select 
            b.* , 
            a.idTabela, 
            cast((select [guid] from dbo.vwNovoGuid) as varchar(36)) idTabela 
        from cteArvoreHierarquia a
        cross apply (
            select 
                *, 
                nivel +1 as nivel 
            from openjson(a.posicoes) 
            with( 
                id int, 
                ativo bit, 
                nome varchar(200), 
                usuarioId int, 
                vigenciaInicial date, 
                dataInativacao date, 
                usuarioAnterior nvarchar(max) as json, 
                posicoes nvarchar(max) as json
            )
        ) b
    )

    insert into @arvoreHierarquia (hierarquiaId, hierarquiaNivelId, hierarquiaNivelDetalheid, idTabela, pai, nivel, id, ativo, nome, usuarioId, vigenciaInicial, dataInativacao, usuarioAnterior, realocacao)
    select null, null, null, idTabela, pai, nivel, id, ativo, nome, usuarioId, vigenciaInicial, dataInativacao, usuarioAnterior, 0
        from cteArvoreHierarquia a 

    -- É realizado o insert da hierarquiaNivel enviada no json em uma tabela variavel, para poder fazer as tratativas dentro da tabela
    insert into @hierarquiaNivelTabela
    select * from openjson(@hierarquiaNivel) with(id int, nome varchar(255), ordem int)

    -- Valido se o Nivel de Hierarquia está sem ID mas a posição referente a este nivel está com ID preenchido
    if exists(select * from @hierarquiaNivelTabela a join @arvoreHierarquia b on a.ordem = b.nivel where a.id is null and b.id is not null)
        set @errors = JSON_MODIFY(@errors, 'append $', 'Nível de hierarquia informado incorreto.')

    -- Valido se o nivel de hierarquia informado está correto de acordo com o banco.
    if exists(select * from @hierarquiaNivelTabela a left join HierarquiaNivel b with(nolock) on b.Ordem = a.ordem and b.id = a.id where b.id is null and a.id is not null)
        set @errors = JSON_MODIFY(@errors, 'append $', 'Nível de hierarquia informado não existe.')

    -- Validando realocação de niveis (ou seja, se foi informado uma ordenação incorreta, por exemplo: 1, 3, 2)
    if exists(select * from @hierarquiaNivelTabela a join @hierarquiaNivelTabela b on a.ordemInsert = b.ordemInsert+1 where a.ordem <> b.ordem+1)
        set @errors = JSON_MODIFY(@errors, 'append $', 'Não é possível realocar a ordem dos niveis de hierarquia.')

    -- Valida se a quantidade de posições é superior que a quantidade de niveis informados.
    if (select max(nivel) from @arvoreHierarquia) > (select max(ordem) from @hierarquiaNivelTabela)
        set @errors = JSON_MODIFY(@errors, 'append $', 'Não é possível possuir mais posições do que niveis.')

    return @errors
end