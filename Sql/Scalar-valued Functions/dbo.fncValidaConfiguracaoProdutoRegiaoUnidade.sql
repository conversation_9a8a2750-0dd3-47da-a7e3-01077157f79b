alter function dbo.fncValidaConfiguracaoProdutoRegiaoUnidade (@put_data varchar(max)) returns varchar(max)
as 
begin
    declare @id UNIQUEIDENTIFIER,
        @produtoId int,
        @nivel int = 1,
        @errors varchar(max) = '[]',
        @matrizId int,
        @credenciadoId int,
        @nivelMax int,
        @nivelMin int,
        @matriz bit,
        @fatherId uniqueidentifier,
        @editado bit

    -- Tabela de apoio para a árvore de unidades
    declare @filo as table (
        seq int identity(1,1),  -- Sequencial da pilha, ela que diz em que ordem os registros entraram 
        fatherId uniqueidentifier, -- pai 
        id uniqueidentifier, -- filho
        done bit -- Se 1, então não existem filhos para serem processados abaixo dele
    )

    declare @regiao as table (
        id int,
        ufId int,
        ufNome char(2),
        cidadeId int,
        cidadeNome varchar(100),
        opera bit
    )

    declare @regiaoPai as table (
        ufId int, 
        cidadeId int, 
        opera bit
    )

    declare @regiaoHeranca as table (
        id int,
        ufId int, 
        cidadeId int, 
        opera bit
    )

    declare @regiaoOut as table (
        id int,
        ufId int,
        cidadeId int,
        opera bit
    )

    declare @produtos as table (
        id int, 
        modalidadeOpcoes tinyint
    )

    declare @produtosAux as table (
        id int, 
        modalidadeOpcoes tinyint
    )

    declare @configuracaoProdutoUnidade as table(
        unidadeId int,
        pai UNIQUEIDENTIFIER,
        id UNIQUEIDENTIFIER,
        nivel int,
        tipoModalidade tinyint,
        editado bit,
        regioes varchar(max),
        regioesHerdadas varchar(max),
        produtos varchar(max)
    )

    declare @configuracaoProdutoUnidadeAux as table(
        unidadeId int,
        pai UNIQUEIDENTIFIER,
        id UNIQUEIDENTIFIER,
        nivel int,
        tipoModalidade tinyint,
        editado bit,
        regioes varchar(max),
        regioesHerdadas varchar(max),
        produtos varchar(max)
    )

    select @credenciadoId = json_value(@put_data, '$.id')
    
    select 
        @matrizId = c.id
    from Credenciado a with(nolock)
    join Correspondente b with(nolock) on a.correspondenteId = b.id
    join Unidade c with(nolock) on b.id = c.correspondenteId and c.tipo = 0
    where a.id = @credenciadoId 

    ;with cteArvoreProdutos as (

        -- Matriz
        select
            1 as matriz, 
            @matrizId as unidadeId,
            cast(1 as bit) as editado,
            unidades,
            produtos,
            regioes,
            0 as nivel, 
            cast(null as uniqueidentifier) as pai, 
            (select [guid] from dbo.vwNovoGuid) id 
        from openjson(@put_data) 
        with( 
            unidades nvarchar(max) as json, 
            produtos nvarchar(max) as json,
            regioes nvarchar(max) as json
        )

        union all 

        -- Unidades e SubUnidades
        select
            0 as matriz, 
            b.unidadeId,
            b.editado,
            b.unidades,
            b.produtos,
            b.regioes,
            b.nivel, 
            a.id as pai,
            (select [guid] from dbo.vwNovoGuid) id 
        from cteArvoreProdutos a 
        cross apply (
            select 
                unidadeId,
                editado,
                unidades,
                iif(editado = 0, (select dbo.fncCredenciadoListaProdutos(@credenciadoId, unidadeId)), produtos) produtos,
                iif(editado = 0, (select dbo.fncCredenciadoListaRegioes(@credenciadoId, unidadeId)), regioes) regioes,
                nivel + 1 as nivel 
            from openjson(a.unidades) 
            with( 
                unidadeId int, 
                editado bit, 
                unidades nvarchar(max) as json, 
                produtos nvarchar(max) as json,
                regioes nvarchar(max) as json
            )) b
        
    )

    insert into @configuracaoProdutoUnidade (unidadeId, pai, id, nivel, editado, regioes,regioesHerdadas, produtos,tipomodalidade)
    select 
        a.unidadeId, a.pai, a.id, a.nivel, a.editado, a.regioes, null, a.produtos,c.tipomodalidade
    from cteArvoreProdutos a 
    outer apply (select * from openjson(a.produtos) with( tipoModalidade tinyint)) c

    select @nivelMax = max(nivel) from @configuracaoProdutoUnidade

    -- Realiza o merge das regiões dos pais com as dos filhos
    while @nivel <= @nivelMax
    begin 
        update a
            set regioesHerdadas = api.fncArrayMerge(a.regioes, iif(b.nivel <> 0 ,b.regioesHerdadas, b.regioes)) 
        from @configuracaoProdutoUnidade a
        join @configuracaoProdutoUnidade b on a.pai = b.id
        where a.nivel = @nivel

        set @nivel += 1
    end

    insert into @configuracaoProdutoUnidadeAux
    select * from @configuracaoProdutoUnidade

    -- Verificar se está sendo passado produto invalido
    if exists (
        select 1
        from @configuracaoProdutoUnidade a
        outer apply (
            select *
            from openjson(a.produtos,'$.produtos')
            with(
                id int,
                modalidadeOpcoes tinyint
            )
        ) b
        where b.id is null and a.editado = 1
        and json_query(a.produtos, '$.produtos') <> '[]'
    )
    begin
        select @errors = api.fncErrorMerge(@errors,'["Existem produtos sem o id informado."]')
        return @errors
    end

    while 1 = 1
    begin

        select top 1 
            @Id = a.id,
            @fatherId = a.pai, 
            @editado = a.editado
        from @configuracaoProdutoUnidade a 
        left join @filo b on a.id = b.id 
        where 1 = 1
        and (b.done is null or b.done = 0)
        and (@fatherId is null or @fatherId = pai) 
        order by a.nivel
        if @@rowcount = 0 break

        if not exists (select * from @filo where id = @Id)
        begin

            -- Parametrização do filho
            insert @regiao
            select b.*
            from @configuracaoProdutoUnidade a
            outer apply (
                select * 
                from openjson(a.regioes)
                with(
                    id int,
                    ufId int,
                    ufNome char(2),
                    cidadeId int,
                    cidadeNome varchar(100),
                    opera bit
                ) 
            ) b
            where a.id = @id

            insert @regiaoHeranca
            select b.*
            from @configuracaoProdutoUnidade a
            outer apply (
                select * 
                from openjson(a.regioesHerdadas)
                with(
                    id int,
                    ufId int,
                    cidadeId int,
                    opera bit
                ) 
            ) b
            where a.id = @id

            insert @produtos
            select b.*
            from @configuracaoProdutoUnidade a
            cross apply (
                select *
                from openjson(a.produtos,'$.produtos')
                with(
                    id int,
                    modalidadeOpcoes tinyint
                )
            ) b
            where a.id = @id

            -- Parametrização do pai
            insert @regiaoPai
            select b.* 
            from @configuracaoProdutoUnidade a
            outer apply (
                select * from openjson(iif(@fatherId is not null, regioesHerdadas, regioes))
                with (
                    ufId int,
                    cidadeId int,
                    opera bit
                )
                where opera = 0
            ) b
            where a.id = @fatherId and a.pai is not null

            if @fatherId is not null -- Validação acontece apenas para Unidades e SubUnidades
            begin

                -- Valida se existe alguma SubUnidade bloqueando regiões já bloqueadas pelo pai
                if exists (
                    select * 
                    from @regiao a 
                    join @regiaoPai b on 1 = 1
                    where
                    (
                        (a.ufId = b.ufId)
                        or 
                        (b.ufId is null and a.ufId is not null) -- Caso seja removido o bloqueio de região do pai e mantido para o filho.
                    )
                    and (
                        (b.cidadeId = a.cidadeId) 
                        or 
                        (b.cidadeId is null)        
                        or 
                        (a.cidadeId is null)
                    )
                )
                    select @errors = api.fncErrorMerge(@errors,'["Região bloqueada é inválida para a subUnidade, verifique se já não foi travada pelo pai."]')


                -- Valida se o produto da unidade filho tem produtos que não tem na unidade pai 
                if exists (
                    select 1 
                    from @produtos a
                    left join (
                        select c.id as produtoId
                            from @configuracaoProdutoUnidade a  -- Filho
                            join @configuracaoProdutoUnidade b on a.pai = b.id -- Pai
                                cross apply (
                                    select 
                                        *
                                    from openjson(b.produtos,'$.produtos')
                                    with(
                                        id int,
                                        modalidadeOpcoes tinyint
                                    )
                                ) c
                        where a.id = @id
                    ) z on a.id = z.produtoId
                    where z.produtoId is null
                )
                    select @errors = api.fncErrorMerge(@errors,'["Existem produtos da unidade filha não estão na unidade pai."]')

            end

            insert into @regiaoOut
            select * from @regiaoHeranca where opera = 0

            -- Deleta as regiões que não irão operar para a Unidade
            delete a from @regiaoHeranca a join @regiaoOut b on isnull(b.id,-1) = isnull(a.id,-1) and isnull(b.ufId,-1) = isnull(a.ufId,-1) and isnull(b.cidadeId,-1) = isnull(a.cidadeId,-1)

            insert into @produtosAux 
            select * from @produtos

            -- Valida se o produto é permitido para a região
            while exists (select * from @produtosAux)
            begin
                select top 1 @produtoId = id from @produtosAux

                if not exists (
                    select 1 
                    from @produtos a
                    join ProdutoRegiao b with(nolock) on b.ProdutoId = a.id
                    join @regiaoHeranca c on 
                        (c.UfId = b.UfId and c.CidadeId is null) -- A UF inteira
                        or
                        (c.UfId = b.UfId and c.CidadeId = b.CidadeId) -- apenas a Cidade
                    where a.id = @produtoId            

                    union  
                    
                    select 1
                    from @produtos a 
                    join ProdutoRegiao b with(nolock) on b.ProdutoId = a.id
                    join UF c with(nolock) on b.RegiaoId = c.RegiaoId -- Regiao
                    where a.id = @produtoId            
                )
                begin
                    select @errors = api.fncErrorMerge(@errors,'["Unidade informada não pode atuar com o produto."]')
                end

                delete from @produtosAux where id = @produtoId
            end

            --Repetição de produto para uma mesma unidade
            if exists(select count(id), id from @produtos group by id having count(id) > 1)
                select @errors = api.fncErrorMerge(@errors,'["Produtos repetidos para a mesma unidade."]')

            -- Valida relação das modalidades filho-pai
            if exists (
                select 1 from @configuracaoProdutoUnidade a 
                join @configuracaoProdutoUnidade b on a.pai = b.id
                where a.id = @id
                and a.tipoModalidade <> b.tipoModalidade and b.tipoModalidade <> 0
            )
                select @errors = api.fncErrorMerge(@errors,'["Modalidade da unidade filha difere da unidade pai."]')


        end

        -- Fim do bloco de regras de negócio
        insert into @filo values (@fatherId, @Id, 0)

        -- Caso o registro possua filhos para serem processados, ele se torna pai e inicaremos um novo ciclo na recursividade
        if exists (select * from @configuracaoProdutoUnidade a left join @filo b on a.id = b.id where pai = @Id and (b.done is null or b.done = 0))
        begin
            set @fatherId = @Id
        end 
        else 
        begin
            -- Senão, marca o item como processado, assim não será mais acessado e nem seus filhos
            update @filo set done = 1 where id = @Id
            -- Pega na pilha o ultimo que ainda não foi processado
            select top 1 @fatherId = fatherId from @filo where done = 0 order by seq desc
            if @@rowcount = 0
            begin
                -- Caso não tenha encontrado alguém na pilha que ainda não foi processado, indica que chegou no fim da fila
                -- Por isso, devemos reiniciar a variável de pai, pois não existe pai na pilha
                set @fatherId = null
            end 
        end 

        -- Limpa as tabelas
        delete from @regiao
        delete from @regiaoHeranca
        delete from @regiaoPai
        delete from @produtos

    end

    return @errors
end