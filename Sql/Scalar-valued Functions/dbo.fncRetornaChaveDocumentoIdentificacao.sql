alter function [dbo].[fncRetornaChaveDocumentoIdentificacao] (@PropostaId int)
returns varchar(255)
as
begin
    declare 
      @ChaveAcertPix varchar(255), 
      @GuidAcionamento varchar(255)

  -- Recupera os IDs das integrações
  declare @integracao as table(
		id int
	)

  -- Recupera os IDs das integrações
	insert into @integracao
  select Id
  from externo.integracao with(nolock)
  WHERE Nome in ('WebhookDocumentoAnaliseOCR','ConsultarDocumentoAnaliseOCR')

  select top 1
    @GuidAcionamento = a.ChaveAcertpix
  from PropostaImagem a with(nolock)
  join Documento b with(nolock) on a.DocumentoId = b.Id
  where b.Nome = 'DOCUMENTO DE IDENTIFICAÇÃO' 
  and a.PropostaId = @PropostaId
  order by a.Id desc

  -- Busca a ChaveAcertPix na tabela PropostaImagem com os filtros aplicados
	select top 1
		@ChaveAcertPix = a.Parametro
	from externo.Consulta a with(nolock)
	join @integracao b on b.id = a.IntegracaoId
	where a.PropostaId = @propostaid
  and IsJson(a.Resposta) = 1
	and coalesce(
            JSON_VALUE(a.Resposta, '$.Analise.Status'),
            JSON_VALUE(a.Resposta, '$.analise.status')
          ) = 10
  and a.Parametro = @GuidAcionamento
	order by a.Id desc;

  return @ChaveAcertPix;
end