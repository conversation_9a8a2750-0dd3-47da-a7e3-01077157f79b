alter function [dbo].[fncHierarquiaValidaEdicaoCredenciado] (@put_data varchar(max), @unidadeId int) 
returns varchar(max)
as 
begin
    
    declare @id int,
            @dataVigenciaInicial date,
            @posicaoAtualUnidade int,
            @errors varchar(max) = '[]',
            @dataVigenciaAtual date

    declare @posicaoAnterior table (id int, dataVigenciaFinal date)
    declare @niveis table (id int, posicaoId int, ordem tinyint)

    -- Resgatando a posição e vigencia atual pela unidadeId
    select 
        @dataVigenciaAtual = dataInicio,
        @posicaoAtualUnidade = HierarquiaNivelDetalheId
    from CorrespondenteHierarquiaNivelDetalhe with(nolock) where UnidadeId = @unidadeId and DataFim is null

    select
        @id = id,
        @dataVigenciaInicial = dataVigenciaInicial
    from openjson(@put_data)
    with(
        id int,
        dataVigenciaInicial date
    )

    insert into @posicaoAnterior
        select
            id,
            dataVigenciaFinal
        from openjson(@put_data, '$.posicaoAnterior')
        with(
            id int,
            dataVigenciaFinal date
        )

    insert into @niveis (id, posicaoId)
        select
            id,
            posicaoId
        from openjson(@put_data, '$.niveis')
        with(
            id int,
            posicaoId int
        )

    update a
        set a.ordem = b.Ordem
    from @niveis a
    join HierarquiaNivel b with(nolock) on a.id = b.Id

    -- Validações caso exista posicaoAnterior e unidadeId
    if exists(select * from @posicaoAnterior) and @unidadeId is not null
    begin
        -- Verifica se a posição informada no objeto de posicaoAnterior é a mesma posição de acordo com a unidade informada.
        if @posicaoAtualUnidade <> (select id from @posicaoAnterior)
            set @errors = JSON_MODIFY(@errors, 'append $', concat('Posição informada para unidade ', @unidadeId, ' não é a posição vigente no sistema'))
        
        -- Verifica se a posição informada no objeto de posicaoAnterior é a que está ativa.
        if not exists(select * from CorrespondenteHierarquiaNivelDetalhe with(nolock) where HierarquiaNivelDetalheId = (select id from @posicaoAnterior) and DataFim is null)
            set @errors = JSON_MODIFY(@errors, 'append $', 'Posição informada não é a posição vigente no sistema.')
        
        -- Verifica se dataVigenciaFinal é menor que a dataVigenciaInicial (validação de sobreposição)
        if exists (select 1 from CorrespondenteHierarquiaNivelDetalhe a with(nolock) join @posicaoAnterior b on 1 = 1 where a.UnidadeId = @unidadeId and a.DataFim is not null
                    and (
                        (b.dataVigenciaFinal between a.DataInicio and a.DataFim)
                        or
                        (b.dataVigenciaFinal > a.DataInicio and @dataVigenciaAtual < DataFim)
                    ) 
        )
            set @errors = JSON_MODIFY(@errors, 'append $', 'Não é permitido sobreposição de vigências. Verifique as datas e tente novamente!')
        
        -- Verifica se a dataVigenciaFinal é futura
        if (select dataVigenciaFinal from @posicaoAnterior) > dbo.getdateBR()
            set @errors = JSON_MODIFY(@errors, 'append $', 'Data final de vigência não pode ser uma data futura.')
        
        -- Verifica se a dataVigenciaFinal é menor do que a data que já existe no banco.
        if (select dataVigenciaFinal from @posicaoAnterior) < (select DataInicio from CorrespondenteHierarquiaNivelDetalhe a with(nolock) join @posicaoAnterior b on a.HierarquiaNivelDetalheId = b.id where a.UnidadeId = @unidadeId and a.DataFim is null)
            set @errors = JSON_MODIFY(@errors, 'append $', 'Data final de vigência precisa ser maior que a data de início da vigência para o antigo responsável dessa posição.')
        
    end

    if exists (select 1 from @niveis)
    begin

        -- Valida se existe a hierarquia informada
        if not exists(select * from Hierarquia a with(nolock) where a.Id = @id)
            set @errors = JSON_MODIFY(@errors, 'append $', 'Hierarquia informada não existe.')

        -- Caso os IDs dos ultimos niveis sejam diferentes, ou a posição em si sejam diferentes, retornado erro.
        -- Ex: Ultimo id informado é 3, e o ultimo id no banco é o 3, porém a posição do que foi informado é posição 3, e no banco este mesmo id está na posição 5, será retornado erro.
        if (select top 1 Id from HierarquiaNivel with(nolock) where HierarquiaId = @id and Ativo = 1 order by Ordem desc) <> (select top 1 id from @niveis order by ordem desc)
            or (select max(Ordem) from HierarquiaNivel with(nolock) where HierarquiaId = @id and Ativo = 1) <> (select max(ordem) from @niveis)
                set @errors = JSON_MODIFY(@errors, 'append $', 'O último nivel de hierarquia informada não é o mesmo último nível cadastrado no sistema para esta hierarquia.')

        -- Valida se dataVigenciaInicial está preenchido
        if @dataVigenciaInicial is null
            set @errors = JSON_MODIFY(@errors, 'append $', 'Data inicial de vigência precisa estar preenchida.')

        -- Valida se dataVigenciaInicial não é uma data futura
        if @dataVigenciaInicial > dbo.getdateBR()
            set @errors = JSON_MODIFY(@errors, 'append $', 'Data inicial de vigência não pode ser uma data futura.')

        if exists (select 1 from CorrespondenteHierarquiaNivelDetalhe a with(nolock) where a.UnidadeId = @unidadeId and @dataVigenciaInicial between a.DataInicio and a.DataFim)
            set @errors = json_modify(@errors, 'append $', 'Já existia uma vigência para a unidade na data inicial informada.')
    end

    -- Validações caso não existam posicaoAnterior e unidadeId preenchida
    if not exists(select * from @posicaoAnterior) and @unidadeId is not null
    begin
        if (select top 1 id from @niveis order by ordem desc) <> (select top 1 Id from HierarquiaNivel with(nolock) where HierarquiaId = @id and Ativo = 1 order by Ordem desc)
            set @errors = JSON_MODIFY(@errors, 'append $', 'Troca indevida de posição.')
    end

    return @errors
end